<template>
  <div class="flex flex-col items-center mt-24 bg-white">
    <!-- 标题 -->
    <div class="w-full title">
      <span class="tit_span">{{ title }}</span>
    </div>
    <div v-if="showTotal" class="f-y-center w-full px-20">
      <img src="@/assets/image/fz.png" class="w-36 h-36 mr-8" />
      <div class="text-[#2A72FF] text-40 font-600">{{ total }}</div>
      <div class="text-[#525665] text-28 ml-4">家</div>
    </div>
    <!-- 热力图 -->
    <div ref="mapChartRef" :style="{ width: '90%', height: '216px' }" class="map-container" />
    <div class="h-24"></div>
  </div>
</template>

<script setup>
import axios from 'axios'
import { provinces } from './maps'
import * as echarts from 'echarts'
import { useThrottleFn } from '@vueuse/core'
// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  title: {
    type: String,
    default: '供应链企业分布',
  },
  current: {
    type: Object,
    default: () => ({}),
  },
  showTotal: {
    type: Boolean,
    default: true,
  }
})

// Refs
const mapChartRef = ref(null)
const mapChart = ref(null)
const mapJson = ref(null)
const total = ref(0)

watch(
  () => props.current,
  val => {
    if (mapChart.value) {
      mapChart.value.dispatchAction({ type: 'downplay' }) // 清楚高亮状态
      if (Object.keys(val).length) {
        const fullName = provinces.find(item => item.name === val.name).fullName
        mapChart.value.dispatchAction({
          type: 'highlight',
          name: fullName,
        })
      }
    }
  },
  { deep: true }
)

// Methods

const getGeo = (options = {}) => {
  return {
    type: 'map',
    map: 'china',
    zoom: 1.2,
    itemStyle: {
      show: false,
      areaColor: '#fff',
      borderColor: '#2A72FF',
      borderWidth: 0.5,
    },
    emphasis: {
      itemStyle: {
        areaColor: '#95b9ff',
        color: 'transparent',
      },
      label: {
        show: false,
        textStyle: {
          color: 'transparent',
        },
      },
    },
    ...options,
  }
}

const getVisualMap = (data, options) => {
  const max = data[0]?.value || 0
  const min = data[data.length - 1]?.value || 0
  return {
    show: true,
    left: '0',
    bottom: '2%',
    max,
    min,
    z: 999,
    calculable: true,
    itemHeight: 60,
    inRange: {
      color: ['#f2f7ff', '#e5eeff', '#b6cfff', '#95b9ff', '#558fff', '#4081ff'],
      // color: ['#ECF8FF', '#388BE9'],
    },
    textStyle: {
      color: '#2C2C2C',
    },
    seriesIndex: 0,
    ...options,
  }
}

const registerMap = (name, map) => {
  echarts.registerMap(name, map)
}

const initMap = (options = {}) => {
  if (mapChart.value) {
    mapChart.value.dispose()
  }
  mapChart.value = echarts.init(mapChartRef.value)

  const option = {
    tooltip: {
      show: true,
      formatter: params => {
        return `${params.name} ${isNaN(params.value) ? 0 : params.value}`
      },
    },
    title: {
      text: '',
      right: 30,
      top: 30,
      textStyle: {
        fontSize: 14,
        fontWeight: 400,
        color: '#282D30',
      },
    },
    geo: [getGeo()],
    visualMap: [],
    series: [],
  }

  if (props.data.length) {
    const filters = ['香港', '澳门', '台湾']
    const data = props.data.filter(item => !filters.some(item1 => item.name.includes(item1)))
    total.value = toRaw(data).reduce((acc, cur) => acc + cur.value, 0)

    const seriesIndex = option.series.length
    option.series[seriesIndex] = {
      name: '',
      type: 'map',
      data,
      geoIndex: 0,
      selectedMode: true,
    }
    option.visualMap.push(getVisualMap(data, { seriesIndex }))
  }
  // 使用 vueuse 的 useThrottleFn
  const throttledResize = useThrottleFn(() => {
    mapChart.value.resize()
  }, 200)

  renderChart(option)
}
const getMapData = () => {
  const { VITE_APP_ENV } = import.meta.env;
  const prot = VITE_APP_ENV === 'zg' ? '' : 'https:/';
  const prefix =
  VITE_APP_ENV === 'development' ? 'https://feifeife.oss-cn-beijing.aliyuncs.com' : `${prot}/feifeife.oss-cn-beijing.aliyuncs.com`;
  axios.get(`${prefix}/opensource/map_json_1.0/json/1000000.json`).then(res => {
    mapJson.value = res.data
    registerMap('china', mapJson.value)
    initMap()
  })
}

const renderChart = option => {
  mapChart.value && mapChart.value.setOption(option)
}

nextTick(() => {
  getMapData()
})

// Watchers
watch(
  () => props.data,
  val => {
    if (val.length) {
      nextTick(() => {
        initMap()
      })
    }
  },
  { deep: true, immediate: true }
)
</script>

<style lang="scss" scoped>
.title {
  @apply h-88 px-24 flex items-center;

  .tit_span {
    @apply pl-12 font-600 relative text-#20263A text-28;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 28px;
      background: linear-gradient(180deg, rgba(42, 114, 255, 0.99) 0%, #3994ff 74%);
    }
  }
}
</style>
