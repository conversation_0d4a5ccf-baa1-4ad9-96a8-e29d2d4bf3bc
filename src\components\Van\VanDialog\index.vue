<template>
  <van-dialog
    v-model:show="show"
    className="custom-dialog"
    v-bind="$attrs"
    :showConfirmButton="showConfirmButton"
    :closeOnClickOverlay="closeOnClickOverlay"
  >
    <template #title v-if="showTitle">
      <div class="title">
        {{ $attrs.title }}
        <svg-icon class="close" name="close" @click="show = false" />
      </div>
    </template>
    <div class="dialog-body">
      <slot></slot>
    </div>
  </van-dialog>
</template>

<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { defineEmits } from 'vue'

const emits = defineEmits(['update:visible'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  showConfirmButton: {
    type: Boolean,
    default: false,
  },
  // close-on-click-overlay
  closeOnClickOverlay: {
    type: Boolean,
    default: true,
  },
  showTitle: {
    type: Boolean,
    default: true,
  },
})

const show = useVModel(props, 'visible')
</script>

<style lang="scss">
.custom-dialog {
  .van-dialog__header {
    padding: 26px 0 !important;
    border-bottom: 2px solid #eee;
  }

  .title {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: bold;
    color: #20263a;
  }

  .close {
    position: absolute;
    right: 32px;
    width: 32px;
    height: 32px;
  }
}

.dialog-body {
  padding: 24px 32px;
  min-height: 400px;
  max-height: 800px;
  overflow-y: auto;
}
</style>
