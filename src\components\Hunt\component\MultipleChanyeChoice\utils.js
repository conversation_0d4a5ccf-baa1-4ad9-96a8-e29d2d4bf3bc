import req from '@/api/index'
const constant = {
  EleseicAry: 'EleseicAry', //行业
  AllCertAry: 'AllCertAry', //企业许可
  EnttypeAry: 'EnttypeAry', //企业类型
  DistrictAry: 'DistrictAry', //地区
  MDistrictAry: 'MDistrictAry', //地区
}
// 扁平化数组 转 树形结构
const arrayToTree = arr => {
  const map = arr.reduce((acc, item) => {
    acc[item.code] = { ...item, children: [], level: (item.level - 1).toString() }
    return acc
  }, {})

  arr.forEach(item => {
    if (item.parent && map[item.parent]) {
      map[item.parent].children.push(map[item.code])
    }
  })

  return Object.values(map).filter(item => !item.parent)
}

/**
 *获取请求数据
 * @param {constant} str
 */
const getCommonReq = async function (str) {
  try {
    const commonApis = {
      [constant.EleseicAry]: req.eleseicPlus,
      [constant.AllCertAry]: req.getAllCert,
      [constant.EnttypeAry]: req.allEntType,
      [constant.DistrictAry]: req.districtPlus,
      [constant.MDistrictAry]: req.mdistrictPlus,
    }
    let res = await commonApis[str]()
    if (str === 'EnttypeAry') {
      //企业类型 ----2021..7.16 把这个数据是扁平化的需要处理成树形结构 才能和那边保持一致 -- 2024删除1级
      res = arrayToTree(res).filter(i => i.code === 'A')[0]?.children || []
      res = res.map(i => {
        delete i.parent
        return i
      })
    }
    localStorage.setItem(constant[str], JSON.stringify(res))
  } catch (err) {
    console.log(err)
  }
}
/**
 * 本地有数据从本地取没有从服务器拿
 * @param {constant} str
 * @returns
 */
const getOrginData = async function (str, isAll) {
  let dataAry = JSON.parse(localStorage.getItem(str) || '[]'),
    newAry = []

  if (!dataAry.length) {
    await getCommonReq(str)
    dataAry = JSON.parse(localStorage.getItem(str) || '[]')
  }
  if (isAll) {
    // 外面拿到没处理的数据
    return JSON.parse(JSON.stringify(dataAry))
  }
  const flatten = obj => {
    let { code, name, parent, level, children = [] } = obj
    let temObj = {
      code,
      name,
      parent,
      level,
      ischildren: children.length ? false : true,
    }
    newAry.push(temObj)
    children.flatMap(item => flatten(item))
  }

  JSON.parse(JSON.stringify(dataAry)).forEach(item => flatten(item))
  return newAry
}
/**
 * 根据类型拿对应数据
 * @param {*} param0
 * @returns
 */
const getData = async function ({ code, dataType, isAll }) {
  const res = await getOrginData(dataType, isAll)
  if (isAll) {
    //单选全量-不做任何处理的数据
    return res
  }
  const barckAry = code
    ? res.filter(item => item.parent === code).slice()
    : res.filter(item => item.level === '1').slice()
  return barckAry
}
// 扁平化树形结构
const flattenTree = tree => {
  const result = []
  const flatten = node => {
    result.push(node)
    if (node.children) {
      node.children.forEach(child => flatten(child))
    }
  }
  tree.forEach(node => flatten(node))
  return result
}
/**
 *
 * @param {*} dataType
 * @param {*} code // 这里的code都是checked 状态 不会有父亲semi-checked的情况
 * @returns  回显数组
 */
const handleCodeAry = (dataType, code = []) => {
  // 地区树全量 当个数据结构
  //   {
  //     "id": 4566,
  //     "code": "370103",
  //     "name": "市中区",
  //     "parent": "370100", // 1级为空
  //     "level": "3" // 1-省 2-市 3-区
  //       children:[] ,// 1 2 级有
  //      status: '' // 全选为  checked  半选为 semi-checked  未选为 ''
  // }
  let dataAry = JSON.parse(localStorage.getItem(dataType) || '[]')
  // 扁平化地区树数据
  const flatDataAry = flattenTree(dataAry)
  // 存储 code 数组及其父级节点
  const result = []
  // 遍历传入的 code 数组
  code.forEach(item => {
    // 找到当前数据
    const targetData = flatDataAry.find(data => data.code === item)
    if (targetData) {
      // 设置当前数据状态为选中
      if (+targetData.level === 3) {
        targetData.status = 'checked'
      } else if (+targetData.level === 1 || +targetData.level === 2) {
        if (!targetData?.children) {
          targetData.status = 'checked'
          return
        }
        // 处理 1、2 级节点
        const childrenCodes = targetData.children.map(child => child.code)
        // 获取 code 数组中匹配到的子节点的 code 的数量
        const matchedCount = childrenCodes.filter(childCode => code.includes(childCode)).length

        if (matchedCount === childrenCodes.length) {
          // 父级数据全部选中
          targetData.status = 'checked'
        } else {
          if (matchedCount > 0 && matchedCount < childrenCodes.length) {
            // 父级数据部分选中
            targetData.status = 'semi-checked'
          } else {
            // 父级数据没有被选中
            targetData.status = 'checked'
          }
        }
      }
      // 找到父级数据
      let parentData = flatDataAry.find(data => data.code === targetData.parent)
      // 循环向上查找父级数据，直到找到最顶级的父级节点
      while (parentData) {
        // 判断 parentData 是否已经在 result 数组中存在
        if (!result.some(data => data.code === parentData.code)) {
          // 获取当前父级节点的所有子节点的 code
          const childrenCodes = parentData.children.map(child => child.code)
          // 获取 code 数组中匹配到的子节点的 code 的数量
          const matchedCount = childrenCodes.filter(childCode => code.includes(childCode)).length

          if (matchedCount === childrenCodes.length) {
            // 父级数据全部选中
            parentData.status = 'checked'
          } else {
            // 这种只有这两种情况 因为是孩子推父亲
            parentData.status = 'semi-checked'
          }
          // 将 parentData 添加到 result 数组中
          result.push({
            ...parentData,
            active: true,
          })
        }
        // 继续向上查找父级数据
        parentData = flatDataAry.find(data => data.code === parentData.parent)
      }
      // 将当前数据和父级数据添加到 result 数组中
      result.push({
        ...targetData,
        active: true,
      })
    }
  })
  // 返回 code 数组及其父级节点
  return result.map(i => {
    delete i.children // 这里可以不用删除-
    return i
  })
}

export { constant, getData, handleCodeAry }
