export const provinces = [
  {
    name: '全国',
    fullName: '全国',
    code: '100000',
    file: 'china.js',
  },
  {
    name: '浙江',
    fullName: '浙江省',
    code: '330000',
    file: 'zhejiang.js',
  },
  {
    name: '安徽',
    fullName: '安徽省',
    code: '340000',
    file: 'anhui.js',
  },
  {
    name: '澳门',
    fullName: '澳门特别行政区',
    code: '820000',
    file: 'aomen.js',
  },
  {
    name: '北京',
    fullName: '北京市',
    code: '110000',
    file: 'beijing.js',
  },
  {
    name: '重庆',
    fullName: '重庆市',
    code: '500000',
    file: 'chongqing.js',
  },
  {
    name: '福建',
    fullName: '福建省',
    code: '350000',
    file: 'fujian.js',
  },
  {
    name: '甘肃',
    fullName: '甘肃省',
    code: '620000',
    file: 'gansu.js',
  },
  {
    name: '广东',
    fullName: '广东省',
    code: '440000',
    file: 'guangdong.js',
  },
  {
    name: '广西',
    fullName: '广西壮族自治区',
    code: '450000',
    file: 'guangxi.js',
  },
  {
    name: '贵州',
    fullName: '贵州省',
    code: '520000',
    file: 'guizhou.js',
  },
  {
    name: '海南',
    fullName: '海南省',
    code: '460000',
    file: 'hainan.js',
  },
  {
    name: '河北',
    fullName: '河北省',
    code: '130000',
    file: 'hebei.js',
  },
  {
    name: '黑龙江',
    fullName: '黑龙江省',
    code: '230000',
    file: 'heilongjiang.js',
  },
  {
    name: '河南',
    fullName: '河南省',
    code: '410000',
    file: 'henan.js',
  },
  {
    name: '湖北',
    fullName: '湖北省',
    code: '420000',
    file: 'hubei.js',
  },
  {
    name: '湖南',
    fullName: '湖南省',
    code: '430000',
    file: 'hunan.js',
  },
  {
    name: '江苏',
    fullName: '江苏省',
    code: '320000',
    file: 'jiangsu.js',
  },
  {
    name: '江西',
    fullName: '江西省',
    code: '360000',
    file: 'jiangxi.js',
  },
  {
    name: '吉林',
    fullName: '吉林省',
    code: '220000',
    file: 'jilin.js',
  },
  {
    name: '辽宁',
    fullName: '辽宁省',
    code: '210000',
    file: 'liaoning.js',
  },
  {
    name: '内蒙古',
    fullName: '内蒙古自治区',
    code: '150000',
    file: 'neimenggu.js',
  },
  {
    name: '宁夏',
    fullName: '宁夏回族自治区',
    code: '640000',
    file: 'ningxia.js',
  },
  {
    name: '青海',
    fullName: '青海省',
    code: '630000',
    file: 'qinghai.js',
  },
  {
    name: '山东',
    fullName: '山东省',
    code: '370000',
    file: 'shandong.js',
  },
  {
    name: '上海',
    fullName: '上海市',
    code: '310000',
    file: 'shanghai.js',
  },
  {
    name: '山西',
    fullName: '山西省',
    code: '140000',
    file: 'shanxi.js',
  },
  {
    name: '陕西',
    fullName: '陕西省',
    code: '610000',
    file: 'shanxi1.js',
  },
  {
    name: '四川',
    fullName: '四川省',
    code: '510000',
    file: 'sichuan.js',
  },
  {
    name: '台湾',
    fullName: '台湾省',
    code: '710000',
    file: 'taiwan.js',
  },
  {
    name: '天津',
    fullName: '天津市',
    code: '120000',
    file: 'tianjin.js',
  },
  {
    name: '香港',
    fullName: '香港特别行政区',
    code: '810000',
    file: 'xianggang.js',
  },
  {
    name: '新疆',
    fullName: '新疆维吾尔自治区',
    code: '650000',
    file: 'xinjiang.js',
  },
  {
    name: '西藏',
    fullName: '西藏自治区',
    code: '540000',
    file: 'xizang.js',
  },
  {
    name: '云南',
    fullName: '云南省',
    code: '530000',
    file: 'yunnan.js',
  },
]

export const findProvince = (value, prop = 'name') => {
  if (typeof value === 'number') value += ''
  return provinces.find(item => item[prop] === value) || {}
}
