/* ./src/index.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .f-all-center {
    @apply flex items-center justify-center;
  }
  .f-y-center {
    @apply flex items-center;
  }
  .f-x-center {
    @apply flex justify-center;
  }
  .abs-y-center {
    @apply absolute transform -translate-y-1/2 top-1/2;
  }
  .abs-x-center {
    @apply absolute transform -translate-x-1/2 left-1/2;
  }
  .blue {
    @apply border border-blue-700 border-solid;
  }
  .red {
    @apply border border-red-600 border-solid;
  }
  /* 滚动条透明样式 */
  .scrollbar-transparent::-webkit-scrollbar {
    width: 0;
    background-color: transparent;
  }

  .scrollbar-transparent::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0); /* 设置滚动条颜色为半透明黑色 */
  }

  .btn {
    @apply flex items-center justify-center text-32 h-80 rounded-[8px];
  }
  .confirm-btn {
    @apply text-white btn bg-confirm;
  }
  .cancel-btn {
    @apply btn text-primary bg-cancel;
  }
}
