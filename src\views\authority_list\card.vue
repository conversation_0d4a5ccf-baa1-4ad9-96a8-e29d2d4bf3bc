<template>
  <div>
    <div class="list-item">
      <div class="item-top flex justify-between">
        <div class="f-y-center">
          <img src="@/assets/image/type_icon.png" class="type_img" />
          <div class="name">{{ item.type_str }}</div>
        </div>
        <div class="text-[#9B9EAC] text-24">
          {{ item.release_date }}
        </div>
      </div>
      <div class="item-bottom" @click="getAuthorityList(item)">
        <div class="label">
          <HightlightText :query="keyword" :text="item.name" />
        </div>
        <div class="right"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import HightlightText from '@/components/HightlightText/index.vue'
import { pageChangeHandler } from '@/utils'

const router = useRouter()

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  keyword: {
    type: String,
    default: '',
  },
})

const getAuthorityList = function (item) {
  const { id, release_date, source, name, type } = item
  pageChangeHandler('/authority-ent-list', { id, release_date, source, name, type })
}
</script>

<style lang="scss" scoped>
.list-item {
  height: 220px;
  margin: 24px 20px;
  background: #ffffff;
  border-radius: 8px;
}
.item-top {
  padding: 0 32px;
  height: 96px;
  border-bottom: 2px solid #eee;
  display: flex;
  align-items: center;
}
.type_img {
  width: 40px;
  height: 40px;
  margin-right: 12px;
}
.name {
  font-size: 28px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #74798c;
}
.item-bottom {
  height: 124px;
  padding: 0 28px 0 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.item-bottom .label {
  font-size: 32px;
  font-family: PingFang SC-Semibold, PingFang SC;
  color: #20263a;
  line-height: 38px;
  width: 90%;
  // background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAALdJREFUSEu11T0OgzAMhuHPmbhYxcpeqRfJykEQgs6sVa7AgZhwlc7F+AeYo/cJkSwTbv5I6g/vkhNjfT0fH+89DoEaB3MPYEugzoscAuNc2p15AaGJIOITXYGIQH33KHIKRBEVEEHUgBcxAR7EDFgRF2BBYgB4AdCAsSX6P+0u4Dcbinj9UzNgiZsBa9wEeOJqwBtXAZH4KRCNi8AVcREYppJB3EtDpNnT8tKfSk5009LX3E5z5gtY2L0ZrugeNQAAAABJRU5ErkJggg==')
  //   no-repeat right center;
  // background-size: 24px 24px;
  span {
    font-weight: 600;
  }
}
.right {
  width: 24px;
  height: 24px;
  line-height: 38px;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAALdJREFUSEu11T0OgzAMhuHPmbhYxcpeqRfJykEQgs6sVa7AgZhwlc7F+AeYo/cJkSwTbv5I6g/vkhNjfT0fH+89DoEaB3MPYEugzoscAuNc2p15AaGJIOITXYGIQH33KHIKRBEVEEHUgBcxAR7EDFgRF2BBYgB4AdCAsSX6P+0u4Dcbinj9UzNgiZsBa9wEeOJqwBtXAZH4KRCNi8AVcREYppJB3EtDpNnT8tKfSk5009LX3E5z5gtY2L0ZrugeNQAAAABJRU5ErkJggg==')
    no-repeat right center;
  background-size: 24px 24px;
}
:deep(.light) {
  font-weight: 600 !important;
}
</style>
