<template>
  <section v-if="noData" class="mt-24">
    <Empty> 新入库企业，暂无项目质量评分 </Empty>
  </section>
  <section v-else class="mt-24">
    <div class="bg-white">
      <div class="w-full">
        <div class="flex justify-center h-520">
          <chart-radar id="radar-chart" :data="radarData" :title="cacheData['总得分']" />
        </div>
      </div>

      <div class="flex items-center justify-center">
        <span class="text-secondary text-32">项目质量评估：</span>
        <div class="font-semibold text-primary text-36">{{ cacheData['总得分'] }}</div>
        <span class="text-[#1E75DB] text-32 font-semibold ml-16">{{ cacheData['对应评估等级'] }}</span>
      </div>

      <div v-if="tableData.length" class="w-full p-24">
        <table class="w-full p-24">
          <tr>
            <th
              v-for="item in columns"
              :key="item.title"
              :class="`w-${item.width}`"
              class="p-20 text-left border-2 text-24 text-tertiary bg-grey border-[#eee]"
            >
              {{ item.title }}
            </th>
          </tr>
          <tr v-for="item in tableData">
            <td :class="tdClass">{{ item.indicators_name }}</td>
            <td :class="tdClass">{{ item.indicators_value }}</td>
            <td :class="tdClass">{{ item.indicators_contribution }}</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="px-24 py-32 mt-24 bg-white interpret-info">
      <p class="text-28 text text-headline mb-18">指数解读</p>
      <p class="p-24 text-tertiary bg-grey text-28">
        本次评估结果，该公司在其所处
        <span class="text-[#1E75DB]">{{ cacheData['投资区域'] }}</span> 向
        <span class="text-[#1E75DB]">{{ cacheData['被投资区域'] }}</span> 投资项目质量为
        <span class="text-[#1E75DB]">{{ cacheData['对应评估等级'] }}</span> 。
      </p>
    </div>
  </section>
</template>

<script setup name="projectQuality">
import ChartRadar from './chartRadar.vue'
import { getProjectScore as getScore } from '@/api/qualityReport'
import Empty from './components/empty.vue'

const radarData = ref([])
const noData = ref(false)
const cacheData = ref({})
const route = useRoute()
const dimensions = [
  { label: '企业关联程度', cont: 0.15, name: '企业与目标地区关联程度' },
  { label: '企业历史投资', cont: 0.15 },
  { label: '区域行业扩张能力', cont: 0.1 },
  { label: '企业规模', cont: 0.06 },
  { label: '企业效益情况', cont: 0.09 },
  { label: '企业成长能力', cont: 0.18 },
  { label: '企业扩张能力', cont: 0.21 },
  { label: '企业抗风险能力', cont: 0.06 },
]

const tdClass = `p-20 text-left text-primary text-24 border-2 border-[#eee]`
const columns = [
  {
    title: '指标名称',
    width: '306',
    dataIndex: 'indicators_name',
  },
  {
    title: '指标值',
    width: '108',
    dataIndex: 'indicators_value',
  },
  {
    title: '指标贡献度',
    width: '108',
    dataIndex: 'indicators_contribution',
  },
]

const tableData = ref([])

const getStockingScore = async ent_id => {
  let res = await getScore(ent_id)

  cacheData.value = res

  if (Object.keys(res).length) {
    const data = []
    const radarDataTemp = []

    dimensions.forEach(item => {
      const name = item.label
      const cont = item.cont
      const value = parseFloat(res[name] ?? 0)

      radarDataTemp.push(value)
      data.push({
        indicators_name: name,
        indicators_value: value,
        indicators_contribution: cont,
      })
    })

    tableData.value = data
    radarData.value = radarDataTemp
  } else {
    noData.value = true
  }
}

onMounted(() => {
  getStockingScore(route.query.ent_id)
})

function onUpdateData() {
  return getStockingScore()
}
</script>

<style lang="scss" scoped></style>
