import { transformDate } from '@/utils'
const TODAY = transformDate(new Date())

const RISK_LEVEL_MAP = [
  { value: '', text: '全部' },
  { value: '5', text: '高风险' },
  { value: '4', text: '风险' },
  { value: '3', text: '警示' },
  { value: '2', text: '提示' },
  { value: '1', text: '利好' },
]
const OPINIONS_PREFER_MAP = [
  { value: '', text: '全部' },
  { value: '1', text: '积极' },
  { value: '0', text: '中立' },
  { value: '-1', text: '消极' },
]
const OPINIONS_TYPE_MAP = [
  { value: '', text: '全部' },
  { value: '1', text: '经营相关' },
  // { value: '2', text: '财务相关' },
  { value: '3', text: '企业管理' },
  { value: '4', text: '产品信息' },
  // { value: '5', text: '项目相关' },
  { value: '6', text: '市场相关' },
  { value: '7', text: '其他' },
]
const RISK_TIME_MAP = [
  { value: '', text: '全部' },
  { value: [transformDate(Date.now() - 86400000) + '$' + TODAY], text: '近24小时' },
  { value: [transformDate(Date.now() - 86400000 * 7) + '$' + TODAY], text: '近7天' },
  { value: [transformDate(Date.now() - 86400000 * 30) + '$' + TODAY], text: '近30天' },
]
const RISK_TYPE_MAP = [
  { value: '', text: '全部' },
  { value: 'A', text: '司法涉诉' },
  { value: 'B', text: '工商变更' },
  { value: 'C', text: '无形资产' },
  {
    value: 'D',
    text: '经营风险',
  },
  {
    value: 'E',
    text: '经营信息',
  },
]
export const options3 = [
  {
    code: 'LEVEL',
    name: '风险级别',
    value: '',
    children: RISK_LEVEL_MAP,
  },
  {
    code: 'TYPE',
    name: '风险类别',
    value: '',
    children: RISK_TYPE_MAP,
  },
]
export const options1 = [
  ...options3,
  {
    code: 'TIME',
    name: '统计时间',
    value: '',
    children: RISK_TIME_MAP,
  },
]

export const options2 = [
  {
    code: 'PREFER',
    name: '舆情倾向',
    value: '',
    children: OPINIONS_PREFER_MAP,
  },
  {
    code: 'RTYPE',
    name: '舆情类别',
    value: '',
    children: OPINIONS_TYPE_MAP,
  },
  {
    code: 'TIME',
    name: '统计时间',
    value: '',
    children: RISK_TIME_MAP,
  },
]
