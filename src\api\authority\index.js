import { Post, Get, Delete } from '@/request'
import { CHAIN_URL } from '../constants'

// 获取权威榜单榜单类型
export function getTypes(data) {
  return Get(`${CHAIN_URL}/list/dictionaryNewData`)
}

// 获取权威榜单列表
export function getAuthorityList(data) {
  return Post(`${CHAIN_URL}/list/newUsablePage`, data)
}

//权威榜单企业列表
export function getAuthorityEntList(data) {
  return Post(`${CHAIN_URL}/list/newUsableList/page`, data)
}

/////龙头企业  就一个接口  不单独起文件了  这个接口也是企业列搜接口 通用
export function getLeadingEntList(data) {
  return Post(`${CHAIN_URL}/industry/ent/portrait/list`, data)
}

//上市企业
export function getListedEntList(data) {
  return Post(`${CHAIN_URL}/listed/board/list`, data)
}
//公告公示
export function getProclamationList(data) {
  return Post(`${CHAIN_URL}/listed/proclamation/list`, data)
}

export default {}
