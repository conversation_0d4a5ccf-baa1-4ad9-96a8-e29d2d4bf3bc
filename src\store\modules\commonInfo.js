import { defineStore } from 'pinia'
import { store } from '../index'
import { transferChainCodeTree } from '@/utils'
import { getPresetInfo, getAreaInfo, getChainCodes } from '@/api/common'

export const useCommonInfo = defineStore({
    id: 'common-info',
    state: () => ({
        presetInfo: null,
        areaInfo: null,
        chainCodesTree: null
    }),
    getters: {
        presetChainInfo: state => {
            return (
                (state.presetInfo &&
                    state.presetInfo.estate_list.map((item, index) => {
                        return {
                            chain_name: item,
                            chain_code: state.presetInfo?.estate_code_list[index]
                        }
                    })) ||
                []
            )
        },
        // 拼装了上中下游的数据
        chainCodesTree_: state => {
            return transferChainCodeTree(state.chainCodesTree)
        },
        presetAreaInfo: state => {
            return (
                (state.presetInfo &&
                    state.presetInfo.carrier_area_list.map((item, index) => {
                        return { name: item, code: state.presetInfo?.carrier_area_code_list[index] }
                    })) ||
                []
            )
        }
    },
    actions: {
        // 获取预设产业链信息
        getPresetInfo() {
            return getPresetInfo().then(result => {
                this.presetInfo = result
                if (!this.chainCodesTree && this.presetInfo) {
                    this.getChainCodesHandler(this.presetInfo.estate_code_list)
                }
                return result
            })
        },
        // 获取预设地区信息
        getAreaInfo() {
            return getAreaInfo().then(result => {
                this.areaInfo = result?.data || []
                return result
            })
        },
        // 获取预设产业链对应的详情（包含上中下游）
        getChainCodesHandler(data) {
            return getChainCodes(data).then(result => {
                this.chainCodesTree = result
                return result
            })
        },
        resetState() {
            this.presetInfo = null
            this.areaInfo = null
            this.chainCodesTree = null
        }
    }
})
export function useCommonInfoWithOut() {
    return useCommonInfo(store)
}
