<script setup name="ChainPops">
import { onBeforeMount, reactive, getCurrentInstance, watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  oldData: {
    type: Array,
  },
  mark: {
    type: String,
  },
  dataType: {
    type: String,
  },
})
const emits = defineEmits(['close', 'submit'])
const { proxy } = getCurrentInstance()

const data = reactive({
  visible: false,
  activeChildList: [], // 处于活动状态的子级列表
  sourceData: [],
})
/**
 * 获取数据
 */
const getAllChainData = async () => {
  let allChainData = localStorage.getItem('allChainDatas')
  if (!allChainData) {
    let [, res] = await proxy.$to('chainAll')
    res.unshift({
      MultipleCelectionSingleSelection: true,
      belong_to: '',
      chain_code: 'ALL',
      children: [],
      code: 'ALL',
      level: '1',
      name: '全部',
    })
    allChainData = res.map(chain => {
      chain.code = chain.chain_code
      chain.MultipleCelectionSingleSelection = true
      chain.children = chain.children.map(child => {
        child.code = child.chain_code
        child.MultipleCelectionSingleSelection = true
        return child
      })
      chain.children.unshift({
        name: chain.name == '全部' ? chain.name : '全部' + chain.name, //全部,
        code: chain.code,
        parent_code: chain.code,
        MultipleCelectionSingleSelection: true,
      })
      return chain
    })
    localStorage.setItem('allChainDatas', JSON.stringify(allChainData))
  } else {
    allChainData = JSON.parse(allChainData)
  }
  data.sourceData = allChainData
}
/**
 * 选中切换
 */
const handleChange = (select, parentind) => {
  let { sourceData, activeChildList } = data
  const updateActiveStatus = (list, code) =>
    list.map(item => {
      item.active = item.code === code
      return item
    })

  if (parentind?.toString()) {
    sourceData = updateActiveStatus(sourceData, select.code)
    activeChildList = JSON.parse(JSON.stringify(sourceData.find(item => item.active).children))
    activeChildList[0].active = true
    data.sourceData = sourceData
    data.activeChildList = activeChildList
  } else {
    data.activeChildList = updateActiveStatus(activeChildList, select.code)
  }
}
/**
 *设置默认选中
 */
const setDefaultSelect = (code, paramsData) => {
  for (let item of paramsData) {
    if (item.code === code) {
      const { sourceData } = data
      item.active = true
      const parentInd = sourceData.findIndex(val => val.code === item.parent_code).toString()

      if (parentInd !== '-1') {
        sourceData[parentInd].active = true
        data.sourceData = sourceData
        data.activeChildList = JSON.parse(JSON.stringify(paramsData))
      } else {
        const tempArr = JSON.parse(JSON.stringify(item.children))
        tempArr[0].active = true
        data.sourceData = paramsData
        data.activeChildList = tempArr
      }
    } else {
      item.active = false
      item.children && setDefaultSelect(code, item.children)
    }
  }
}
/**
 * 重置所有状态
 */
const resetAllStates = (source, callback) => {
  if (!source || !source.length) return
  source.forEach(item => {
    item.active = false
    if (item.children) {
      item.children.forEach(itm => (itm.active = false))
    }
  })
  callback && callback()
}
/**
 * 弹窗回调
 */
const submit = () => {
  data.visible = false
  let arr = data.activeChildList.filter(item => item.active)
  arr = arr.filter(i => i.code != 'ALL')
  emits('submit', {
    checkedList: arr,
    mark: props.mark,
  })
}
/**
 * 关闭弹窗
 */
const close = () => {
  data.visible = false
  emits('close', {
    checkedList: data.oldData,
    mark: data.mark,
    visible: false,
  })
}
watch(
  () => props.visible,
  val => {
    data.visible = val
    if (!val) return
    let { sourceData } = data
    resetAllStates(sourceData, () => {
      setDefaultSelect(props.oldData[0]?.code || 'ALL', sourceData)
    })
  },
  {
    immediate: true,
  }
)
//生命周期
onBeforeMount(() => {
  getAllChainData()
})
</script>
<template>
  <div>
    <div class="chain-wrap">
      <!-- 父级列表 -->
      <div class="parent">
        <div
          v-for="(item, index) in data.sourceData"
          :key="index"
          @click="handleChange(item, index)"
          :class="{ item: true, active: item.active }"
        >
          <div class="name text-ellipsis">{{ item.name }}</div>
        </div>
      </div>
      <!-- 子级列表 -->
      <div class="children">
        <div
          v-for="(child, childIndex) in data.activeChildList"
          :key="childIndex"
          @click="handleChange(child)"
          :class="{ item: true, 'text-ellipsis': true, active: child.active }"
        >
          {{ child.name }}
        </div>
      </div>
    </div>
    <div class="footer">
      <button class="close" @click="close">取消</button>
      <button class="submit" @click="submit">确认</button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.chain-wrap {
  background-color: #fff;
  color: #20263a;
  font-size: 26px;
  position: relative;
  overflow-x: hidden;
}

.parent {
  width: 308px;
  height: 756px;
  background-color: rgba(247, 247, 247, 0.6);
  overflow-y: scroll;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}

.item {
  height: 84px;
  line-height: 84px;
  padding-left: 24px;
  font-size: 26px;
  color: #20263a;
  font-family: PingFang SC-Regular, PingFang SC;
}

.item.active {
  background-color: #fff;
  color: #2a72ff;
  font-weight: 600;
  font-family: PingFang SC-Semibold, PingFang SC;
}

.item .name {
  width: 220px;
}

.text-ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.children {
  height: 756px;
  width: 100%;
  position: absolute;
  left: 308px;
  top: 0;
  overflow-y: scroll;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}
.footer {
  width: 100%;
  display: flex;
  padding: 10px 0;
  justify-content: space-between;

  button {
    width: 340px;
    height: 80px;
    line-height: 80px;
    border: none !important;
    padding: 0;
    font-size: 32px;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 400;
    text-align: CENTER;
    color: #74798c;
    margin: 0 12px 10px;
    background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
    border-radius: 8px;
    &.close {
      background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
      color: #74798c;
    }
    &.submit {
      background: linear-gradient(272deg, #076ee4 0%, #53a2ff 100%) !important;
      color: #fff;
    }
  }
}
</style>
