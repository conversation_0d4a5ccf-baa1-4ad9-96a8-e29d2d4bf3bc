<script setup name="SearchAdvance">
import <PERSON> from '@/components/Hunt/index.vue'
import VanbPop from '@/components/Van/VanbPop/index.vue'
import {
  searRadioConstant,
  handleData,
  searInputConstant,
  searMultPopConstant,
  getNameFromPop,
  searMultNotCusConstant,
  searMultIptConstant,
  searMultiSelectConstant,
  searCheckPopConstant,
  searMultAllConstant,
  getHeightStatus,
} from '@/components/Hunt/common/huntAbout'
import { searchTermList } from '@/components/Hunt/common/hconstant'
import { handleCodeAry } from '@/components/Hunt/component/MultiplecChoice/utils.js'
import { onMounted, reactive, ref, getCurrentInstance, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { pageChangeHandler } from '@/utils'
import { saveTemplate } from '@/api/advanceSearch/index.js'

const { proxy } = getCurrentInstance()
const router = useRouter()
const route = useRoute()
const domRef = ref(null)
const bottomRef = ref(null)
const huntHeight = ref('0px')
const hunRef = ref(null)
const data = reactive({
  paramsData: {},
  isHeight: false,
  saveSearVal: '',
  updating: false,
})

onMounted(()=>{
  let tempObj;
  let { search_json } = route.query
  tempObj = echoData(JSON.parse(search_json))

  //   调用模版
  hunRef.value?.setBackfillData(tempObj)
})
/**
 * 筛选回调
 */
const getParams = ({ isHeight, paramsData }) => {
  data.isHeight = isHeight
  data.paramsData = paramsData
}
/**
 * 清空当前选项
 */
const clearSear = () => {
  hunRef.value?.clearSear()
}

/**
 * 立即搜索
 */
const submitSear = async () => {
  let obj
  const { paramsData, isHeight } = data
  if (!isHeight) {
    proxy.$showToast('请添加筛选条件后再试!')
    return
  }
  if (paramsData['register_time']?.length) {
    paramsData['register_time'].map(i => {
      delete i['name']
    })
  }
  paramsData['isHeightParams'] = true //区分其它筛选
  obj = handleData(paramsData)
  if (!hunRef.value?.checkoutSear(paramsData)) {
    return
  }

  // 存一份 用来返回时渲染
  localStorage.setItem('backParams', JSON.stringify(paramsData))
  //高级搜索
  localStorage.setItem('hParams', encodeURIComponent(obj))
  pageChangeHandler('/search')
}
/**
 * 保存模版 --这里不保存企业名称
 */
const saveSear = () => {
  let { paramsData } = data
  // delete paramsData['ent_name']

  if (getHeightStatus(paramsData) && hunRef.value?.checkoutSear(paramsData)) {
    data.saveSearPop = true
    data.saveSearVal = ''
    data.updating = false
  } else {
    proxy.$showToast('请选择筛选条件！', 'none', 1500)
  }
}
/**
 * 搜索模版处理数据
 */
const searRenders = obj => {
  let renderArr = []
  Object.keys(obj).forEach(key => {
    // 这是淘汰的字段
    if (['classic_industry'].includes(key)) return
    renderArr.push({
      name: obj[key].title,
      value: Array.isArray(obj[key].label) ? obj[key].label.join(',') : obj[key].label,
    })
  })
  return renderArr
}
/**
 * 搜索模版
 */
const getsearData = async () => {
  let [err, res] = await proxy.$to('getTemplate')
  let items = res || false
  let searRender = {}
  if (!items || err) {
    // proxy.$showToast('网络丢失,请稍后再试!')
    items = []
    return
  }
  items.map((item, index) => {
    item.active = false
    if (index === 0) {
      item.active = true
      searRender = JSON.parse(item['search_json'] || null)
    }
    return item
  })
  let renderAry = searRenders(searRender)
  data.templateAry = items
  data.renderAry = renderAry
}

/**
 * 反向渲染模版
 */
const onsearItm = (item, index) => {
  let { search_json } = item
  let { templateAry } = data
  let renderObj = JSON.parse(search_json || null)
  let idx = templateAry.findIndex(i => i.active)
  if (index != idx) {
    templateAry[idx].active = false
    templateAry[index].active = true
  }
  let renderAry = searRenders(renderObj)
  // 設置企業规模显示的数据
  data.templateAry = templateAry
  data.renderAry = renderAry
}
// 构造能够回显的数据结构
const echoData = source => {
  let dataSource = {}
  Object.keys(source).forEach(key => {

    const objVal = source[key]
    const originObj = searchTermList.find(itm => itm.type == key || itm.type == `super_dimension_${key}`)
    // 输入框的情况
    if (searInputConstant['list'].includes(key) && objVal.value.trim()?.length > 0) {
      dataSource[key] = objVal.value
    }
    // 单选的情况
    if (
      (searRadioConstant['list'].includes(key) && objVal.value) ||
      (searRadioConstant['list'].includes(`super_dimension_${key}`) && objVal.value)
    ) {
      if (key === 'leading_ent') {
        // 龙头适配pc
        dataSource[key] = [objVal.value[0] + '']
      } else if (searRadioConstant['list'].includes(`super_dimension_${key}`)) {
        dataSource[`super_dimension_${key}`] = [objVal.value === true ? 'true' : objVal.value]
      } else {
        dataSource[key] = [objVal.value === true ? 'true' : objVal.value]
      }
    }
    // 多选
    if (searMultiSelectConstant['list'].includes(key) && objVal.value.length > 0) {
      dataSource[key] = objVal.value
      if (key === 'chain_codes') {
        dataSource['chain_codes_data'] = objVal.value.map((i, idx) => ({
          name: source['chain_codes'].label[idx],
          code: i,
          active: true,
          MultipleCelectionSingleSelection: 'isSingles',
          level: '1',
        }))
      }
    }
    // 多选的情况  & -没有自定义的情况
    if (searMultNotCusConstant['list'].includes(key) && objVal.value.length > 0) {
      dataSource[key] = objVal.value.map(i => {
        const [start = '', end = ''] = i.split('$')
        return {
          start,
          end,
        }
      })
    }

    // 多选- & 自定义的情况
    if (
      (searCheckPopConstant['list'].includes(key) ||
        searMultIptConstant['list'].includes(key) ||
        ['social_num'].includes(key)) &&
      objVal.value.length > 0
    ) {
      dataSource[['social_num'].includes(key) ? `super_dimension_${key}` : key] = objVal.label.map((name, index) => {
        let ids = originObj.list.find(item => item.name == name)
        if (ids) {
          const [start, end] = ids.id.split('$')
          return {
            name: ids.name,
            start,
            end,
          }
        }
        // 自定义的情况
        const { start = '', end = '' } = objVal.value[index]
        return {
          name,
          start,
          end,
          special: true,
        }
      })
    }
    // 弹窗的情况
    if (Object.keys(searMultPopConstant).includes(key) && !['chain_codes'].includes(key)) {
      const maps = {
        areas: 'DistrictAry',
        trade_types: 'EleseicAry',
        ent_type: 'EnttypeAry',
        ent_cert: 'AllCertAry',
        // chain_codes: 'allChainDatas',
      }
      dataSource[key] = objVal.value
      dataSource[searMultPopConstant[key].data] = handleCodeAry(maps[key], objVal.value)
    }
    // 部分特殊情况--处理上传的时候这个处理了 super_dimension_patent_category
    if (['patent_category'].includes(key)) {
      dataSource[`super_dimension_${key}`] = objVal.value
    }
  })
  return dataSource
}
// 构造pc能渲染的数据结构
const echoPcData = paramsData => {
  let dataSource = {}
  // 企业名称不要
  // delete paramsData.ent_name
  Object.keys(paramsData).forEach(key => {
    const objValue = paramsData[key]
    const originObj = searchTermList.find(itm => itm.type == key)
    const mockData = {
      prop: key.includes('super_dimension_') ? `${key.slice('super_dimension_'.length)}` : key,
      label: [],
      title: originObj?.title || '',
      value: [],
    }
    // 输入框的情况
    if (searInputConstant['list'].includes(key) && objValue.trim()?.length > 0) {
      mockData['label'] = objValue
      mockData['value'] = objValue
      dataSource[key] = mockData
    }
    // 单选的情况
    if (searRadioConstant['list'].includes(key) && objValue) {
      const value = ['true', 'false'].includes(objValue[0]) ? Boolean(objValue[0]) : objValue[0]
      mockData['value'] = key === 'leading_ent' ? [value] : value
      mockData['label'] = originObj.list.find(item => item.id == objValue[0])?.name
      if (key.includes('super_dimension_')) {
        dataSource[`${key.slice('super_dimension_'.length)}`] = mockData
      } else {
        dataSource[key] = mockData
      }
    }
    // 多选
    if (searMultiSelectConstant['list'].includes(key) && objValue.length > 0) {
      // 特俗情况 处理产业链
      if (key === 'chain_codes') {
        mockData['label'] = paramsData['chain_codes_data'].map(i => i.name)
        mockData['value'] = objValue
        dataSource[key] = mockData
      } else {
        mockData['value'] = [...objValue]
        mockData['label'] = originObj.list.filter(item => objValue.includes(item.id)).map(i => i.name)
        if (key.includes('super_dimension_')) {
          dataSource[`${key.slice('super_dimension_'.length)}`] = mockData
        } else {
          dataSource[key] = mockData
        }
      }
    }
    // 多选的情况  & -没有自定义的情况
    if (searMultNotCusConstant['list'].includes(key) && objValue.length > 0) {
      mockData['value'] = objValue.map(i => {
        return `${i.start}$${i.end}`
      })
      mockData['label'] = originObj.list.filter(item => mockData['value'].includes(item.id)).map(i => i.name)
      dataSource[key] = mockData
    }
    // 多选- & 自定义的情况
    if (
      (searCheckPopConstant['list'].includes(key) && objValue.length > 0) ||
      (searMultIptConstant['list'].includes(key) && objValue.length > 0)
    ) {
      console.log('ddd', key)

      objValue.map(i => {
        const { start = '', end = '' } = i
        const id = `${start}$${end}`
        const shuJu = originObj.list.find(item => item.id == id)
        if (shuJu) {
          mockData['value'].push(id)
          mockData['label'].push(shuJu.name)
        } else {
          // 自定义的情况
          if (searCheckPopConstant['list'].includes(key)) {
            const { unit } = searCheckPopConstant['tip'][key]
            const text = `${start}${start && end ? '-' : ''}${end}${unit}`
            mockData['value'].push({ start, end })
            mockData['label'].push(text)
          }
          if (searMultIptConstant['list'].includes(key)) {
            const { unit } = searMultIptConstant['tip'][key]
            const text = `${start}${start && end ? '-' : ''}${end}${unit}`
            mockData['value'].push({ start, end })
            mockData['label'].push(text)
          }
        }
      })

      if (key.includes('super_dimension_')) {
        dataSource[`${key.slice('super_dimension_'.length)}`] = mockData
      } else {
        dataSource[key] = mockData
      }
    }
    // 弹窗的情况
    if (Object.keys(searMultPopConstant).includes(key)) {
      mockData['title'] = searMultPopConstant[key].label
      const shuJu = paramsData[searMultPopConstant[key].data]
      mockData.label = shuJu.map(i => i.name)
      mockData.value = shuJu.map(i => i.code)
      dataSource[key] = mockData
    }
  })
  return dataSource
}
/**
 * 模版保存确定
 */
const saveSearSub = async () => {
  let { saveSearVal, paramsData, updating } = data
  if (updating) return
  saveSearVal = saveSearVal.trim()
  if (!saveSearVal) {
    proxy.$showToast('请输入模版名称!', 'none', 1500)
    return
  }
  let copyObj = {
    template_type: 'P', // 到时候和pc不保持一致 就去掉这个
    title: saveSearVal,
    search_json: JSON.stringify(echoPcData(paramsData)),
    user_id: JSON.parse(localStorage.getItem('hm_tokenInfo')).user_id, // 到时候和pc不保持一致 就去掉这个
  }
  data.updating = true
  try {
    await saveTemplate(copyObj)
    proxy.$showToast('模版新增完成!', 'none', 2000)
  } catch (error) {
    proxy.$showToast(error?.message || '新增模板失败!', 'none', 2000)
  }
  // 发请求 关闭弹窗
  data.saveSearPop = false
  getsearData() //发送请求刷新数据搜索模版
}
/**
 * 应用模版
 */
const searsub = () => {
  let { templateAry } = data,
    tempObj
  if (templateAry.length <= 0) return

  let { search_json } = templateAry.filter(item => item.active)[0]
  tempObj = echoData(JSON.parse(search_json))

  //   调用模版
  hunRef.value?.setBackfillData(tempObj)
}
/**
 * 删除模版
 */
const onsearDel = async (item, index) => {
  //搜索模版-删除
  const { id } = item
  let { templateAry } = data
  try {
    const [err, res] = await proxy.$to('delTemplate', id)
    if (err) {
      proxy.$showToast('删除失败,请稍后再试!')
      return
    }
    // 删除成功
    if (templateAry[index]?.active && templateAry.length >= 2) {
      //恰好删除的是高亮的这个

      let item, idx
      if (index === 0) {
        item = templateAry[1]
        idx = 1
      } else {
        item = templateAry[0]
        idx = 0
      }
      onsearItm(item, idx)
    }
    templateAry.splice(index, 1)
    data['templateAry'] = templateAry
  } catch (err) {
    proxy.$showToast('删除失败,请稍后再试!')
  }
}

onMounted(() => {
  const { height } = useWindowSize()
  nextTick(() => {
    const { top } = useElementBounding(domRef)
    const { height: bottomRefH } = useElementBounding(bottomRef)
    huntHeight.value = height.value - top.value - bottomRefH.value + 'px'
  })
  getsearData()
})
onMounted(() => {
  // 如果时返回的 就拿到数据 并清空缓存
  const source = localStorage.getItem('backParams')
  if (source) {
    data.paramsData = JSON.parse(source)
    nextTick(() => {
      hunRef.value?.setBackfillData(data.paramsData)
      localStorage.removeItem('backParams')
    })
  }
})
</script>

<template>
  <div class="box">
    <div class="zhanWei" ref="domRef"></div>
    <!-- 筛选 -->
    <Hunt :height="huntHeight" @submit="getParams" ref="hunRef" />
    <!-- 按钮 -->
    <div class="h-auto van-safe-area-bottom" ref="bottomRef">
      <div class="flex justify-between footer">
        <div @click="clearSear">清空条件</div>
        <div @click="saveSear">保存模板</div>
        <div @click="submitSear">立即搜索</div>
      </div>
    </div>
    <!-- 搜索模板 -->
    <div class="s-btn" @click="() => (data.searPop = true)">
      <img
        src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search-white.png"
        mode="aspectFit"
      />
      <span class="txt">搜模板</span>
    </div>
  </div>
  <!-- 保存选项弹窗 -->
  <div v-if="data.saveSearPop" class="fadeIn mask">
    <div class="box">
      <div class="tit">保存选项</div>
      <div class="inpt">
        <input
          type="text"
          v-model="data.saveSearVal"
          placeholder="请输入自定义保存模版名称"
          class="placeholder-input"
        />
      </div>
      <div class="btn">
        <div @click="() => (data.saveSearPop = false)">取消</div>
        <!-- 确定判断是否为空 吐司提示 -->
        <div style="color: #2a72ff" @click="saveSearSub">确定</div>
      </div>
    </div>
  </div>
  <!-- 搜索模版 -->
  <VanbPop
    :visible="data.searPop"
    @close="() => (data.searPop = false)"
    @submit="searsub"
    confirmBtnText="应用"
    cancelBtnText="退出"
  >
    <div class="sear-con">
      <template v-if="data.templateAry.length > 0">
        <div class="sear-con-h">
          <!-- 给一个死的高度 让其滚动 scroll-y-->
          <div class="sear-con-h-box">
            <template v-for="(item, index) in data.templateAry" :key="index">
              <div class="item" :class="{ active: item.active }" @click="onsearItm(item, index)">
                <span class="item-l">{{ item.title }}</span>
                <img
                  class="item-r"
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/del.png"
                  v-if="true"
                  @click.stop="onsearDel(item, index)"
                />
              </div>
            </template>
          </div>
        </div>
        <div class="sear-con-b">
          <div class="title">企业规模</div>
          <div class="tagbox">
            <div class="wrap">
              <template v-for="(item, index) in data.renderAry" :key="index">
                <div class="tag">{{ item.name + ':' + item.value }}</div>
              </template>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div style="height: 400px">
          <div class="null">
            <div class="img">
              <img src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/null.png" />
            </div>
            <div class="text">{{ errTips || '暂无数据' }}</div>
          </div>
        </div>
      </template>
    </div>
  </VanbPop>
</template>
<style lang="scss" scoped>
.box {
  padding-top: $inset-top;
}
.footer {
  width: 100%;
  background-color: #fff;
  padding: 10px 24px;
  box-shadow: 8px 0px 8px 2px rgba(204.00000303983688, 204.00000303983688, 204.00000303983688, 0.20000000298023224);
  padding-bottom: 40px;
  div {
    width: 220px;
    height: 80px;
    line-height: 80px;
    text-align: center;
    border-radius: 8px;
    color: #20263a;
    font-size: 32px;
    cursor: pointer;

    &:nth-child(1) {
      background: linear-gradient(315deg, #eeeeee 0%, #f5f5f5 100%);
    }
    &:nth-child(2) {
      background: linear-gradient(315deg, #eeeeee 0%, #f5f5f5 100%);
    }
    &:nth-child(3) {
      color: #fff;
      background: linear-gradient(315deg, #076ee4 0%, #53a2ff 100%);
    }
  }
}
// 保存选项
.fadeIn {
  -webkit-animation: c 0.3s forwards;
  animation: c 0.3s forwards;
}
.s-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed;
  right: 32px;
  bottom: 300px;
  width: 100px;
  height: 100px;
  background: linear-gradient(90deg, #94b2ed 0%, #2a72ff 100%);
  box-shadow: 0px 4px 4px 2px rgba(16, 19, 29, 0.1);
  border-radius: 50%;
  overflow: hidden;
  z-index: 1;

  img {
    width: 40px;
    height: 40px;
  }
  .txt {
    font-size: 20px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff !important;
  }
}

@-webkit-keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
.mask {
  position: fixed;
  z-index: 99;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);

  .box {
    width: 604px;
    height: 354px;
    background: #ffffff;
    border-radius: 16px 16px 16px 16px;
    opacity: 1;
    margin: 434px auto 0;
    display: flex;
    align-items: center;
    flex-direction: column;

    .tit {
      padding: 48px 0 24px;
      font-size: 32px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #20263a;
    }

    .inpt {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 524px;
      height: 88px;
      background: #f7f7f7;
      border-radius: 4px 4px 4px 4px;

      > input {
        padding: 24px 28px;
        width: 524px;
        height: 88px;
        font-size: 28px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #5b5f6f;
        border: none;
        color: #2a72ff;
      }
    }

    .placeholder-input {
      font-size: 28px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #9b9eac;
    }

    .btn {
      position: relative;
      display: flex;
      height: 100px;
      width: 100%;
      margin-top: 48px;

      &::before {
        content: ' ';
        width: 100%;
        height: 1px;
        background: #eee;
        position: absolute;
        top: 0;
        left: 0;
        transform: scaleY(0.5);
      }

      &::after {
        content: ' ';
        width: 1px;
        height: 100px;
        background: #eee;
        position: absolute;
        top: 0;
        left: 50%;
        transform: scaleX(0.5) translateX(-50%);
      }

      div {
        flex: 1;
        font-size: 32px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #20263a;
        line-height: 100px;
        text-align: center;
      }
    }
  }
}
// 模版样式
.sear-con {
  padding: 0 24px;

  .sear-con-h {
    position: relative;
    padding: 60px 0 32px;
    .sear-con-h-box {
      max-height: 216px;
      scroll-behavior: smooth;
      overflow-y: scroll;
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, and Opera */
      }
    }
    &-box {
      max-height: 216px;
      overflow: hidden;
    }

    &::before,
    &::after {
      content: ' ';
      width: 100%;
      height: 1px;
      background: #eee;
      position: absolute;
      left: 0;
      transform: scaleY(0.5);
    }

    &::before {
      top: 0;
    }

    &::after {
      bottom: 0;
    }

    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 80px;
      background: #f4f4f4;
      border-radius: 8px 8px 8px 8px;
      padding: 0 28px;
      margin-bottom: 28px;

      .item-l {
        font-size: 28px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #5b5f6f;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      .item-r {
        width: 40px;
        height: 40px;
      }

      &.active {
        background-color: rgba(42, 114, 255, 0.1) !important;
        .item-l {
          color: #2a72ff !important;
          font-weight: 600 !important;
        }
      }
    }
  }

  .sear-con-b {
    padding: 40px 24px 52px;

    .title {
      font-size: 28px;
      font-family: PingFang SC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
      padding-bottom: 24px;
    }

    .tagbox {
      height: 300px;
      overflow-y: scroll;
      scroll-behavior: smooth;

      .wrap {
        display: flex;
        flex-wrap: wrap;
        max-height: 100%;
      }

      .tag {
        padding: 10px 20px;
        margin-right: 20px;
        margin-bottom: 28px;
        text-align: left;
        border-radius: 4px 4px 4px 4px;
        border: 2px solid #2a72ff;
        color: #2a72ff;
        font-size: 26px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        max-height: 85px;
      }
    }
  }
}
.null {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 27px;
  color: #20263a;
  background-color: #fff;
  .img {
    width: 230px;
    height: 230px;
    margin-bottom: 24px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .text {
    font-size: 28px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: LEFT;
    color: #74798c;
  }
}
</style>
