<template>
  <div class="bg-grey h-ull">
    <van-sticky :offset-top="headerHeight + 'px'">
      <DropdownMenu :filterOptions="['所属地区', '所属行业']" @submit="handleSubmit" />
    </van-sticky>
    <div class="mt-24 bg-white">
      <PullDownList ref="pullListRef" :apiUrl="InvestmentListApi" :query-params="queryParams">
        <template #data="{ sourceData }">
          <div
            v-for="(item, index) in sourceData"
            :key="index"
            class="px-24 py-32 border-b border-[#eee]"
            :class="index === 0 ? 'border-t' : ''"
          >
            <div class="flex justify-between mb-16">
              <span class="text-primary text-28 text-font-semibold">{{ item.ent_name }}</span>
              <span
                class="flex items-center px-10 border rounded-4 text-24"
                :class="`${STATUS_MAP[item.ent_status] || STATUS_MAP.default}`"
              >
                {{ item.ent_status }}
              </span>
            </div>

            <div class="[&{p}]:text-secondary [&{p}]:mb-12 text-24 [&{span}]:text-tertiary">
              <p>
                法定代表人：<span>{{ item.fa_ren }}</span>
              </p>
              <div class="flex [&>p]:w-[50%] flex-wrap">
                <p>
                  投资数额：<span>{{ item.sub_con_am_usd }}万元</span>
                </p>
                <p>
                  持股比例：<span>{{ item.con_prop }}%</span>
                </p>
                <p class="mb-0">
                  成立日期：<span>{{ item.es_date }}</span>
                </p>
                <p class="mb-0">
                  投资时间：<span>{{ item.con_date }}</span>
                </p>
              </div>
            </div>
          </div>
        </template>
      </PullDownList>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { InvestmentListApi } from '@/api/qualityReport'
import { useRoute } from 'vue-router'
import PullDownList from '@/components/PullDownList/index.vue'
import DropdownMenu from '../dropdownMenu.vue'
import { useAppHeaderHeight } from '@/hooks/useAppHeaderHeight'

const headerHeight = useAppHeaderHeight()

const STATUS_MAP = {
  吊销: '!text-danger !border-danger',
  在营: '!text-[#26c8a7] !border-[#26c8a7]',
  default: '!text-danger !border-danger',
}

const route = useRoute()
const queryParams = computed(() => {
  return {
    ent_id: route.query.ent_id,
  }
})
const pullListRef = ref(null)

const handleSubmit = value => {
  const region = typeof value.region_code === 'string' ? [value.region_code] : value.region_code
  const nic = typeof value.nic_code === 'string' ? [value.nic_code] : value.nic_code
  pullListRef.value.onRefreshFn({
    ...queryParams.value,
    region: region?.join(',') || undefined,
    nic: nic?.join(',') || undefined,
  })
}
</script>

<style lang="scss" scoped>
:deep(.van-tab--active) {
  .van-tab__text {
    color: #2a72ff;
  }
}

:deep(.van-tabs__line) {
  background: linear-gradient(90deg, #2a72ff 0%, #3994ff 71%);
  width: 40px;
  height: 6px;
}

:deep(.van-search) {
  background-color: #f7f7f7;

  .van-search__action {
    margin-left: 24px;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 32px;
      background-color: #dedede;
    }
  }
}
</style>
