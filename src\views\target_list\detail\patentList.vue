<template>
  <div class="bg-grey">
    <van-sticky :offset-top="headerHeight + 'px'">
      <van-tabs v-model:active="active" :swipe-threshold="3" @change="handleChange">
        <van-tab :title="`${item.label}`" v-for="item in patentTypes"> </van-tab>
      </van-tabs>
    </van-sticky>
    <div class="mt-24 bg-white">
      <PullDownList ref="pullListRef" :apiUrl="entPatentApi" :query-params="queryParams">
        <template #data="{ sourceData }">
          <div
            v-for="(item, index) in sourceData"
            :key="index"
            class="py-32 px-24 border-[#eee]"
            :class="{ 'border-b': index !== sourceData.length }"
          >
            <div class="flex justify-between mb-16">
              <span class="font-semibold text-primary text-28">{{ item.patent_name }}</span>
              <!-- <span class="flex items-center px-10 border text-danger rounded-4 border-danger break-keep h-36 text-24">
                {{ item.type }}
              </span> -->
            </div>

            <p class="mb-12 text-24 text-secondary">
              专利号： <span class="text-tertiary">{{ item.patent_no }}</span>
            </p>
            <p class="mb-12 text-24 text-secondary">
              专利类型： <span class="text-tertiary">{{ item.type }}</span>
            </p>
            <p class="text-24 text-secondary">
              IPC分类： <span class="text-tertiary">{{ item.ipc_type }}</span>
            </p>
            <p class="mb-12 text-24 text-secondary">
              申请日期： <span class="text-tertiary">{{ item.release_date }}</span>
            </p>
          </div>
        </template>
      </PullDownList>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { getPatentCount, entPatentApi } from '@/api/qualityReport'
import { useRoute } from 'vue-router'
import PullDownList from '@/components/PullDownList/index.vue'
import { useAppHeaderHeight } from '@/hooks/useAppHeaderHeight'

const headerHeight = useAppHeaderHeight()
const route = useRoute()
const active = ref(0)
const queryParams = computed(() => {
  return {
    ent_id: route.query.ent_id,
    sort: { name: 'sqrq', order: 'DESC' },
    ptypes: active.value === 0 ? undefined : `${active.value}`,
  }
})
// const queryParams = ref({
//   ent_id: route.query.ent_id,
//   sort: { name: 'sqrq', order: 'DESC' },
// })
const pullListRef = ref(null)

const patentCount = ref([])
const patentTypes = computed(() => {
  return patentCount.value
    .filter(item => item.ptype !== '') // 去除其他类型
    .map((item, i, ary) => {
      return {
        label: `${item.ptype_name}（${item.count > 9999 ? '9999+' : item.count || 0}）`,
        value: item.ptype,
      }
    })
})

getPatentCount(route.query.ent_id).then(res => {
  patentCount.value = res?.datalist || []
})

const handleChange = value => {
  pullListRef.value.onRefreshFn({
    ...queryParams.value,
    ptypes: active.value === 0 ? undefined : `${active.value}`,
  })
}
</script>

<style lang="scss" scoped>
:deep(.van-tab--active) {
  .van-tab__text {
    color: #2a72ff;
  }
}

:deep(.van-tabs__line) {
  background: linear-gradient(90deg, #2a72ff 0%, #3994ff 71%);
  width: 40px;
  height: 6px;
}
</style>
