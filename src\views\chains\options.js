export const asideOptions = [
  {
    title: '成立年限',
    hideTitle: true,
    map: {
      register_time: '成立年限',
    },
  },
  {
    title: '注册资本',
    hideTitle: true,
    map: {
      register_capital: '注册资本',
    },
  },
  {
    title: '企业状态',
    hideTitle: true,
    map: {
      ent_status: '企业状态',
    },
  },
  {
    title: '企业类型',
    hideTitle: true,
    map: {
      ent_type: '企业类型',
    },
  },
  {
    title: '产业优选',
    map: {
      leading_ent: '龙头企业',
      expand_status: '疑似扩张',
      ent_scale: '企业规模',
      benefit_assess: '效益评估',
      technology_types: '科技型企业',
    },
  },
  {
    title: '融资信息',
    hideTitle: true,
    map: {
      financing_info: '融资信息',
    },
  },
  {
    title: '上市状态',
    hideTitle: true,
    map: {
      listed_status: '上市状态',
    },
  },
  {
    title: '专利信息',
    hideTitle: true,
    map: {
      super_dimension_patent_category: '专利信息',
    },
  },
  {
    title: '失信信息',
    hideTitle: true,
    map: {
      untrustworthy_info_data: '失信信息',
    },
  },
  {
    title: '裁判文书',
    hideTitle: true,
    map: {
      judgment_doc_data: '裁判文书',
    },
  },
  {
    title: '行政处罚',
    hideTitle: true,
    map: {
      adminstrative_penalties_data: '行政处罚',
    },
  },
  {
    title: '动产抵押',
    hideTitle: true,
    map: {
      chattel_mortage_data: '动产抵押',
    },
  },
  {
    title: '经营异常',
    hideTitle: true,
    map: {
      abnormal_operation_data: '经营异常',
    },
  },
]
