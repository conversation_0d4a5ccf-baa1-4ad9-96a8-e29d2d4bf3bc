<template>
  <div class="GridCard-component">
    <div v-if="title" class="GridCard-title" :style="titleStyle">
      <span>{{ title }}</span>
    </div>
    <VanGrid :border="false" :column-num="columnNum">
      <VanGridItem
        v-for="(item, index) in items"
        :key="index"
        :text="item.text"
        :dot="item.dot"
        :badge="item.badge"
        :url="item.url"
        :to="item.to"
      >
        <template #icon>
          <SvgIcon v-if="item.svg" :name="item.svg"></SvgIcon>
          <VanIcon v-else-if="item.icon" :name="item.icon"></VanIcon>
          <VanIcon v-else name="photo-o"></VanIcon>
        </template>
      </VanGridItem>
    </VanGrid>
  </div>
</template>
<script setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
  titleStyle: {
    type: Object,
    default() {
      return {}
    },
  },
  columnNum: {
    type: Number,
    default: 4,
  },
  items: {
    type: Object,
    default() {
      return {}
    },
  },
})
</script>
<style lang="scss" scoped>
.GridCard-component {
  overflow: hidden;
  margin: 5px 20px 5px 20px;
  border-radius: 10px;
  background-color: var(--van-grid-item-content-background-color);
  .GridCard-title {
    margin: 15px 15px 0 20px;
    span {
      font-size: 18px;
      font-weight: 500;
    }
  }
}
</style>
