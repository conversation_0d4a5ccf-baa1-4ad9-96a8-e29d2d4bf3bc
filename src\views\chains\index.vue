<template>
  <div class="w-full h-full h_container bg-grey">
    <van-sticky :offset-top="headerHeight + 'px'">
      <div class="p-20 bg-white">
        <van-search
          v-model="keyword"
          class="!p-0 w-full"
          :maxlength="16"
          :show-action="!!keyword"
          :clear-trigger="keyword ? 'always' : 'focus'"
          placeholder="请输入产业链名称关键词"
          @input="
            () => {
              delayFn()
            }
          "
          @clear="
            () => {
              emptyflag = true
              keyword = ''
              filterDataSource = []
            }
          "
          @cancel="() => ((keyword = ''), (filterDataSource = []))"
        />
      </div>
    </van-sticky>

    <div v-show="keyword" class="bg-[#f7f7f7] w-full" :class="`${!filterDataSource.length ? 'h-full' : ''}`">
      <div v-if="!filterDataSource.length && emptyflag" class="flex flex-col items-center justify-center w-full h-full">
        <SvgIcon name="empty-data" class="w-[230px] h-[230px] mb-24" />
        <p class="text-secondary text-28">暂未检索到相关信息</p>
      </div>

      <div v-else class="pb-40 bg-white">
        <div
          v-for="(item, i) in filterDataSource"
          :key="item.id"
          class="flex flex-wrap items-center px-24 bg-white"
          @click="handleClickItem(item)"
        >
          <div :key="item.chain_code" class="py-24 w-full flex flex-wrap items-center text-28 border-b border-[#eee]">
            <span class="flex items-center break-keep">
              <vanHighlight :keywords="keyword" highlight-class="text-danger" :source-string="item.name"></vanHighlight>
              <!-- <span class="px-8" v-if="i < item.length - 1">{{ '>' }}</span> -->
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- <div v-show="!keyword" class="mt-24 bg-white">
      <div class="p-24 text-primary text-32 font-semibold border-b border-[#f7f7f7]">场景选择</div>

      <div class="border-b border-[#f7f7f7] flex justify-around">
        <div
          v-for="(type, index) in types"
          :key="type"
          :class="`${currentType === index ? 'font-semibold text-selected selected-type' : ''}`"
          class="p-24 text-28 text-secondary"
          @click="currentType = index"
        >
          {{ type }}
        </div>
      </div>
    </div> -->

    <div v-show="!keyword">
      <div
        class="px-24 main-item border-b border-[#eee] bg-white pt-32 pb-16"
        v-for="item in supplementaryData"
        :key="item.id"
      >
        <!-- <div class="mb-24">
          <span class="font-semibold text-28 text-primary">{{ item.name }}</span>
        </div> -->
        <div :class="`${!item.children ? 'flex flex-wrap' : ''}`">
          <template v-for="(child, index) in item.children ?? item.children_nodes" :key="child.id">
            <div :class="item.children ? '' : 'w-[50%]'">
              <div>
                <!-- <p v-if="child.subtitle" class="mb-24 text-selected">{{ child.subtitle }}</p> -->
                <div class="flex flex-wrap mb-8">
                  <div
                    v-for="(itm, i) in child.children_nodes ?? [child]"
                    class="flex justify-center flex-grow p-12 mb-16 text-center bg-grey text-tertiary text-26"
                    :class="`${i % 2 === 0 ? 'mr-16' : ''} ${item.children ? 'w-[48%]' : 'w-full'}`"
                    :style="{ cursor: item.lock ? 'no-drop' : '' }"
                    @click="handleClickChain(itm)"
                  >
                    {{ itm.name }}
                    <SvgIcon v-if="!itm.available" name="locked" class="w-32 h-32 ml-4" />
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getChainSearch } from '@/api/chains'
import { getSupplementaryData } from '@/api/home'
import { debounce, unique, pageChangeHandler } from '@/utils'
import { useAppHeaderHeight } from '@/hooks/useAppHeaderHeight'
import { commonChainMenuLists } from '@/contant/dict.js'

const router = useRouter()
const { proxy } = getCurrentInstance()
const headerHeight = useAppHeaderHeight

const filterDataSource = ref([])
const keyword = ref('')
const emptyflag = ref(true)
const types = ['不限', '强链', '补链']
const currentType = ref(0)

const handleClickItem = item => {
  pageChangeHandler('/chain-list', {
    code: item.chain_code,
    name: item.name,
    level: item.level,
  })
}

const industrySearch = () => {
  if (!keyword.value) {
    filterDataSource.value = []
    return
  }
  getChainSearch(keyword.value).then(res => {
    filterDataSource.value = unique(
      res?.flat?.()?.filter(item => item.name.includes(keyword.value)) || [],
      'chain_code'
    )
  })
}

const delayFn = debounce(industrySearch, 500)

const supplementaryData = ref([])

// proxy.$loading('加载中...')
// getSupplementaryData()
//   .then(res => {
//     ;(res || []).forEach(item => {
//       if (!item.subtitle) {
//         supplementaryData.value.push(item)
//       } else {
//         const find = supplementaryData.value.find(_ => _.name === item.name)
//         if (find?.name) {
//           find.children.push(item)
//         } else {
//           supplementaryData.value.push({
//             ...item,
//             children: [item],
//           })
//         }
//       }
//     })
//   })
//   .finally(() => {
//     proxy.$close()
//   })

onMounted(() => {
  supplementaryData.value = [
    {
      name: 'name',
      code: 'code1',
      deleted: false,
      id: 1000,
      children_nodes: commonChainMenuLists.map(item => ({
        name: item.label,
        code: item.value,
        available: true,
        id: item.value,
        deleted: false,
        parent_id: 1000,
      })),
    },
  ]
  console.log('2222', supplementaryData.value)
})

const handleClickChain = item => {
  if (!item.available) return
  pageChangeHandler('/chainDetail', {
    code: item.code,
  })
}
</script>

<style lang="scss" scoped>
.selected-type {
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 6px;
    background: linear-gradient(90deg, #2a72ff 0%, #3994ff 71%);
  }
}

:deep(.van-search) {
  background-color: #f7f7f7;

  .van-search__action {
    margin-left: 24px;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 32px;
      background-color: #dedede;
    }
  }
}
</style>
