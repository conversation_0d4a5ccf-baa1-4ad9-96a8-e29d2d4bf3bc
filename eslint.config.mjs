import globals from 'globals'
// 预定义配置
import pluginJs from '@eslint/js'
import pluginVue from 'eslint-plugin-vue'

import commpnParser from 'vue-eslint-parser'
import prettier from 'eslint-plugin-prettier'

// "@babel/eslint-parser";

// import customConfig from "./esconfig/custom_config.js";

export default [
  // languageOptions：配置如何检查 js 代码
  {
    // 1.1 处理 与 JavaScript 相关的配置项
    // - ecmaVersion
    // - sourceType
    // - globals
    // - parser
    // - parserOptions
    // files: ["**/*.ts", "**/*.vue"],
    // ignores: ["**/*.config.js"],
    ignores: ['**/*.config.js', 'dist/**', 'node_modules/**', '!**/eslint.config.js'],
    languageOptions: {
      // 1.11 定义可用的全局变量
      globals: globals.browser,
      // 1.12 扩展
      // ecmaVersion: "latest",
      // sourceType: "module",
      parser: commpnParser,
      parserOptions: {},
    },
  },
  // 原来的extends替换为如下模式
  pluginJs.configs.recommended,
  ...pluginVue.configs['flat/essential'],
  {
    plugins: {
      prettier, // 代码风格
    },
    rules: {
      // 开启这条规则后，会将prettier的校验规则传递给eslint，这样eslint就可以按照prettier的方式来进行代码格式的校验
      'prettier/prettier': 'off',
      // eslint（https://eslint.bootcss.com/docs/rules/）
      'no-var': 'error', // 要求使用 let 或 const 而不是 var
      'no-multiple-empty-lines': 'off', // 不允许多个空行
      // eslint-disable-next-line no-undef
      'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
      // eslint-disable-next-line no-undef
      'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
      'no-unexpected-multiline': 'off', // 禁止空余的多行
      'no-useless-escape': 'off', // 禁止不必要的转义字符

      // eslint-plugin-vue (https://eslint.vuejs.org/rules/)
      'vue/multi-word-component-names': 'off', // 要求组件名称始终为 “-” 链接的单词
      'vue/script-setup-uses-vars': 'error', // 防止<script setup>使用的变量<template>被标记为未使用
      'vue/no-mutating-props': 'off', // 不允许组件 prop的改变
      'vue/attribute-hyphenation': 'off', // 对模板中的自定义组件强制执行属性命名样式
      // 禁用对以"_"开头命名的变量进行校验
      'no-underscore-dangle': 'off',
    },
  },
  {
    plugins: ['auto-import'],
    rules: {
      'auto-import/auto-import': [
        2,
        {
          rootPath: './src',
          packages: {
            d3: 'd3',
            bloodhound: 'Bloodhound',
            moment: 'moment',
            alkali: {
              hasExports: 'module-path/to/alkali',
            },
            dgrid: {
              modulesIn: './bower_components/dgrid',
            },
            dstore: {
              modulesIn: './bower_components/dstore',
            },
          },
        },
      ],
    },
  },
]
