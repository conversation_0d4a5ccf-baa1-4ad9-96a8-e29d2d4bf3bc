{"name": "hd-h5", "version": "0.0.1", "scripts": {"dev": "vite --mode development --no-open", "dev:test": "vite  --mode test --no-open", "build": "vite build --mode production", "build:test": "vite build --mode test", "build:zg": "vite build --mode zg", "preview": "vite preview --port 7777", "lint": "eslint src/**/*.{js,vue}", "lint:fix": "npm run lint --fix", "prepare": "husky install"}, "lint-staged": {"src/**/*.{js,json,vue}": ["eslint --fix"]}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/hierarchy": "^0.6.8", "@antv/x6": "^1.32.11", "@vant/use": "^1.6.0", "@vueuse/core": "^10.0.2", "axios": "^1.3.4", "better-scroll": "2.5.1", "crypto-js": "^4.1.1", "d3": "5.16.0", "dayjs": "^1.11.11", "depcheck": "^1.4.7", "echarts": "^5.4.2", "mitt": "^3.0.1", "pinia": "^2.0.33", "pinia-plugin-persist": "^1.0.0", "postcss-pxtorem": "^6.0.0", "rough-notation": "^0.5.1", "uuid": "^9.0.0", "vant": "^4.9.0", "vue-clipboard3": "^2.0.0", "vue-demi": "latest", "vue-router": "^4.1.6"}, "devDependencies": {"@babel/core": "^7.21.4", "@babel/eslint-parser": "^7.21.3", "@babel/preset-env": "^7.24.7", "@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "@eslint/js": "^9.5.0", "@vitejs/plugin-legacy": "^4.0.2", "@vitejs/plugin-vue": "^4.1.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "autoprefixer": "^10.4.19", "crypto.js": "^3.1.0", "esbuild": "^0.17.18", "eslint": "^9.5.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.26.0", "globals": "^15.6.0", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "^13.2.0", "moment": "^2.30.1", "path": "^0.12.7", "postcss": "^8.4.38", "postcss-px-to-viewport": "^1.1.1", "prettier": "^2.8.7", "sass": "1.54", "sass-loader": "^13.3.3", "svg-preview-plugin": "^0.6.0", "svg-sprite-loader": "^6.0.11", "tailwindcss": "^3.4.4", "terser": "^5.16.8", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.24.1", "vconsole": "^3.15.1", "vite": "^4.2.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-qiankun": "^1.0.15", "vite-plugin-vconsole": "^1.3.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "^3.2.47", "vue-clipboard3": "^2.0.0", "vue-wechat-title": "^2.0.7"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^2.0.0 || >=3.0.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}, "resolutions": {"cheerio": "1.0.0-rc.12"}}