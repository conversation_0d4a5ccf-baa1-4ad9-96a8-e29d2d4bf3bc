<template>
  <div class="w-full h-full" ref="mapRef" />
</template>

<script setup>
import { useIndexMap, useListMap } from '../hook/useMap'

const props = defineProps({
  simple: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['update:modelValue', 'update:postion'])
const map = ref(null)
const mapRef = ref(null)
const data = reactive({
  geolocation: null,
  centerMarker: null,
  geocoder: null, //获取地理位置根据坐标
  mouseTool: '', // 画图
  isPathTool: false, //绘制图形 Flag
  graphTool: null, //绘制的实例
  radius: 100, // 当前绘制的圆半径
  circle: null, // 圆实例
  infoWindow: null,
  type: 'entAry',
})
const { addCenterMarker } = useIndexMap({ emits, map, data })
const { init } = useListMap({ emits, map, data })

const beforeInitMap = callback => {
  if (!map.value) {
    map.value = new AMap.Map(mapRef.value, {
      resizeEnable: false, // 是否监控地图容器尺寸变化
      expandZoomRange: true,
      zoom: 14,
      zooms: [6, 26],
    })

    // 定位
    const controlBar = new AMap.ControlBar({
      position: {
        top: '-10px',
        right: '-10px',
      },
    })
    map.value.addControl(controlBar)
    data.geocoder = new AMap.Geocoder()
    data.mouseTool = new AMap.MouseTool(map.value) // 画图工具
  }
  callback && callback()
}
const initMap = code => {
  beforeInitMap(() => {
    addCenterMarker(code)
  })
}

const reFreshMap = val => {
  beforeInitMap(() => {
    if (!val || !Object.keys(val).length) return
    let { radius, center, type, markers } = val
    if (radius > 1000) {
      map.value.setZoom(12)
    } else if (radius >= 9000) {
      map.value.setZoom(10)
    } else {
      map.value.setZoom(14)
    }
    data.radius = radius || 100
    data.type = type
    data.center = center
    nextTick(() => {
      if (!map.value) {
        return
      }
      init({ newData: { center, markers } })
    })
  })
}
const reFreshAreaMap = ({ code, center }) => {
  beforeInitMap(() => {
    init({ adcode: code, newData: { center } })
  })
}
// 在组件的 onBeforeMount 钩子中销毁地图对象
onBeforeMount(() => {
  if (map.value) {
    map.value?.destroy() // 销毁地图对象
    map.value = null // 重置地图对象为 null
  }
})
defineExpose({
  reFreshMap,
  reFreshAreaMap,
  initMap,
})
</script>

<style>
.amap-copyright {
  opacity: 0;
}
.amap-logo {
  opacity: 0;
}
.amap-luopan {
  transform: scale(0.5);
}
.amap-info-content {
  padding: 12px 24px !important;
}
.amap-info-close {
  display: none;
}
.amap-info-sharp {
  opacity: 0;
}
</style>
