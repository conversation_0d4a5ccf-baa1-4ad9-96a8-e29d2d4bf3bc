<template>
  <div class="w-full h-full flex flex-col bg-[#f7f7f7]">
    <div class="flex-shrink-0 relative">
      <div class="h-[90px] flex items-center text-#74798C px-[24px] bg-[#F7F7F7] justify-between">
        <div class="text-[28px]">
          共<u class="text-#3E7BFA px-[8px] font-semibold">{{ total }}</u
          >家企业入住
        </div>
        <div class="w-170">
          <van-dropdown-menu active-color="#3E7BFA" :z-index="170" ref="dropRef">
            <!-- 更多筛选 -->
            <van-dropdown-item
              title="更多筛选"
              :title-class="filterData.hunH ? 'activeMenuItem' : ''"
              @open="handleClick('huntOpen')"
            >
              <template #default>
                <div>
                  <div>
                    <Hunt
                      :asideList="asideOptionsThree"
                      height="360px"
                      ref="huntRef"
                      @submit="val => handleClick('hunt', val)"
                    ></Hunt>
                  </div>
                  <div class="flex justify-between px-24 py-10 bg-white">
                    <div class="w-[340px] mr-22 cancel-btn" @click="handleClick('huntClose')">取消</div>
                    <div class="w-[340px] confirm-btn" @click="handleClick('huntSure')">确定</div>
                  </div>
                </div>
              </template>
            </van-dropdown-item>
          </van-dropdown-menu>
        </div>
      </div>
    </div>
    <!-- 列表 -->
    <div
      class="h-full overflow-y-scroll scroll-smooth"
      ref="listRef"
      style="scrollbar-width: none; -ms-overflow-style: none"
    >
      <PullDownList
        ref="pullListRef"
        apiUrl="/hdzhzs/business_admin/v0.1/maps/zi_ding_yi"
        :queryParams="queryParams"
        :requestAuto="false"
        :callback="callback"
        requestType="page_index"
      >
        <template #data="{ sourceData }">
          <!--根据传来的距离 动态计算出距离 -->
          <EntCard
            type="noThreeAddCollect"
            :entList="sourceData"
            :query="highlightQuery"
            :openNow="false"
            :special="release"
          />
        </template>
      </PullDownList>
    </div>
  </div>
</template>

<script name="ExcavateList" setup>
import PullDownList from '@/components/PullDownList/index.vue'
import EntCard from '@/components/EntCard/index.vue'
import Hunt from '@/components/Hunt/index.vue'
import { asideOptionsThree } from '../component/filterOptions.js'
import { openReport } from '@/utils'

const emits = defineEmits(['clickItem'])
const props = defineProps({
  isPage: {
    type: Boolean,
    default: true,
  },
  params: {
    type: Object,
    default: () => ({}),
  },
  isRelease: {
    type: Boolean,
    default: false,
  },
})
const filterData = reactive({
  tempHunt: {},
  sureHunt: {}, //更多筛选确定数据-回填
  hunH: false, //高亮
})

const route = useRoute()
const highlightQuery = ref('')
const queryParams = ref({})
const pullListRef = ref(null)
const total = ref(0)
const grids = ref([0, 0])
const dropRef = ref(null)
const huntRef = ref(null)

const release = computed(() => {
  return props.isRelease
})
// 计算距离 km
const GetDistance = (lat1, lng1, lat2, lng2) => {
  const radLat1 = (lat1 * Math.PI) / 180.0
  const radLat2 = (lat2 * Math.PI) / 180.0
  const a = radLat1 - radLat2
  const b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0
  let s =
    2 *
    Math.asin(
      Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2))
    )
  s = s * 6378.137 // EARTH_RADIUS;
  s = Math.round(s * 10000) / 10000
  return s
}
const callback = source => {
  let lng, lat
  lng = grids.value[0]
  lat = grids.value[1]
  if (!lng || !lat) return source
  source.forEach(item => {
    const distance = GetDistance(item.location.lat, item.location.lon, lat, lng)
    item.distance = (+distance).toFixed(2)
  })
  return source
}
const handleClick = async (type, value) => {
  switch (type) {
    // 更多筛选
    case 'hunt':
      filterData.tempHunt = JSON.parse(JSON.stringify(value))
      break
    case 'huntClose':
      dropRef.value?.close()
      break
    case 'huntOpen':
      // 回填数据
      huntRef.value?.setBackfillData(filterData.sureHunt)
      break
    case 'huntSure':
      filterData.sureHunt = { ...filterData.tempHunt.paramsData }
      filterData.hunH = filterData.tempHunt.isHeight
      if (filterData.tempHunt?.paramsData?.eleseic_data) {
        delete filterData.tempHunt.paramsData.eleseic_data
      }
      if (filterData.tempHunt?.paramsData?.chain_codes_data) {
        delete filterData.tempHunt.paramsData.chain_codes_data
      }
      queryParams.value = { mid: queryParams.value.mid, ...filterData.tempHunt.paramsData }
      pullListRef.value?.onRefreshFn(queryParams.value)
      dropRef.value?.close()
      break

    default:
      break
  }
}

onMounted(() => {
  const { mid, lat, lon } = route.query
  queryParams.value = { mid: [mid] }
  grids.value = [lon, lat]
  nextTick(() => {
    pullListRef.value?.onRefreshFn()
  })
})
watch(
  () => pullListRef.value?.total,
  val => {
    total.value = val || 0 // 这里0也是要的
  }
)
</script>

<style lang="scss" scoped>
:deep(.noData) {
  justify-content: flex-start;
  background: white !important;
  height: 100% !important;
  padding-top: 60px;
}

:deep(.van-dropdown-menu__bar) {
  height: 88px;
  overflow: hidden;
  box-shadow: none;
  // border-bottom: 1px solid #eee;
  .van-dropdown-menu__item {
    justify-content: flex-start;
    background: #f7f7f7 !important;
    .van-dropdown-menu__title {
      font-size: 28px !important;
      color: #525665;
    }
  }
}
:deep(.activeMenuItem) {
  color: #3e7bfa !important;
  span {
    color: #3e7bfa !important;
  }
}
:deep(.activeMenuItem:after) {
  border-color: transparent transparent currentColor currentColor;
}
// 处理更多筛选样式
:deep(.yxbglt1) {
  border: none !important;
}
</style>
