<template>
  <span v-if="query && switcher">
    <!-- 此代码格式不能随意变动, 额外的换行会导致文本之间出现空格 -->
    <span v-for="(item, index) in text" :key="index" :class="query.includes(item) ? 'light' : 'text'">{{ item }}</span>
  </span>
  <span v-else>{{ text }}</span>
</template>

<script setup>
/**
 * 高亮文本
 * 通过以query来拆分字符串然后拼接实现.
 */
defineProps({
  query: {
    type: String,
    default: undefined,
  },
  hightlightClass: {
    type: String,
    default: 'light',
  },
  hightlightStyle: {
    type: Object,
    default: () => ({}),
  },
  switcher: {
    type: Boolean,
    default: true,
  },
  text: {
    type: String,
    default: undefined,
  },
})
</script>

<style lang="scss" scoped>
.text {
  font-size: 32px;
  font-weight: 600;
  color: #20263a;
}
.light {
  font-size: 32px;
  font-weight: 600;
  color: #2a72ff;
}
</style>
