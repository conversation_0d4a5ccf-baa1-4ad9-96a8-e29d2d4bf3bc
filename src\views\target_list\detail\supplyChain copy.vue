<template>
  <div class="mt-24">
    <div v-if="!noData" class="graph-container">
      <div id="supply-chain-relation"></div>
    </div>
    <div v-else class="flex items-center justify-center bg-white h-500 text-secondary">
      <!-- <Empty title="暂无供应链关系" icon-style="font-size: 280px; height: 166px" /> -->
      暂无供应链关系
    </div>
    <!-- <div
      class="fixed flex items-center justify-center mx-24 text-white w-702 bg-confirm rounded-8 bottom-80 h-80"
      @click="handleFullScreen"
    >
      横屏查看
    </div> -->
  </div>
</template>

<script>
// import Empty from '@/views/home/<USER>/components/Empty/index.vue'
import { getEntSupplyChains } from '@/api/qualityReport'
import { Graph } from '@antv/x6'
import Hierarchy from '@antv/hierarchy'
import './x6'
import { setPx } from '@/utils/height'
import { showToast } from 'vant'
import { pageChangeHandler } from '@/utils'

const RECT_SIZE = {
  width: 200,
  height: 36,
}

export default {
  // components: { Empty },
  data() {
    return {
      supplyChain: {
        top: [],
        bottom: [],
      },
      top: {
        count: 0,
        datalist: [],
      },
      bottom: {
        count: 0,
        datalist: [],
      },
      noData: undefined,
      graph: null,
      container: null,
    }
  },
  async created() {
    await this.loadData()
  },
  methods: {
    async loadData() {
      try {
        const res = await Promise.all([this._getEntSupplyChains(1), this._getEntSupplyChains(2)])
        this.noData = res.every(item => item.datalist.length === 0)
        if (!this.noData) {
          this.$nextTick(() => {
            this.renderGraph()
          })
        }
      } catch (error) {
        console.error('数据加载出错:', error)
      }
    },
    async _getEntSupplyChains(type) {
      const key = type === 1 ? 'top' : 'bottom'
      return getEntSupplyChains({
        page_size: 10,
        page_index: Math.ceil((this[key].datalist.length || 0) / 10) + 1,
        type,
        ent_id: this.$route.query.ent_id,
      }).then(res => {
        res.datalist.forEach(item => {
          item.direction = type === 1 ? 'left' : 'right'
          item.type = 'topic-child'
          item.id = item.ent_id + parseInt(Math.random() * 10 ** 10)
          item.label = item.ent_name
          Object.assign(item, RECT_SIZE)
        })
        this.supplyChain[key] = [...this.supplyChain[key], ...res.datalist]
        this[key].count = res.count
        this[key].datalist = [...this[key].datalist, ...res.datalist]
        return res
      })
    },
    async handleMore(moreCount, type) {
      if (!moreCount) {
        this.$message.warning('没有更多了')
        return
      }
      await this._getEntSupplyChains(type)
      this.renderGraph()
    },
    renderGraph() {
      if (this.graph) {
        this.graph.dispose()
        this.graph = null
      }
      this.container = document.getElementById('supply-chain-relation')
      this.graph = new Graph({
        container: this.container,
        autoResize: true,
        // virtual: true,
        // width: setPx(750),
        // height: setPx(1000),
        // scroller: {
        //   enabled: true,
        //   pageVisible: true,
        //   pageBreak: false,
        //   pannable: true,
        // },
        background: {
          color: '#fff',
        },
        // minimap: {
        //   enabled: true,
        //   container: document.getElementById('minimap-container'),
        //   height: 160
        // },
        // mousewheel: {
        //   enabled: true,
        // },
        panning: {
          enabled: true,
        },
        connecting: {
          connectionPoint: 'anchor',
          router: {
            name: 'manhattan',
          },
        },
        interacting: false,
      })

      this.graph.on('click:more', ({ node }) => {
        const data = node.data || {}

        this.handleMore(data.count, data.chainType)
      })

      const topMoreCount = this.top.count - this.top.datalist.length > 0 ? this.top.count - this.top.datalist.length : 0
      const bottomMoreCount =
        this.bottom.count - this.bottom.datalist.length > 0 ? this.bottom.count - this.bottom.datalist.length : 0
      const topMore = {
        ...RECT_SIZE,
        type: 'more-left',
        direction: 'left',
        id: 'more-left',
        label: `更多(${topMoreCount})`,
        count: topMoreCount,
        chainType: 1,
      }

      const bottomMore = {
        ...RECT_SIZE,
        type: 'more-right',
        direction: 'right',
        id: 'more-right',
        label: `更多(${bottomMoreCount})`,
        count: bottomMoreCount,
        chainType: 2,
      }

      const _data = {
        label: this.$route.query.ent_name,
        id: this.$route.query.ent_id,
        type: 'topic',
        height: 42,
        width: 250,
        children: [...this.top.datalist, ...this.bottom.datalist],
      }

      topMoreCount && _data.children.push(topMore)
      bottomMoreCount && _data.children.push(bottomMore)

      const render = () => {
        const result = Hierarchy.mindmap(_data, {
          direction: 'H',

          getHGap() {
            return 56
          },
          getVGap() {
            return 12
          },
          getSide: e => {
            return e.data.direction
          },
        })

        const cells = []
        const traverse = (hierarchyItem, item) => {
          if (hierarchyItem) {
            const { data, children } = hierarchyItem
            if (item) {
              let node = null
              node = this.graph.addNode({
                inherit: 'rect',
                id: data.id,
                x: hierarchyItem.x,
                y: hierarchyItem.y,
                data: item,
                markup: [
                  {
                    tagName: 'rect',
                    selector: 'body1',
                    attrs: {
                      width: 200,
                      height: 36,
                    },
                  },
                  {
                    tagName: 'foreignObject',
                    selector: 'wrapper1',
                    textContent: data.label,
                    title: data.label,
                    style: {
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      padding: '0 5px',
                      lineHeight: '36px',
                      color: '#20263A',
                      fontSize: '14px',
                      textAlign: 'center',
                    },
                  },
                  {
                    tagName: 'title',
                    selector: 'title',
                    textContent: data.label,
                  },
                ],
                attrs: {
                  body1: {
                    rx: 3,
                    ry: 3,
                    fill: '#E7ECF5',
                    strokeWidth: 0,
                  },
                  wrapper1: {
                    width: '200px',
                    height: '36px',
                    fill: '#C9EAFF',
                    strokeWidth: 0,
                  },
                },
              })

              if (item.type.includes('more')) {
                node = this.graph.createNode({
                  id: data.id,
                  shape: data.type,
                  x: hierarchyItem.x,
                  y: hierarchyItem.y,
                  width: data.width,
                  height: data.height,
                  label: data.label,
                  type: data.type,
                  data: item,
                })
              }

              cells.push(node)
            } else {
              cells.push(
                this.graph.createNode({
                  id: data.id,
                  shape: data.type,
                  x: hierarchyItem.x,
                  y: hierarchyItem.y,
                  width: data.width,
                  height: data.height,
                  label: data.label,
                  type: data.type,
                  data: item,
                })
              )
            }

            if (children) {
              children.forEach(item => {
                const { id, data } = item

                cells.push(
                  this.graph.createEdge({
                    shape: 'mindmap-edge',
                    source: {
                      cell: hierarchyItem.id,
                    },
                    target: {
                      cell: id,
                    },
                  })
                )
                traverse(item, data)
              })
            }
          }
        }
        traverse(result)

        this.graph.resetCells(cells)
        this.graph.centerContent()
        this.graph.zoomToFit({ maxScale: 1 })
      }

      render()

      window.addEventListener('resize', () => {
        setTimeout(() => {
          render()
        })
      })

      this.handleScale()
    },
    handleScale() {
      let startDistance = 0
      const touchStart = e => {
        if (e.touches.length === 2) {
          startDistance = Math.hypot(e.touches[0].pageX - e.touches[1].pageX, e.touches[0].pageY - e.touches[1].pageY)
        }
      }

      const touchMove = e => {
        if (e.touches.length === 2) {
          const distance = Math.hypot(e.touches[0].pageX - e.touches[1].pageX, e.touches[0].pageY - e.touches[1].pageY)
          // 计算缩放比例
          const scale = distance / startDistance

          this.graph.zoomTo(scale)
        }
      }

      // 绑定事件到DOM元素
      this.container.addEventListener('touchstart', touchStart)
      this.container.addEventListener('touchmove', touchMove)
    },
    zoomIn() {
      const zoom = this.graph.zoom()
      this.graph.zoomTo(zoom + 0.2)
    },
    zoomOut() {
      const zoom = this.graph.zoom()
      this.graph.zoomTo(zoom - 0.2)
    },
    center() {
      this.graph?.centerContent?.()
    },
    handleFullScreen() {
      pageChangeHandler('/supply-chain/fullscreen', this.$route.query)
    },
  },
  computed: {},
  watch: {},
}
</script>

<style lang="less" scoped>
.graph-container {
  // position: relative;
  width: 100%;
  height: 100%;
}

#supply-chain-relation {
  height: 1000px;
}

#minimap-container {
  position: absolute;
  right: 0;
  top: 0;
  margin-right: 12px;
  width: 300px;
}

.intelligence-monitor-no-data-container {
  height: 500px;
}

.supply-chain-wrapper {
  // padding: 30px;
}

.toolbar {
  & > div {
    height: 60px;
    width: 120px;
    border: 2px solid #dedede;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    color: #525665;
    font-size: 32px;
    border-radius: 8px 8px 8px 8px;
  }
}
</style>
