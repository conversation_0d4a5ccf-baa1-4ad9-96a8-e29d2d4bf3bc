export function getTagBackGround(tagArr = []) {
  if (!tagArr.length) return []
  const tagColorList = {
    darkBlue: ['世界500强', '中国500强'],
    wathetBlue: ['央企', '地方重点国企', '央企一级子公司', '央企二级子公司'],
    yellow: ['纳税A级企业', '纳税B级企业'],
    green: ['5A旅行社'],
    purple: [
      '国家高新技术企业',
      '中关村高新技术企业',
      '科技型中小企业',
      '国家高新技术企业(过期)',
      '技术先进型服务企业',
      '瞪羚企业',
      '牛羚企业',
      '独角兽企业',
      '众创空间',
      '科技企业孵化器',
      '企业技术中心',
      '科技小巨人',
      '专精特新企业',
      '雏鹰企业',
      '专精特新小巨人企业',
      '技术创新示范企业',
    ],
    brown: ['工信部制造业单项冠军'],
    onBusiness: ['在营', '临时', '待迁入'],
    comColor: ['企业', '组织机构', '个体', '全球企业'],
    miniCom: ['微型', '大型', '小型', '中型'],
    logOff: [
      '注销',
      '吊销',
      '迁出',
      '撤销',
      '清算中',
      '停业',
      '拟注销',
      '非正常户',
      '吊销未注销',
      '吊销已注销',
      '正在注销',
      '经营期限届满',
      '其他',
    ],
  }
  tagArr = tagArr.map(tag => {
    const tagObj = {
      tagName: tag,
      tagColor: '',
    }

    // 给不同tag添加不同颜色
    for (const color in tagColorList) {
      if (tagColorList[color].includes(tag)) {
        tagObj.tagColor = color
        break
      }
      // 21.10.9补充 如果没有找到就全部粉色 --后面确实是不是这样弄
      tagObj.tagColor = 'customColor'
    }
    return tagObj
  })
  return tagArr
}
// 删除节点
export function deleteChild(cla) {
  var _element = document.querySelector(cla)
  var _parentElement = _element?.parentNode
  if (_parentElement) {
    _parentElement.removeChild(_element)
  }
}
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}
