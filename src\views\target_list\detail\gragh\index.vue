<template>
  <div :class="fullscreen ? 'upandown-page' : ''">
    <div class="tooltipUp"></div>
    <div id="treeRootUp" v-if="flag" class="treeRoot-up"></div>
    <div class="noData" v-else>
      <!-- 缺省页 -->
      <!-- <img src="@/assets/image/null.png" alt="" /> -->
      <div>未查到相关信息!</div>
    </div>
    <Toolbar v-if="fullscreen" downloadName="上下游关系" downloadClass="treeRoot-up" @refresh="getData" />
  </div>
</template>

<script>
import Toolbar from '@/views/supply_chain/child/atlasCompoent/Toolbar.vue'
import * as d3 from 'd3'

import { useRoute } from 'vue-router'
import { getTagBackGround } from '@/utils/contant'
import { deleteChild, setPx, handleCss, usePortrait } from '@/views/supply_chain/child/atlasCompoent/utils.js'
import { getEntSupplyChains } from '@/api/qualityReport'

import { debounce } from '@/utils/index'

const setTags = item => {
  item.tags = []
  item.is_faucet && item.tags.push('龙头企业')
  item.is_market && item.tags.push('上市企业')
  item.is_local && item.tags.push('本地企业')
  return item
}

var getEntListFn // 获取企业列表方法 根据传进来的参数调不同的
var circlewidth1 = 0,
  circlewidth4 = 0
var margin1 = { top: 40, right: 0, bottom: 0, left: 90 }

export default {
  name: 'UpDownRelation',
  props: {
    fullscreen: {
      type: Boolean,
      default: true,
    },
    width: {
      type: [Number, String],
      default: undefined
    },
    height: {
      type: [Number, String],
      default: undefined
    },
  },
  data() {
    return {
      isSingle: true,
      loading: false,
      flag: true,
      companyName: '',
      container: null, //容器svg>g
      duration: 500, //动画持续时间
      scaleRange: [0.2, 2], //container缩放范围
      direction: ['r', 'l'], //分为左右2个方向
      centralPoint: [0, 0], //画布中心点坐标x,y
      root: { r: {}, l: {} }, //左右2块数据源
      rootNodeLength: 0, //根节点名称长度
      rootName: [], //根节点名称
      textSpace: 15, //多行文字间距
      themeColor: '#128BED', //主色
      nodeSize: [50, 50], //节点间距(高/水平)
      fontSize: 14, //字体大小，也是单字所占宽高
      rectMinWidth: 50, //节点方框默认最小，
      textPadding: 5, //文字与方框间距,注：固定值5
      circleR: 6, //圆圈半径
      dirRight: '',
      forUpwardNew: '',
      svg: null,
      tooltip: null,
      tipNode: null,
      ent_id: null,
      entName: null,
      pageSize: 10,
      upData: [],
      dowData: [],
    }
  },
  components: {
    Toolbar,
  },
  computed: {
    treeMap() {
      return d3
        .tree()
        .nodeSize(this.nodeSize)
        .separation((a, b) => {
          let result = a.parent === b.parent && !a.children && !b.children ? 1 : 2

          return result
        })
    },
  },
  mounted() {
    const { query, path } = useRoute()
    if (path.includes('fullscreen')) {
      usePortrait()
    }
    if (query?.ent_name) {
      const title = decodeURIComponent(query.ent_name)
      this.entName = title.split(',')
    }
    this.ent_id = decodeURIComponent(query?.ent_id)
    this.isSingle = query?.isSingle && query?.isSingle !== 'false'

    getEntListFn = getEntSupplyChains
    this.getData()
  },
  created() {
    this.debounceGetMore = debounce(this.getMore, 1000, true)
    this.debounceGetNext = debounce(this.getNext, 1000, true)
  },
  methods: {
    async getData() {
      if (this.loading) return
      this.$loading({ message: '加载中...', forbidClick: true, duration: 0 })
      // handleCss()
      this.loading = true
      let { ent_id, entName } = this

      d3.selectAll('svg.svg-upanddown').remove()
      let param = {
        ent_id,
        page_size: this.pageSize,
        page_index: 1,
      }
      let upParam = { ...param, type: 1 }
      let downParam = { ...param, type: 2 }
      let up = await getEntListFn(upParam)
      this.upData = up.datalist
      up.le_count = up.count - this.upData.length
      let down = await getEntListFn(downParam)
      this.downData = down.datalist
      down.le_count = down.count - this.downData.length
      this.loading = false
      this.$close()
      this.flag = false

      this.flag = !!up.datalist.length || !!down.datalist.length

      // if (!up.datalist.length && !down.datalist.length) {
      //   this.loading = false;
      //   this.flag = false;
      //   return;
      // }

      // up.datalist.forEach(item => {
      //   setTags(item)
      // })
      // down.datalist.forEach(item => {
      //   setTags(item)
      // })
      // this.loading = false;
      // this.flag = true;

      this.rootName = Array.isArray(entName) ? entName : [entName]
      let resData = {
        r: {
          ent_name: '',
          children: [
            {
              ent_name: '下游企业',
              nodetype: '222',
              children: [],
              count: 0,
            },
          ],
        },
        l: {
          ent_name: '',
          children: [
            {
              ent_name: '上游企业',
              nodetype: '222',
              children: [],
              count: 0,
            },
          ],
        },
      }
      resData.r.children[0].children.push(...down.datalist)
      resData.r.children[0].count = down.le_count
      resData.l.children[0].children.push(...up.datalist)
      resData.l.children[0].count = up.le_count

      let left = resData.l,
        right = resData.r
      let mynodes

      for (var i = 0; i < left.children.length; i++) {
        // 添加更多按钮
        if (left.children[i].count > 0) {
          left.children[i].children.push({
            ent_name: '更多',
            type: -1,
            val: left.children[i].count,
            page_index: 1,
            page_size: this.pageSize,
            streamType: 1,
            isUp: true,
            ent_id,
          })
        }
      }
      for (var i = 0; i < right.children.length; i++) {
        // 添加更多按钮
        if (right.children[i].count > 0) {
          right.children[i].children.push({
            ent_name: '更多',
            type: -1,
            val: right.children[i].count,
            page_index: 1,
            page_size: this.pageSize,
            streamType: 2,
            isUp: false,
            ent_id,
          })
        }
      }
      this.$nextTick(() => {
        this.treeInit(resData)
      })
    },

    uuid() {
      function s4() {
        return Math.floor((1 + Math.random()) * 0x10000)
          .toString(16)
          .substring(1)
      }

      return s4() + s4() + '-' + s4() + '-' + s4() + '-' + s4() + '-' + s4() + s4() + s4()
    },

    //初始化
    treeInit(data) {
      const margin = { top: 0, right: 0, bottom: 0, left: 0 }
      const width = this.width || document.body.clientWidth
      const height = this.height || document.body.clientHeight
      const treeWidth = width - margin.left - margin.right //tree容器宽
      const treeHeight = height - margin.top - margin.bottom //tree容器高
      const centralY = treeWidth / 2 + margin.left
      const centralX = treeHeight / 2 + margin.top
      this.centralPoint = [centralX, centralY] //中心点坐标

      //根节点字符所占宽度 -- 如果是数组 这里应该要取最大长度的那个名字

      // this.rootNodeLength = this.rootName[0].length * this.fontSize + 50
      this.rootNodeLength = Math.max(...this.rootName.map(name => name.length)) * this.fontSize + 50

      this.tooltip = d3.select('.tooltipUp')
      this.tooltip
        .on('touchend', d => {
          this.tooltip.style('opacity', 0.0).style('z-index', -1)
        })
        .on('touchcancel', d => {
          this.tooltip.style('z-index', 999).style('opacity', 1.0)
        })

      //svg标签
      this.svg = d3
        .select('#treeRootUp')
        .append('svg')
        .attr('class', 'svg-upanddown')
        .attr('xmlns', 'http://www.w3.org/2000/svg')
        .attr('width', treeWidth)
        .attr('height', treeHeight)
        .attr('font-size', this.fontSize)
        // .attr('viewBox', `0 0 ${treeWidth*1.5} ${treeHeight*1.5}`)
        .attr('fill', '#555')

      //g标签
      this.container = this.svg
        .append('g')
        .attr('class', 'container1')
        // .attr('width', treeWidth)
        // .attr('height', treeHeight)
        .attr('transform', 'translate(' + margin1.left + ',' + margin1.top + ')scale(0.7)')

      //画出根节点
      this.drawRoot()
      //指定缩放范围
      const zoom = d3?.zoom().scaleExtent(this.scaleRange).on('zoom', this.zoomFn)
      //动画持续时间
      this.container.transition().duration(this.duration).call(zoom.transform, d3.zoomIdentity)
      this.svg.call(zoom)
      //数据处理
      this.dealData(data)
    },

    zoomFn() {
      var weizhi = document.getElementsByClassName('container1')[0]

      weizhi.style.transform = '' //偏移位置
      const zoom = d3.event.transform
      return this.container.attr(
        'transform',
        'translate(' +
          (Number(zoom.x) + Number(margin1.left)) +
          ',' +
          (zoom.y + margin1.top) +
          ')scale(' +
          zoom.k * 0.8 +
          ')'
      )
    },
    //数据重组
    DataReor(d, direction, source, appendData) {
      var setDepth = function (node, num, appendLeaf) {
        //重新设置depth

        node.depth = num
        if (node.children && node.children.length) {
          //遍历children
          node.children.forEach(function (item) {
            setDepth(item, num + 1, appendLeaf)
          })
        } else {
          appendLeaf.push(node)
        }
      }

      var setHeight = function (arr, num) {
        //重新设置height
        var parent = []
        arr.forEach(function (node) {
          node.height = Math.max(num, node.height)
          if (node.parent && parent.indexOf(node.parent) == -1) {
            parent.push(node.parent)
          }
        })

        if (parent.length) {
          setHeight(parent, num + 1)
        }
      }

      var appendLeaf = [] //增加的叶子节点

      if (appendData.children.length) {
        if (!d.children) {
          d.children = []
        }
        appendData.children.forEach(function (item, index) {
          var newNode = d3.hierarchy(item)
          newNode.parent = d
          newNode.height = -1
          setDepth(newNode, d.depth + 1, appendLeaf)
          d.children.push(newNode)
        })
      }

      if (appendLeaf.length) {
        setHeight(appendLeaf, 0)
      }

      if (source.data.ent_name == '更多') {
        source.parent.descendants().forEach(d => {
          d._children = d.children
          d.id = direction + this.uuid()
        })
      } else {
        source.descendants().forEach(d => {
          d._children = d.children
          d.id = direction + this.uuid()
        })
      }
      this.update(d, direction)
    },
    getMore(d, direction, source) {
      let vm = this
      d.parent.children.splice(d.parent.children.length - 1, d.parent.children.length)
      this.DataReor(d.parent, direction, source, { children: [] })
      let { ent_id, page_index, page_size, streamType } = d.data
      page_index += 1
      getEntSupplyChains({ type: streamType, ent_id: ent_id, page_index, page_size: this.pageSize }).then(res => {
        res.datalist.forEach(item => {
          setTags(item)
        })
        var arr = res.datalist
        let le_count = 0
        if (streamType === 1) {
          this.upData = [...this.upData, ...arr]
          le_count = res.count - this.upData.length
        } else {
          this.downData = [...this.downData, ...arr]
          le_count = res.count - this.downData.length
        }
        if (le_count > 0) {
          arr.push({
            ent_name: '更多',
            type: -1,
            val: le_count,
            page_index,
            page_size: this.pageSize,
            streamType,
            ent_id,
          })
        }
        var appendData = {
          children: arr,
        }
        this.DataReor(d.parent, direction, source, appendData)
      })
    },
    getNext(d, direction, source, type) {
      let vm = this
      const { ptype, ent_id, nodetype } = d.data
      if (nodetype == '222') {
        this.$showToast({ message: '未查询到相关信息！', forbidClick: false, duration: 1200 })
        handleCss()
        return
      }
      let param = {
        ent_id: ent_id,
        page_size: this.pageSize,
        page_index: 1,
        type: ptype,
        levels: ['1'],
        is_faucet: false,
        is_market: false,
        is_local: false,
      }
      getEntListFn(param).then(next => {
        if (next.datalist.length == 0) {
          this.$showToast({ message: '未查询到相关信息！', forbidClick: false, duration: 1200 })
          handleCss()
          return
        }
        next.datalist.forEach(item => {
          // setTags(item)
        })
        var data = [
          {
            ent_name: '企业',
            nodetype: '222',
            children: [],
            count: 0,
            ent_id,
          },
        ]
        data[0].children.push(...next.datalist)
        data[0].count = next.le_count
        if (data[0].count > 0) {
          data[0].children.push({
            ent_name: '更多',
            type: -1,
            val: data[0].count,
            page_index: 1,
            page_size: 10,
            types: [ptype],
            ent_id,
            levels: ['1'],
            is_faucet: false,
            is_market: false,
            is_local: false,
          })
        }

        var appendData = {
          children: data[0].children,
        }

        vm.DataReor(d, direction, source, appendData)
      })
    },
    getNode(d, direction, source, type) {
      let vm = this
      var mynodes

      if (d.data.ent_name == '更多') {
        this.debounceGetMore(d, direction, source, type)
      } else if (type == 1) {
        var appendData = {
          children: [],
        }
        this.DataReor(d, direction, source, appendData)
      } else {
        // this.debounceGetNext(d, direction, source, type)
        // const { ntype, ent_id, hasnext } = d.data;
        // if (hasnext) {
        //   let params = { ent_id };
        //   api.getUpAndDown(params).then((res) => {
        //     var data = [
        //       {
        //         ent_name: '上游供应商',
        //         nodetype: '222',
        //         children: [],
        //         count: 0,
        //       },
        //       {
        //         ent_name: '下游服务商',
        //         nodetype: '222',
        //         children: [],
        //         count: 0,
        //       },
        //     ];
        //     data[1].children.push(...res.facilitator_data.data);
        //     data[1].count = res.facilitator_data.count;
        //     data[0].children.push(...res.supplier_data.data);
        //     data[0].count = res.supplier_data.count;
        //     // var data = resData;
        //     var mynodes;
        //     for (var i = 0; i < data.length; i++) {
        //       if (data[i].count > 0) {
        //         data[i].children.push({
        //           ent_name: '更多',
        //           type: -1,
        //           val: data[i].count,
        //           page_index: 1,
        //           tab: i == 0 ? 'supplier' : 'facilitator',
        //           ent_id,
        //         });
        //       }
        //     }
        //     var appendData = {
        //       children: data,
        //     };
        //     vm.DataReor(d, direction, source, appendData);
        //   });
        // } else {
        //   // this.$message.info('未查询到相关信息！');
        //   this.$showToast({ message: '未查询到相关信息！', forbidClick: false, duration: 1200 });
        //   handleCss();
        // }
      }
    },
    //数据处理
    dealData(data) {
      this.direction.forEach(item => {
        this.root[item] = d3.hierarchy(data[item])
        this.root[item].x0 = this.centralPoint[0] //根节点x坐标
        this.root[item].y0 = this.centralPoint[1] //根节点Y坐标

        this.porData(this.root[item], item)
      })
    },
    //遍历
    porData(obj, item) {
      obj.descendants().forEach(d => {
        d._children = d.children
        d.id = item + this.uuid()
      })
      this.update(obj, item)
    },
    //画根节点
    drawRoot() {
      const rectHeight = 42
      const rectRadius = 5 // 矩形圆角
      const gapBetweenRects = 10 // 每个矩形之间的间距
      const lineLRBetweenRects = 20 // 每个矩形之间的间距
      let yPosition = 0 // 用于计算每个矩形的垂直位置
      const centerH =
        (rectHeight * this.rootName.length + (this.rootName.length - 1) * gapBetweenRects) / 2 - rectHeight / 2

      const totalH = rectHeight * this.rootName.length + (this.rootName.length - 1) * gapBetweenRects
      const title = this.container
        .append('g')
        .attr('id', 'rootTitleUp')
        .attr('cursor', 'pointer')
        .attr('transform', `translate(${this.centralPoint[1]},${this.centralPoint[0] - centerH})`)
      // 先绘制包裹框
      if (!this.isSingle) {
        // 画2条线 -左边
        title
          .append('line')
          .attr('x1', -this.rootNodeLength / 2 - lineLRBetweenRects)
          .attr('y1', -rectHeight / 2 + rectHeight / 2)
          .attr('x2', -this.rootNodeLength / 2 - lineLRBetweenRects)
          .attr('y2', totalH - rectHeight) // 调整线长
          .attr('stroke', 'rgb(153, 153, 153,0.5)') // 设置线颜色
          .attr('stroke-width', 1) // 设置线宽
        // 画2条线 -右边
        title
          .append('line')
          .attr('x1', this.rootNodeLength / 2 + lineLRBetweenRects)
          .attr('y1', rectHeight / 2 - rectHeight / 2)
          .attr('x2', this.rootNodeLength / 2 + lineLRBetweenRects)
          .attr('y2', totalH - rectHeight) // 调整线长
          .attr('stroke', 'rgb(153, 153, 153,0.5)') // 设置线颜色
          .attr('stroke-width', 1) // 设置线宽
        this.rootName.forEach((ent_name, index) => {
          // 为每个公司名称创建一个 group
          const nodeGroup = title.append('g').attr('class', 'nodeGroup').attr('transform', `translate(0, ${yPosition})`)

          // 添加线
          // 添加2条线,分别在左右两边
          title
            .append('line')
            .attr('x1', -this.rootNodeLength / 2)
            .attr('y1', index * (rectHeight + gapBetweenRects))
            .attr('x2', -(this.rootNodeLength / 2 + lineLRBetweenRects))
            .attr('y2', index * (rectHeight + gapBetweenRects))
            .attr('stroke', 'rgb(153, 153, 153,0.5)') // 设置线颜色
            .attr('stroke-width', 1) // 设置线宽
          title
            .append('line')
            .attr('x1', this.rootNodeLength / 2)
            .attr('y1', index * (rectHeight + gapBetweenRects))
            .attr('x2', this.rootNodeLength / 2 + lineLRBetweenRects)
            .attr('y2', index * (rectHeight + gapBetweenRects))
            .attr('stroke', 'rgb(153, 153, 153,0.5)') // 设置线颜色
            .attr('stroke-width', 1) // 设置线宽

          // 添加矩形
          nodeGroup
            .append('rect')
            .attr('class', 'rootTitleUp')
            .attr('x', -this.rootNodeLength / 2) //  默认中间 所以需要左移一半 -
            .attr('y', -rectHeight / 2) // 默认中间 所以需要上移一半 单个
            .attr('width', this.rootNodeLength)
            .attr('height', rectHeight)
            .attr('rx', rectRadius)
            .style('fill', this.themeColor)
          // 添加文本
          nodeGroup
            .append('text')
            .attr('fill', 'white')
            .attr('x', 0) // 水平居中
            .attr('y', 5) // 垂直居中可以根据字体大小微调
            .attr('text-anchor', 'middle')
            .style('font-size', '16px')
            .style('font-weight', 500)
            .text(ent_name)
          // 更新 yPosition，以便下一个矩形不会重叠
          yPosition += rectHeight + (index === this.rootName.length - 1 ? 0 : gapBetweenRects)
        })
        return
      }

      title
        .append('svg:rect')
        .attr('class', 'rootTitleUp')
        .attr('y', 0)
        .attr('x', -this.rootNodeLength / 2)
        .attr('width', this.rootNodeLength)
        .attr('height', 42)
        .attr('rx', 5) //圆角
        .style('fill', this.themeColor)
      // .style('fill', 'rgba(0,0,0,0.5)')
      this.rootName.forEach((ent_name, index) => {
        title
          .append('text')
          .attr('fill', 'white')
          .attr('y', function () {
            return 5
          })
          .attr('text-anchor', 'middle')
          .style('font-size', function () {
            if (index == 1) {
              return '16px'
            } else {
              return '16px'
            }
          })
          .style('font-weight', 500)
          .text(ent_name)

        d3.select('#rootTitleUp rect')
          .attr('height', 42)
          .attr('y', -42 / 2)
      })
    },
    seat(newArr, dirRight, data) {
      for (var j = 0; j < newArr.length; j++) {
        if (j != 0) {
          if (j == 1) {
            for (var i = 0; i < data[j].length; i++) {
              data[j][i].y = data[j - 1][0].y + newArr[j - 1] - 90
            }
          } else {
            for (var i3 = 0; i3 < data[j].length; i3++) {
              data[j][i3].y = data[j - 1][0].y + newArr[j - 1] - 20
            }
          }
        }
      }
    },
    testLength(data, dirRight) {
      var level = [],
        width1
      for (var i = 0; i < data.length; i++) {
        var newArr = new Array()
        for (var j = 0; j < data[i].length; j++) {
          this.svg
            .append('text')
            .style('font-size', this.fontSize)
            .text(function (d) {
              if (data[i][j].data.tags) {
                return data[i][j].data.ent_name + data[i][j].data.tags.join('补全四字')
              } else if (data[i][j].data.ent_name) {
                return data[i][j].data.ent_name
              }
            })
            .attr('class', 'test')
            .attr('width', function (d) {
              width1 = d3.select(this.getComputedTextLength())._groups[0]
              if (data[i][j].data.tags) {
                newArr.push(Number(width1) + 100 + 35 + 20)
              } else {
                newArr.push(Number(width1) + 100 + 35)
              }
              data.width1 = d3.select(this.getComputedTextLength())._groups[0]
            })
          // $('.test').remove();
          deleteChild('.test')
        }
        level.push(Math.max.apply(null, newArr))
      }
      this.seat(level, dirRight, data)
    },

    //开始绘图
    update(source, direction) {
      let vm = this
      let timer
      const dirRight = direction === 'r' ? 1 : -1 //方向为右/左
      const forUpward = direction == 'r'
      this.forUpwardNew = forUpward
      const className = `${direction}gNode`
      const tree = this.treeMap(this.root[direction])
      const nodes = tree.descendants()
      const links = tree.links()
      var data = []
      if (nodes.length > 1) {
        for (var i = 0; i < nodes.length; i++) {
          if (!data[nodes[i].depth]) {
            var arr = []
            arr.push(nodes[i])
            data[nodes[i].depth] = arr
          } else {
            data[nodes[i].depth].push(nodes[i])
          }
        }
        //检测最大长度
        this.testLength(data, dirRight)
      }
      nodes.forEach(d => {
        let wid = -20
        if (d.children && d.data.ent_name) {
          this.svg
            .append('text')
            .style('font-size', this.fontSize)
            .text(d.data.ent_name)
            .attr('class', 'test')
            .attr('width', function (d) {
              wid = parseInt(d3.select(this.getComputedTextLength())._groups[0][0])
            })
        }
        // $('.test').remove();
        deleteChild('.test')
        d.y = dirRight * (d.y + this.rootNodeLength / 2) + this.centralPoint[1]
        d.sy = wid
        d.x = d.x + this.centralPoint[0]
      })

      //根据class名称获取左或者右的g节点，达到分块更新
      const node = this.container.selectAll(`g.${className}`).data(nodes, d => d.id)
      //新增节点，tree会根据数据内的children扩展相关节点
      const nodeEnter = node
        .enter()
        .append('g')
        .attr('cursor', 'pointer')
        .attr('id', d => `g${d.id}`)
        .attr('class', className)
        .attr('transform', d => `translate(${source.y0},${source.x0})`)
        .attr('fill-opacity', 0)
        .attr('stroke-opacity', 0)
        .on('click', function (d) {
          d3.select(this)
            .selectAll('.node-circle .node-circle-vertical')
            .transition()
            .duration(this.duration)
            .attr('stroke-width', function (d) {
              if (d.children) {
                return 1
              } else {
                return 0
              }
            })

          if (d.data.ent_name == '更多') {
            return vm.clickNode(d, direction, source)
          } else if (d.depth !== 0) {
            return vm.clickNode(d, direction, source)
          }
        })
        // mouseenter
        .on('touchstart', d => {
          changeAnimation(d, true)
          // const { clientX, layerY } = d3.event;
          // if (d.data.ent_id) {
          //   clearTimeout(timer);
          //   timer = setTimeout(() => {
          //     vm.changeTooltip(d, clientX, layerY, true);
          //   }, 500);
          // }
        })
        // mouseleave
        .on('touchend', d => {
          changeAnimation(d, false)
          // clearTimeout(timer);
          // d.data.ent_id && vm.changeTooltip(d, false);
        })
        .on('touchcancel', d => {
          changeAnimation(d, false)
          // clearTimeout(timer);
          // d.data.ent_id && vm.changeTooltip(d, false);
        })
      nodeEnter.each(d => {
        if (d.depth > 0) {
          this.drawText(`g${d.id}`, dirRight)
          this.drawRect(`g${d.id}`, dirRight)
          this.marker(`g${d.id}`, dirRight)
        }
        if (d.data.tags && d.data.tags.length > 0) {
          d.data.tags.forEach((item, index) => {
            this.drawRectPop(`g${d.id}`, dirRight, item, index)
          })
        }
        //画节点数量
        if (d.data.type == -1) {
          this.drawLength(`g${d.id}`, dirRight)
        }
        if (d.depth > 0) {
          this.drawRect(`g${d.id}`, dirRight)
          // 绘制打开子级的按钮 加减号
          if (d.data?.nodetype === '222') {
            this.drawCircle(`g${d.id}`, dirRight, source, direction)
          }
        }
        // if (d.depth > 0) {
        //   let nameLength = d.data.ent_name ? d.data.ent_name.length : 0;
        //   let right = dirRight > 0;
        //   let xDistance = right ? nameLength * 14 : -nameLength * 14;
        //   this.drawCircle(`g${d.id}`, dirRight, source, direction, xDistance);
        // }
      })

      // 更新节点：节点enter和exit时都会触发tree更新
      const nodeUpdate = node
        .merge(nodeEnter)
        .transition()
        .duration(this.duration)
        .attr('transform', function (d) {
          d3.select(this)
            .selectAll('.node-circle .node-circle-vertical')
            .transition()
            .duration(this.duration)
            .attr('stroke-width', function (d) {
              if (d.children) {
                return 0
              } else {
                return 1
              }
            })
          var index = 0
          return 'translate(' + d.y + ',' + d.x + ')'
        })
        .attr('fill-opacity', 1)
        .attr('stroke-opacity', 1)

      // 移除节点:tree移除掉数据内不包含的节点(即，children = false)
      const nodeExit = node
        .exit()
        .transition()
        .duration(this.duration)
        .remove()
        .attr('transform', d => `translate(${source.y},${source.x})`)
        .attr('fill-opacity', 0)
        .attr('stroke-opacity', 0)
      // Update the links 根据 className来实现分块更新
      const link = this.container.selectAll(`path.${className}`).data(links, d => d.target.id)
      // Enter any new links at the parent's previous position.
      //insert是在g标签前面插入，防止连接线挡住G节点内容
      // const linkEnter = link.enter().append("path")
      const linkEnter = link
        .enter()
        .insert('path', 'g')
        .attr('class', className)
        .attr('d', d => {
          const o = { x: source.x0, y: source.y0, sy: source.sy }
          return this.diagonal({ source: o, target: o })
        })
        .attr('fill', 'none')
        .attr('stroke-width', 1)
        .attr('stroke', '#D8D8D8')

      // Transition links to their new position.
      link
        .merge(linkEnter)
        .transition()
        .duration(this.duration)
        .attr('fill-opacity', 1)
        .attr('stroke-opacity', 1)
        .attr('d', this.diagonal)
      // Transition exiting nodes to the parent's new position.
      link
        .exit()
        .transition()
        .duration(this.duration)
        .remove()
        .attr('fill-opacity', 0)
        .attr('stroke-opacity', 0)
        .attr('d', d => {
          const o = { x: source.x, y: source.y, sy: source.sy }
          return this.diagonal({ source: o, target: o })
        })

      // Stash the old positions for transition.
      this.root[direction].eachBefore(d => {
        d.x0 = d.x
        d.y0 = d.y
      })

      // 鼠标移入移出连线动画
      function changeAnimation(a, show) {
        let classname = 'flow-out'
        let link1 = vm.container.selectAll(`path`)
        link1.classed(classname, function (edge) {
          if (a.data.nodetype == '222') {
            if (edge.source === a || edge.target === a) {
              return show
            } else {
              return false
            }
          }
        })
      }
    },

    //画连接线
    diagonal({ source, target }) {
      let s = source,
        d = target
      if (this.forUpwardNew) {
        return (
          'M' +
          (s.y + s.sy + 20) +
          ',' +
          s.x +
          'L' +
          (s.y + (d.y - s.y) - 40) +
          ',' +
          s.x +
          'L' +
          (s.y + (d.y - s.y) - 40) +
          ',' +
          d.x +
          'L' +
          d.y +
          ',' +
          d.x
        )
      } else {
        return (
          'M' +
          (s.y - s.sy - 20) +
          ',' +
          s.x +
          'L' +
          (s.y + (d.y - s.y) + 40) +
          ',' +
          s.x +
          'L' +
          (s.y + (d.y - s.y) + 40) +
          ',' +
          d.x +
          'L' +
          d.y +
          ',' +
          d.x
        )
      }
    },

    //画箭头
    marker(id, dirRight, ent_name) {
      let vm = this
      let gMark = d3.select(`#${id}`).append('g')
      return gMark
        .insert('path', 'text')
        .attr('d', function (d) {
          if (d.data.nodetype == '222' && dirRight > 0) {
            return 'M0,0 L0,5 L8,0 L0,-5 Z'
          } else if (d.data.nodetype == '222' && dirRight < 0) {
            return 'M-8,0 L0,-5 L0,5 Z'
          }
        })
        .style('fill', '#128BED')
    },

    //画文本
    drawText(id, dirRight) {
      let vm = this
      dirRight = dirRight > 0 //右为1，左为-1
      return d3
        .select(`#${id}`)
        .append('text')
        .attr('y', vm.textPadding)
        .attr('x', function (d) {
          if (d.data.nodetype == '222') {
            return dirRight ? 10 : -10
          } else if (d.data.ent_name == '更多') {
            return 0
          } else {
            return dirRight ? 20 : -20
          }
        })
        .attr('text-anchor', dirRight ? 'start' : 'end')
        .style('font-size', this.fontSize)
        .text(function (d) {
          return d.data.ent_name
        })
        .attr('fill', function (d) {
          if (d.data.nodetype == -1 || d.data.type == -1) {
            return 'rgb(33, 150, 243)'
          } else if (d.data.nodetype == '222') {
            return '#3D4255'
          } else {
            return '#333'
          }
        })
        .attr('width', function (d) {
          circlewidth1 = d3.select(this.getComputedTextLength())._groups[0][0] + 2
          return d3.select(this.getComputedTextLength())._groups[0][0] + 2
        })
    },
    //更多节点数量
    drawLength(id) {
      let vm = this
      return d3
        .select(`#${id} text`)
        .append('tspan')
        .attr('fill', d => '#999')
        .text(function (d) {
          if (d.data.type == '-1') {
            return ' (' + d.data.val + ')'
          }
        })
        .attr('fill', 'rgb(33, 150, 243)')
        .attr('width', function (d) {
          if (d.data.type == '-1') {
            circlewidth4 = d3.select(this.getComputedTextLength())._groups[0][0]
          }
          return d3.select(this.getComputedTextLength())._groups[0]
        })
    },
    //画方框
    drawRect(id, dirRight) {
      let vm = this
      let realw = document.getElementById(id).getBBox().width + 30 //获取g实际宽度后，设置rect宽度
      return d3
        .select(`#${id}`)
        .insert('rect', 'text')
        .attr('x', function (d) {
          if (dirRight < 0) {
            return -realw
          } else {
            0
          }
        })
        .attr('y', function (d) {
          return -vm.textSpace + vm.textPadding - 4
        })
        .attr('width', function (d) {
          return realw
        })
        .attr('height', function (d) {
          return vm.textSpace + vm.textPadding + 8
        })
        .attr('rx', 6) //圆角
        .style('fill', function (d) {
          if (d.data.type == -1 || d.data.nodetype == '222') {
            if (d.data.type == -1) {
              return 'rgba(255,255,255,0.1)'
            } else {
              return 'rgba(255,255,255,0)'
            }
          } else {
            // return 'rgba(255, 255, 255,0.5)';
            return '#E0F0FF'
          }
        })
    },

    //画标签
    drawRectPop(id, dirRight, item, index) {
      let vm = this
      let realw = document.getElementById(id).getBBox().width + 50 //获取g实际宽度后，设置rect宽度
      let pop = d3
        .select(`#${id}`)
        .append('g')
        .attr('transform', function (d) {
          let leng = Number(circlewidth1) + 15 + 20
          if (dirRight < 0) {
            return 'translate(' + (-64 - leng + 6 - index * 74) + ',' + -10 + ')'
          } else {
            return 'translate(' + (leng - 6 + index * 74) + ',' + -10 + ')'
          }
        })
      pop
        .append('rect')
        .attr('width', 64)
        .attr('height', 20)
        .attr('rx', 6) //圆角
        .style('fill', '#ECF5FF')
      pop
        .append('text')
        .attr('fill', '#076EE4')
        .attr('y', 14)
        .attr('x', 8)
        .style('font-size', '12px')
        .style('font-weight', 400)
        .text(item)
      return pop
    },

    //画circle
    drawCircle(id, dirRight, source, direction, xDistance) {
      let realw = document.getElementById(id).getBBox().width + 50 //获取g实际宽度后，设置rect宽度
      let vm = this
      let leng
      let gMark = d3
        .select(`#${id}`)
        .append('g')
        .attr('class', 'node-circle')
        .attr('stroke', 'rgb(153, 153, 153)')
        .attr('transform', function (d) {
          if (d.data.type == '-1') {
            leng = Number(circlewidth1 + circlewidth4) + 15
            if (dirRight == 1) {
              return 'translate(' + leng + ',0)'
            } else {
              return 'translate(' + -leng + ',0)'
            }
          } else if (d.data.nodetype == '222') {
            leng = Number(circlewidth1) + 15 + 1
            if (dirRight == 1) {
              return 'translate(' + leng + ',0)'
            } else {
              return 'translate(' + -leng + ',0)'
            }
          } else {
            leng = realw - 65
            if (dirRight == 1) {
              return 'translate(' + leng + ',0)'
            } else {
              return 'translate(' + -leng + ',0)'
            }
          }
        })
        .attr('stroke-width', d => (d.data.nodetype == '222' ? 0 : 1))

      gMark
        .append('circle')
        .attr('r', function (d) {
          if (d.data.type == '-1') {
            return 6
          }
          return 6
        }) //根节点不设置圆圈
        .attr('fill', function (d) {
          if (d.data.nodetype == '222' || d.data.ent_name == '更多') {
            return '#A0A5BA'
          }
          return 'none'
        })
        .attr('stroke', function (d) {
          if (d.data.nodetype == '222' || d.data.ent_name == '更多') {
            return 'none'
          }
          return '#3D4255'
        }) //横线
      let padding = this.circleR - 2

      gMark
        .append('path')
        .attr('d', `m -${padding} 0 l ${2 * padding} 0`)
        .attr('stroke-width', function (d) {
          if (d.data.nodetype == '222' || d.data.ent_name == '更多') {
            return 1
          }
          return 1
        })
        .attr('stroke', function (d) {
          if (d.data.nodetype == '222' || d.data.ent_name == '更多') {
            return '#FCFCFC'
          }
          return '#3D4255'
        }) //横线

      gMark
        .append('path') //竖线 ，根据展开/收缩动态控制显示
        .attr('d', `m 0 -${padding} l 0 ${2 * padding}`)
        .attr('stroke-width', function (d) {
          if (d.data.nodetype == '222') {
            return 0
          }
          return 1
        })
        .attr('class', 'node-circle-vertical')
        .attr('stroke', function (d) {
          if (d.data.nodetype == '222' || d.data.ent_name == '更多') {
            return '#FCFCFC'
          }
          return '#3D4255'
        })
      return gMark
    },

    expand(d, direction, source) {
      if (d.data.ent_name == '更多' || !d._children) {
        this.getNode(d, direction, source)
        return
      }
      if (d._children) {
        d.children = d._children

        d._children = null
        this.update(d, direction)
      }
    },

    collapse(d, direction, obj) {
      if (d.children) {
        d._children = d.children
        d.children = null
      }
      if (obj == 1) {
        this.update(d, direction)
      }
    },

    //点击某个节点
    clickNode(d, direction, source) {
      let vm = this
      // vm.container.transition().duration(vm.duration).attr(
      //       "transform",
      //       "translate(" + d.y + "," + d.x + ")"
      //     );
      if (d.children || d.children) {
        vm.collapse(d, direction, 1)
      } else {
        vm.expand(d, direction, source)
      }
    },
    // 鼠标移入移出信息面板
    // eslint-disable-next-line no-unused-vars
    changeTooltip(d, x, y, show) {
      if (show) {
        if (this.tipNode == d) {
          return false
        } else {
          this.tipNode = d
          const { ent_id } = d.data

          let params = { ent_id }
          // 24.10.19
          // api.basicRegInfo(params).then(res => {
          //   const data = res
          //   let detailDom = this.setCompanyDetailDom(data)

          //   this.tooltip
          //     .html(detailDom)
          //     .style('transform', `translate(${x}px, ${y}px)`)
          //     .style('opacity', 1)
          //     .style('z-index', 999)
          // })
        }
      } else {
        this.tipNode = undefined
        this.tooltip.style('opacity', 0).style('z-index', -1)
      }
    },
    // 设置企业详情节点内容
    setCompanyDetailDom(data) {
      // onClick="clickName(entData) 下面点击事件外面接收 用的是window.clickName这种来做的
      let tags = getTagBackGround(data.other_tag)
        .map(tag => {
          return `<span class=${tag.tagColor}>${tag.tagName}</span>`
        })
        .filter((tag, index) => index < 4)
        .join('')
      const _html = `<div class="tooltip-box">
					<img src=${data.ent_logo} />
					<div class="info">
						<p class="tit">
							<span style="cursor:pointer;">${data.ent_name}</span>
							${getTagBackGround([data.ENTSTATUS])
                .map(tag => {
                  return `<span class='tag ${tag.tagColor}'>${tag.tagName}</span>`
                })
                .join('')}
						</p>
						<p class="tag-box">
							${tags}
						</p>
						<p style="margin-top:"+${setPx(10)}+"px;">
							<span>法定代表人:</span>
							<span class="value">${data.FRNAME}</span>
						</p>
						<p>
							<span>注册资本：</span>
							<span class="value">${data.REGCAP}${data.REGCAPCUR}</span>
						</p>
						<p>
							<span>成立日期：</span>
							<span class="value">${data.ESDATE}</span>
						</p>
					</div>
				</div>`
      return _html
    },
  },
}
</script>

<style lang="scss">
@import '@/views/supply_chain/child/atlasCompoent/tooltip.scss';
</style>
<style lang="scss" scoped>
// @media screen and(orientation: portrait) {
//   body {
//     transform-origin: 0 0;
//     transform: rotateZ(90deg) translateY(-100%);
//   }
// }

.noData {
  width: 100%;
  height: 100%;
  :deep(.cont) {
    // border: 1px solid red;
    transform: translateY(0);
    transform: scale(0.7);
  }
}
</style>
