<template>
  <!-- 只有龙头企业筛选是全部产业 其余列表筛选为行业 -->
  <div class="leading-page">
    <div class="dropdown" ref="zhanWeiRef">
      <DropDown @submit="filterSubmit" :oldData="heightParams" :aside-list="asideOptions" isChain="chain_codes" />
    </div>
    <!-- <div class="total f-y-center justify-between">
      <div>
        共找到<span class="num">{{ total }}</span
        >家企业
      </div>
      <div class="pr-24">
      </div>
    </div> -->
    <div class="flex items-center justify-between p-24 text-28 bg-grey">
      <div class="flex items-center">
        共为您推荐
        <span class="px-8 font-semibold text-selected">{{ total }}</span>
        家企业
      </div>

      <van-popover v-model:show="showPopover" placement="bottom-end" @select="onOrderSelect">
        <template #reference>
          <span class="flex items-center text-28 text-tertiary">
            {{ orderby.text }}
            <svg-icon
              name="arrow-down"
              class="ml-8 transition-all duration-300"
              :class="[showPopover ? 'rotate-180' : '']"
            />
          </span>
        </template>

        <div class="w-full bg-white">
          <div
            v-for="item in actions"
            :key="item.text"
            class="p-24 text-28"
            :class="[orderby.value === item.value && 'text-selected']"
            @click="onOrderSelect(item)"
          >
            {{ item.text }}
          </div>
        </div>
      </van-popover>
    </div>
    <div :style="{ height: listHeight + 'px' }" class="list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          @load="loadmore"
          offset="1"
          :immediate-check="false"
          :finished="finished"
          :finished-text="entList.length ? '没有更多了' : undefined"
        >
          <EntCard
            :entList="entList"
            :query="highlightQuery"
            :openNow="openNow"
            @visibleChange="visibleChange"
            @clickItem="item => emits('clickItem', item)"
            special
            type="noThreeAddCollect"
          />
        </van-list>
      </van-pull-refresh>
      <div class="noData" v-if="noData">
        <img src="@/assets/image/null.png" alt="" />
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup name="LeadingEnterprises">
import DropDown from '@/components/Hunt/component/dropDown/index.vue'
import EntCard from '@/components/EntCard/index.vue'
import { handlestructure, handleData } from '@/components/Hunt/common/huntAbout'
import { getLeadingEntList, getMultiLeadingEntList } from '@/api/supplyChain/index.js'
import { asideOptions } from './busOptions.js'

const { proxy } = getCurrentInstance()
const props = defineProps({
  openNow: {
    type: Boolean,
    default: true,
  },
  //展示全部产业还是全部行业  false 展示行业
  showChain: {
    type: Boolean,
    default: false,
  },

  filterkEY: {
    // 排除高亮
    type: Array,
    default: () => [],
  },
  // 关键字 高亮公司名称
  highlightQuery: {
    type: String,
    default: undefined,
  },
  special: {
    //用于做一些个性化需求
    type: String,
    default: '',
  },
  ent_name: {
    type: String,
    default: '',
  },
})

const route = useRoute()
const emits = defineEmits(['clickItem'])

const actions = [
  { text: '默认排序', value: '' },
  { text: '成立日期从晚到早', value: 'ESDATE$DESC' },
  { text: '成立日期从早到晚', value: 'ESDATE$ASC' },
  { text: '注册资本从高到低', value: 'REGCAP$DESC' },
  { text: '注册资本从低到高', value: 'REGCAP$ASC' },
]

const total = ref(0)
const entList = ref([])
const disabled = ref(false)
const loading = ref(false) //是否处于加载状态
const finished = ref(false) //是否已加载完成
const refreshing = ref(false) //下拉刷新加载
const noData = ref(false)

const refreshParams = ref({
  page_index: 1,
  page_size: 10,
}) // 刷新参数
const heightParams = ref({}) //高级搜索参数
const chainCodes = ref([])
const zhanWeiRef = ref(null) //拿到列表高度
const listHeight = ref(0)
const showPopover = ref(false)
const orderby = ref(actions[0])

const onRefresh = () => {
  _getLeadingEntList({ ...refreshParams.value, page_index: 1, page_size: 10 })
}
const loadmore = function () {
  if (!entList.value.length) return
  if (total.value <= entList.value.length) {
    finished.value = true
    return
  }
  refreshParams.value.page_index += 1
  _getLeadingEntList(refreshParams.value)
}
const _getLeadingEntList = function (screenParams = {}) {
  const { areas = [], page_index, page_size, chain_codes = [], types = [], levels = [], ckfw = [] } = screenParams

  const params = {
    ent_id: decodeURIComponent(route.query.ent_id),
    is_faucet: ckfw.includes('is_faucet'),
    is_local: ckfw.includes('is_local'),
    is_market: ckfw.includes('is_market'),
    page_size,
    page_index,
    codes: areas,
    industry_codes: chain_codes,
    levels,
    types,
  }
  if (props.ent_name) {
    params['ent_name'] = props.ent_name
  }
  if (orderby.value?.value) {
    const [name, order] = orderby.value.value.split('$')
    params['sort'] = { name, order }
  }
  loading.value = true
  noData.value = false

  const fn = route.query?.isMulti ? getMultiLeadingEntList : getLeadingEntList

  fn({
    ...params,
  })
    .then(res => {
      const aryList = res.datalist || []
      aryList.forEach(item => {
        const tags = []
        if (item.ptype == 1) {
          tags.push(`上游企业-${item.ptype}级`)
        } else {
          tags.push(`下游企业-${item.ptype}级`)
        }
        if (item.is_local) {
          tags.push('本地企业')
        }
        if (item.is_faucet) {
          tags.push('龙头企业')
        }
        if (item.is_market) {
          tags.push('上市企业')
        }

        item.tags = [...tags, ...item.tags] // 将新标签添加到原标签数组的前面
      })
      if (params.page_index === 1) {
        entList.value = aryList
      } else {
        entList.value.push(...aryList)
      }
      total.value = res.count || 0
      noData.value = !res.count
      if (total.value === 0) {
        finished.value = true
      }
      proxy.$close()
    })
    .finally(() => {
      loading.value = false
      refreshing.value = false
    })
}

const visibleChange = visible => {
  disabled.value = visible
}
// 下拉筛选
const onOrderSelect = item => {
  orderby.value = item
  showPopover.value = false
  _getLeadingEntList({ ...refreshParams.value, page_index: 1, page_size: 10 })
}

watch(
  () => props.ent_name,
  val => {
    const oldParams = { ...refreshParams.value, ent_name: val, page_index: 1, page_size: 10 }
    _getLeadingEntList(oldParams)
  },
  { immediate: true }
)

// 弹窗回调
const filterSubmit = (obj, str) => {
  console.log('filterSubmit', obj)
  entList.value = []
  total.value = 0
  finished.value = false
  let objs = handlestructure(obj.params)
  let paramsss = { ...objs, page_index: 1, page_size: 10 }
  refreshParams.value = paramsss
  _getLeadingEntList(paramsss)
}
onMounted(() => {
  // proxy.$loading('加载中...')
  nextTick(() => {
    // 计算列表滚动高度
    const { height: h } = useWindowSize()
    const { top, height } = useElementBounding(zhanWeiRef, { immediate: true, windowResize: true }) //第一次拿不到
    listHeight.value = h.value - top.value - height.value - 48
  })
})
</script>

<style lang="scss" scoped>
.leading-page {
  height: 100%;
  background: #f7f7f7;
  .dropdown {
    // border-top: 2px solid #eee;
    display: flex;
    height: var(--van-dropdown-menu-height);
  }
  .list {
    // height: calc(100vh - 280px);

    overflow: auto;
    -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
  .total {
    font-size: 28px;
    line-height: 96px;
    height: 96px;
    color: #74798c;
    padding-left: 24px;
    .num {
      color: #2a72ff;
      margin: 0 8px;
      font-weight: 600;
    }
  }
}
.noData {
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 230px;
    height: 230px;
    transform: translateY(-180px);
  }
  div {
    color: #74798c;
    margin-top: 24px;
    transform: translateY(-180px);
  }
}
.null {
  height: 20px;
}
</style>
