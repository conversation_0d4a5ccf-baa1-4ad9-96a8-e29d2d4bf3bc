<template>
  <div :id="id" class="chart-container"></div>
</template>

<script setup>
import { ref, watch, reactive } from 'vue'
import * as echarts from 'echarts'
import { setPx } from '@/utils/height'

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
  id: {
    type: String,
    default: '',
  },
})
const chartInstance = ref(null)

const chartData = reactive({
  grid: {
    left: setPx(40),
    top: setPx(50),
    right: setPx(52),
    bottom: setPx(30),
    containLabel: true,
  },
  legend: {
    show: false,
    itemWidth: 12,
    itemHeight: 12,
    itemGap: 20,
    textStyle: {
      lineHeight: 0,
      padding: [2, 0, 0, 0],
    },
  },
  tooltip: {
    show: false,
  },
  xAxis: {
    type: 'category',
    boundaryGap: true,
    axisTick: {
      show: false,
    },
    axisLabel: {
      color: '#6F6F79',
    },
    data: [],
  },
  yAxis: {
    type: 'value',
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      color: '#3D3D47',
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#EEEEEE',
      },
    },
  },
  series: [
    {
      type: 'bar',
      barWidth: setPx(32),
      label: {
        show: true,
        position: 'top',
        offset: [0, 0],
        color: '#4AB8FF',
      },
      itemStyle: {
        color: '#4AB8FF',
      },
      data: [],
    },
  ],
})
watch(
  () => props.data,
  newVal => {
    newVal && initMap(newVal)
  },
  {
    deep: true,
  }
)
const initMap = function (option) {
  chartInstance.value = echarts.init(document.querySelector(`#${props.id}`))
  chartData.xAxis.data = option.legend
  chartData.series[0].data = option.value
  chartInstance.value.setOption(chartData)
}
</script>

<style>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
