<template>
  <div class="steps">
    <div v-for="item in data" class="mb-40 step-item">
      <div class="step-line">
        <svg-icon name="dot" class="dot"></svg-icon>
        <div class="line"></div>
      </div>
      <div class="step-item-content">
        <div class="flex h-32 mb-16 text-24 text-secondary">
          <div class="time">{{ item.invest_date }}</div>
          <div class="ml-16">{{ item.invest_round_name }}</div>
        </div>
        <div class="px-24 py-20 bg-grey">
          <span class="font-semibold text-primary text-28">
            {{
              !item.invest_detail_money || item.invest_detail_money === '0'
                ? '未透露'
                : transformUnit(item.invest_detail_money + '0000') + item.invest_currency_name
            }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
})

const transformUnit = (n, isInt) => {
  if (!n || isNaN(n)) return isInt ? '0' : ''

  if (typeof n !== 'number') n = Number(n)

  if (Math.abs(n) >= 100000000) {
    return (n / 100000000).toFixed(2) + '亿'
  } else if (Math.abs(n) >= 10000) {
    return (n / 10000).toFixed(2) + '万'
  } else {
    return isInt ? `${n}` : n.toFixed(2)
  }
}
</script>

<style scoped lang="scss">
.steps {
  padding-left: 48px;
  position: relative;

  & > div:last-of-type {
    .step-line {
      .line {
        height: 0;
      }
    }
  }

  .step-item {
    position: relative;
  }

  .step-line {
    position: absolute;
    left: -40px;
    top: 10px;
    .dot {
      width: 16px;
      height: 16px;
    }

    .line {
      height: 148px;
      border-left: 2px dashed #dedede;
      left: 6px;
    }
  }

  .step-item-content {
    .time {
      color: #74798c;
    }
  }
}
</style>
