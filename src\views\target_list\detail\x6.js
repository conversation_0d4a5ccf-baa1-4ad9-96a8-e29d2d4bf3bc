import { Graph } from '@antv/x6';

// 中心主题
Graph.registerNode(
  'topic',
  {
    inherit: 'rect',
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'text',
        selector: 'label',
      },
    ],
    attrs: {
      body: {
        rx: 3,
        ry: 3,
        fill: '#53A2FF',
        strokeWidth: 0,
      },
      label: {
        fontSize: 16,
        fill: '#fff',
      },
    },
  },
  true,
);

const moreNode = name => ({
  inherit: 'rect',
  markup: [
    {
      tagName: 'rect',
      selector: 'body',
    },
    {
      tagName: 'rect',
      selector: 'wrapper',
      style: {
        cursor: 'pointer',
      },
    },
    {
      tagName: 'text',
      selector: 'label',
      style: {
        cursor: 'pointer',
      },
    },
  ],
  attrs: {
    body: {
      rx: 8,
      ry: 8,
      strokeWidth: 0,
    },
    wrapper: {
      ref: 'body',
      refX: name === 'more-left' ? 87 : 0,
      width: 113,
      height: 36,
      fill: '#E7ECF5',
      strokeWidth: 0,
      event: 'click:more',
    },
    label: {
      ref: 'wrapper',
      width: 113,
      height: 36,
      fontSize: 16,
      fill: '#20263A',
      event: 'click:more',
    },
  },
});

Graph.registerNode('more-left', moreNode('more-left'), true);
Graph.registerNode('more-right', moreNode('more-right'), true);

// 边
Graph.registerEdge(
  'mindmap-edge',
  {
    inherit: 'edge',
    router: {
      name: 'manhattan',
      args: {
        step: 30,
      },
    },
    attrs: {
      line: {
        targetMarker: '',
        stroke: '#2A86F1',
        strokeWidth: 1,
      },
    },
    zIndex: 0,
  },
  true,
);
