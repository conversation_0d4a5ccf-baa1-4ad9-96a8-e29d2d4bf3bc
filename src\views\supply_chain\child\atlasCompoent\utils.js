// 删除节点
export function deleteChild(cla) {
  var _element = document.querySelector(cla)
  var _parentElement = _element?.parentNode
  if (_parentElement) {
    _parentElement.removeChild(_element)
  }
}
export const setPx = val => {
  const width = window.innerWidth
    ? window.innerWidth
    : document.documentElement.clientWidth
    ? document.documentElement.clientWidth
    : screen.width
  return (parseInt(val) * width) / 375
}
export async function handleCss() {
  document.getElementsByClassName('van-toast--loading')[0]?.classList.add('van-popup--center-cover')
  document.getElementsByClassName('van-popup--center')[0]?.classList.add('van-popup--center-cover')
  console.log(document.getElementsByClassName('van-popup--center')[0]?.classList)
  onUnmounted(() => {
    document.getElementsByClassName('van-toast--loading')[0]?.classList.remove('van-popup--center-cover')
    document.getElementsByClassName('van-popup--center')[0]?.classList.remove('van-popup--center-cover')
    console.log('移除：', document.getElementsByClassName('van-popup--center')[0]?.classList)
  })
}

const resetScreenHandle = () => {
  const body = document.getElementsByTagName('body')[0]
  const html = document.getElementsByTagName('html')[0]
  const width = html.clientWidth
  const height = html.clientHeight
  const max = width > height ? width : height
  const min = width > height ? height : width
  body.style.transform = 'rotate(0deg)'
  body.style['transform-origin'] = '50% 50%'
  body.style.width = min + 'px'
  body.style.height = max + 'px'
  body.style['top'] = 0 + 'px'
  body.style['left'] = 0 + 'px'
  // document.documentElement.style.fontSize = width / 10 + 'px'
  // 处理toast样式
  document.getElementsByClassName('van-toast--loading')[0]?.classList.remove('van-popup--center-cover')
}
const forceLandscapeScreenHandle = callback => {
  // 处理toast样式
  document.getElementsByClassName('van-toast--loading')[0]?.classList.add('van-popup--center-cover')
  // 强制橫批
  const body = document.getElementsByTagName('body')[0]
  const html = document.getElementsByTagName('html')[0]
  const width = html.clientWidth
  const height = html.clientHeight
  const max = width > height ? width : height
  const min = width > height ? height : width
  function common() {
    body.style.width = max + 'px'
    body.style.height = min + 'px'
    body.style.transform = 'rotate(90deg)'
    body.style['transform-origin'] = '50% 50%'
    // body.style['postion'] = 'relative';
    body.style['top'] = (height - width) / 2 + 'px'
    body.style['left'] = 0 - (height - width) / 2 + 'px'
    // document.documentElement.style.fontSize = height / 10 + 'px'
  }
  common()
  var evt = 'onorientationchange' in window ? 'orientationchange' : 'resize'
  window.addEventListener(evt, function () {
    setTimeout(function () {
      callback && callback()
      common()
    }, 300)
  })
}

// eslint-disable-next-line no-unused-vars
const onWindowSizeChanged = () => {
  window.addEventListener('resize', forceLandscapeScreenHandle)
}
export function usePortrait() {
  forceLandscapeScreenHandle()
  onWindowSizeChanged()
  onMounted(() => {
    forceLandscapeScreenHandle()
  }),
    onUnmounted(() => {
      resetScreenHandle()
      window.removeEventListener('resize', forceLandscapeScreenHandle)
    })
}
