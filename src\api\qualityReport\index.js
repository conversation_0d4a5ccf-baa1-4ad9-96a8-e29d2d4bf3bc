import { Post, Get, Delete } from '@/request'
import { CHAIN_URL } from '../constants'

/**
 * 根据企业id获取企业工商信息
 */
export const getEntBusinessInfo = entId => {
  return Get(`${CHAIN_URL}/stocking/business/info/${entId}`)
}

/**
 * 根据企业id获取企业详情
 */

export const getEntDetail = entId => {
  return Get(`${CHAIN_URL}/stocking/queryStocking/${entId}`)
}

/**
 * 根据企业id获取年度规模效益等级
 */
export const getAnnualBenefit = entId => {
  return Get(`${CHAIN_URL}/stocking/annual/benefit/${entId}`)
}

/**
 * 根据企业id获取企业融资事件
 */
export const getEntFinancing = entId => {
  return Get(`${CHAIN_URL}/stocking/company/melting/infos/${entId}`)
}

/**
 * 根据企业id获取专利数量统计
 */
export const getPatentCount = entId => {
  return Get(`${CHAIN_URL}/stocking/static/patent/${entId}`)
}

/**
 * 根据企业id获取企业专利列表
 */
export const entPatentApi = `stocking/patent`
export const getEntPatent = data => {
  return Post(`${CHAIN_URL}/${entPatentApi}`, data)
}

///供应链关系
export const getEntSupplyChains = data => {
  return Post(`${CHAIN_URL}/stocking/supply/chains`, data)
}

///企业关系
export const getEntRelationChain = entId => {
  return Get(`${CHAIN_URL}/stocking/ent/connection/?ent_id=${entId}`)
}

export const getProjectScore = entId => {
  return Get(`${CHAIN_URL}/stocking/new/${entId}`)
}

/**
 * 获取项目情报 - 风险动态列表
 */
export const riskListingURL = `monitor/ent/risks`
export const getRiskListing = data => {
  return Post(riskListingURL, data)
}

/**
 * 获取项目情报 - 舆情动态列表
 */
export const opinionsListingURL = `monitor/ent/news`
export const getOpinionsListing = data => {
  return Post(opinionsListingURL, data)
}

// 企业详情 对外投资区域分布
export const getInvestAreaSpread = entId => {
  return Get(`${CHAIN_URL}/stocking/company/inv/region/analysis/${entId}`)
}

/**
 * 根据企业id获取企业对外投资行业占比
 */
export const getInvestIndustryRatio = entId => {
  return Get(`${CHAIN_URL}/stocking/company/inv/nic/analysis/${entId}`)
}

/**
 * 根据企业id获取企业财务报表信息
 */
export const getEntFinanceReport = entId => {
  return Get(`${CHAIN_URL}/stocking/finance/index/info/${entId}`)
}

/**
 * 根据企业id获取产业榜单
 */
export const getAuthorityList = data => {
  return Post(`${CHAIN_URL}/stocking/top/list`, data)
}

/**
 * 根据企业id获取企业对外投资列表
 */
export const InvestmentListApi = `stocking/company/investment`
export const getInvestmentList = data => {
  return Post(`${CHAIN_URL}/${InvestmentListApi}`, data)
}

/**
 * 获取风险动态列表每项的详情
 */
export const getRiskDetailUrl = data => {
  return Post(`${CHAIN_URL}/stocking/dimensionTpkDetailReport/detail`, data)
}
