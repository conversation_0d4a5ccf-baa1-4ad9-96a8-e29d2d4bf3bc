<template>
  <!-- Added comments -->
  <div class="pulldown" ref="wrapperRef">
    <div class="pulldown-scroller">
      <div class="pulldown-wrapper" v-if="pullDownRefresh">
        <div v-show="data.beforePullDown">
          <span>下拉刷新</span>
        </div>
        <div v-show="!data.beforePullDown">
          <div v-show="data.isPullingDown">
            <VanLoading :size="setPx(24) + 'px'">刷新中...</VanLoading>
          </div>
          <div v-show="!data.isPullingDown">
            <span>加载成功...</span>
          </div>
        </div>
      </div>
      <slot></slot>
      <div class="pullup-tips" v-if="pullUpLoad && !data.isPullingDown">
        <div v-if="!data.isPullUpLoad" class="before-trigger">
          <VanLoading type="spinner" :size="setPx(16) + 'px'" color="#999">加载中</VanLoading>
        </div>
        <div v-if="data.isPullUpLoad" class="after-trigger">
          <span class="pullup-txt">暂无更多</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import BetterScroll from 'better-scroll'
import { reactive, onMounted, toRefs, ref, nextTick, onUnmounted, watch } from 'vue'
import { setPx } from '@/utils/height'

const THRESHOLD = 70
const STOP = 45

const props = defineProps({
  probeType: {
    type: Number,
    default: 0,
  },
  pullDownRefresh: {
    type: Boolean,
    default: false,
  },
  pullUpLoad: {
    type: Boolean,
    default: false,
  },
  bounce: {
    type: Boolean,
    default: false,
  },
  isScrollY: {
    type: Boolean,
    default: true,
  },
  stopPropagation: {
    type: Boolean,
    default: false,
  },
  timeBounce: {
    type: Number,
    default: 800,
  },
})

const emit = defineEmits(['getScrollY', 'pullUpLoad', 'pullDownRefresh'])

const wrapperRef = ref(null)

const data = reactive({
  bscroll: null,
  beforePullDown: true,
  isPullingDown: false,
  isPullUpLoad: false,
  num: 30,
})

const init = () => {
  nextTick(() => {
    data.bscroll = new BetterScroll(wrapperRef.value, {
      observeDOM: true,
      probeType: props.probeType,
      click: true,
      bounce: props.bounce,
      useTransition: false,
      startX: 0,
      startY: 0,
      scrollX: !props.isScrollY,
      scrollY: props.isScrollY,
      bounceTime: props.timeBounce,
      stopPropagation: props.stopPropagation,
      bindToWrapper: true,
      pullDownRefresh: props.pullDownRefresh && {
        threshold: THRESHOLD,
        stop: STOP,
      },
      pullUpLoad: props.pullUpLoad && {
        threshold: 0,
      },
      disableTouch: false, // https://www.jianshu.com/p/bf9bbb4d872c
    })

    data.bscroll.on('scroll', pos => {
      emit('getScrollY', pos.y)
    })

    props.pullingDown &&
      data.bscroll.on('pullingDown', () => {
        data.isPullingDown = true
        emit('pullDownRefresh', () => {
          data.isPullingDown = false
          data.beforePullDown = false
          data.bscroll.finishPullDown()
          data.bscroll.refresh()
        })
      })

    props.pullingUp &&
      data.bscroll.on('pullingUp', () => {
        data.isPullUpLoad = true
        emit('pullUpLoad', () => {
          data.isPullUpLoad = false
          data.bscroll.finishPullUp()
          data.bscroll.refresh()
        })
      })
  })
}

onMounted(() => {
  init()
})

onUnmounted(() => {
  data.bscroll?.destroy()
})

watch(
  () => props.pullDownRefresh,
  newVal => {
    if (newVal) {
      data.beforePullDown = true
    }
  }
)

watch(
  () => props.pullUpLoad,
  newVal => {
    if (!newVal) {
      data.isPullUpLoad = false
    }
  }
)
// Define methods to be exposed
const methods = {
  // Refresh the scroll
  refresh() {
    nextTick(() => {
      data.bscroll && data.bscroll.refresh()
    })
  },
  // Finish pull up loading
  finishPullUp() {
    data.bscroll && data.bscroll.finishPullUp()
  },
  // Get the current scroll position
  getScrollY() {
    return data.bscroll ? data.bscroll.y : 0
  },
  // Scroll to the top of the page
  goTop(x = 0, y = 0, time = 2000) {
    data.bscroll && data.bscroll.scrollTo(x, y, time)
  },
  // Scroll to a specific element
  goTopEl(el, time, offsetX, offsetY, easing) {
    data.bscroll.scrollToElement(el, time, offsetX, offsetY, easing)
  },
  // Scroll to an element by its ID
  goTopById(id, time, offsetX, offsetY, easing) {
    data.bscroll.scrollToElement(document.getElementById(id), time, offsetX, offsetY, easing)
  },
}

// Expose data and methods
defineExpose({
  ...toRefs(data),
  ...methods,
  wrapperRef,
})
</script>

<style lang="scss" scoped>
$scroll-padding: 20px;
$scroll-color: #999;

.pulldown {
  position: relative;
  height: 100%;
  overflow: hidden;
}

.pulldown-list {
  padding: 0;
}

.pulldown-list-item {
  padding: 10px 0;
  list-style: none;
}

.pulldown-wrapper {
  position: absolute;
  width: 100%;
  padding: $scroll-padding;
  box-sizing: border-box;
  transform: translateY(-100%) translateZ(0);
  text-align: center;
  color: $scroll-color;
}

.pullup-tips {
  padding: $scroll-padding;
  text-align: center;
  color: $scroll-color;
}
</style>
