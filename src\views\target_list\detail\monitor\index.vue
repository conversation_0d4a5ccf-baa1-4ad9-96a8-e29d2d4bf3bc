<template>
  <div>
    <van-sticky class="mt-24">
      <div class="tabs">
        <van-tabs :active="active" @click-tab="onClickTab">
          <van-tab title="风险动态"> </van-tab>
          <van-tab title="舆情动态"></van-tab>
        </van-tabs>
      </div>
      <div class="dropdown">
        <DropDown :options="options" @change="changeDrop" />
      </div>
    </van-sticky>
    <div class="list">
      <PullDownList ref="listRef" :queryParams="params" :apiUrl="url" :requestAuto="false" requestType="page_index">
        <template #data="{ sourceData }">
          <div v-for="(item, index) in sourceData" :key="index">
            <div v-if="active === 0" class="bg-white border-b border-[#eee] py-32 px-24 " @click="handleToDetail(item)">
              <div class="flex justify-between items-center mb-16">
                <div
                  class="text-28 text-primary font-semibold w-600  overflow-hidden whitespace-nowrap overflow-ellipsis">
                  {{ item.alter_after }}
                </div>
                <SvgIcon v-if="item.type_of_detail === 'jump'" name="arrow" class="w-24 h-24" />
              </div>
              <p class="mb-12 text-24 text-secondary">
                风险等级： <span class="text-tertiary">{{ RISK_LEVEL_MAP[item.risk_level] }}</span>
              </p>
              <p class="mb-12 text-24 text-secondary">
                风险类别： <span class="text-tertiary">{{ item.risk_type_name }}</span>
              </p>
              <p class="text-24 mb-0 text-secondary">
                发布日期： <span class="text-tertiary">{{ item.created }}</span>
              </p>
            </div>

            <div v-if="active === 1" class="bg-white border-b border-[#eee] py-32 px-24 " @click="handleToDetail(item)">
              <div class="flex justify-between items-center mb-16">
                <div
                  class="text-28 text-primary font-semibold w-600  overflow-hidden whitespace-nowrap overflow-ellipsis">
                  {{ item.title }}
                </div>
                <SvgIcon v-if="item.url" name="arrow" class="w-24 h-24" />
              </div>
              <p class="mb-12 text-24 text-secondary">
                舆情倾向性： <span class="text-tertiary">{{ item.impact_name }}</span>
              </p>
              <p class="mb-12 text-24 text-secondary">
                舆情类别： <span class="text-tertiary">{{ item.cls_name }}</span>
              </p>
              <p class="text-24 mb-0 text-secondary">
                发布日期： <span class="text-tertiary">{{ item.created }}</span>
              </p>
            </div>
          </div>
        </template>
      </PullDownList>
    </div>
  </div>
</template>

<script setup>
import { options1, options2 } from './dist'
import DropDown from './dropDown.vue'
import FxItem from './FxItem.vue'
import PullDownList from '@/components/PullDownList/index.vue'
import { riskListingURL, opinionsListingURL, getRiskDetailUrl } from '@/api/qualityReport'

const RISK_LEVEL_MAP = {
  5: '高风险',
  4: '风险',
  3: '警示',
  2: '提示',
  1: '利好',
};

const props = defineProps({})
const active = ref(0)
const List = ref([])
const route = useRoute()
const listRef = ref(null)
const options1Copy = JSON.parse(JSON.stringify(options1))
const options2Copy = JSON.parse(JSON.stringify(options2))
// 切换就是操作这个参数
const initParams = { groupId: route.query.group_id, ent_id_in: [route.query.ent_id], investment_insight: false }
const filterParams = ref(JSON.parse(JSON.stringify(initParams)))
const params = ref({})
const options = ref(options1Copy)
const url = computed(() => {
  return active.value === 0 ? riskListingURL : opinionsListingURL
})

// methods
const onClickTab = function (val) {
  if (val.name == active.value) return
  if (val.name === 0) options.value = options1Copy
  if (val.name === 1) options.value = options2Copy
  active.value = val.name
  filterParams.value = JSON.parse(JSON.stringify(initParams))
  refreshList()
}

const changeDrop = function (val, str) {
  let temOjb = toRaw(filterParams.value)
  switch (str) {
    case 'LEVEL':
      temOjb.risk_level = val
      break
    case 'TYPE':
      temOjb.risk_cls = val
      break
    case 'TIME':
      if (val) {
        temOjb.end_date = val[0].split('$')[1]
        temOjb.start_date = val[0].split('$')[0]
      } else {
        delete temOjb['end_date']
        delete temOjb['start_date']
      }
      break
    case 'PREFER':
      temOjb.impact = val
      break
    case 'RTYPE':
      temOjb.new_cls = val
      break
    default:
      break
  }
  // 遍历 params 对象，删除值为空的字段
  for (const key in temOjb) {
    if (!temOjb[key]) {
      delete temOjb[key]
    }
  }
  filterParams.value = temOjb
  refreshList()
}

const refreshList = () => {
  const filterKeys =
    active.value === 0 ? ['ent_id_in', 'risk_level', 'risk_cls', 'end_date', 'start_date'] : ['ent_id_in', 'impact', 'new_cls', 'start_date', 'end_date']

  const temOjb = Object.keys(filterParams.value).reduce((obj, key) => {
    if (filterKeys.includes(key)) {
      obj[key] = filterParams.value[key]
    }
    return obj
  }, {})

  // params.value = temOjb
  listRef.value?.onUrlChangeRefreshFn(
    {
      ...temOjb,
      investment_insight: false,
    },
    url.value
  )
}

const handleToDetail = (record) => {
  if(active.value === 0) {
    if (record.type_of_detail !== 'jump') return;
    getRiskDetailUrl({ tpk: record.tpk, dimension: record.dimension }).then((res) => {
      res.url && window.open(res.url);
    });
  } else {
    record.url && window.open(record.url);
  }
}

onMounted(async () => {
  options.value = active.value === 0 ? options1Copy : options2Copy
  params.value = filterParams.value
  refreshList()
})
onUnmounted(() => {
  options.value.map(item => (item.value = ''))
})
</script>

<style lang="scss" scoped>
:deep(.van-tab--active) {
  color: #3e7bfa !important;
  background: none !important;
}

:deep(.van-tabs__line) {
  background: #3e7bfa;
}

.dropdown {
  border-top: 1px solid #eee;
  display: flex;
  height: 88px;
  margin-bottom: 20px;
}
</style>
