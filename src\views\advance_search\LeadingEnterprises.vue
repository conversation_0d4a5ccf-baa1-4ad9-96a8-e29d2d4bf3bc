<template>
  <!-- 只有龙头企业筛选是全部产业 其余列表筛选为行业 -->
  <div class="leading-page">
    <div class="dropdown" ref="zhanWeiRef">
      <DropDown
        @submit="filterSubmit"
        :oldData="heightParams"
        :huntHeight="huntHeight"
        isChain="eleseic_data"
        :filters="filters"
        isHunt
      />
    </div>
    <div v-if="showTotal" class="total">
      共找到<span class="num">{{ total }}</span
      >家<span>{{ text }}</span
      >企业
    </div>
    <div class="null" v-else></div>
    <div :style="{ height: listHeight + 'px' }" class="list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          @load="loadmore"
          offset="1"
          :immediate-check="false"
          :finished="finished"
          :finished-text="entList.length ? '没有更多了' : undefined"
        >
          <EntCard
            :entList="entList"
            :query="highlightQuery"
            :openNow="openNow"
            @visibleChange="visibleChange"
            @clickItem="item => emits('clickItem', item)"
            special
            type="noThreeAddCollect"
          />
        </van-list>
      </van-pull-refresh>
      <div class="noData" v-if="noData">
        <img src="@/assets/image/null.png" alt="" />
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup name="LeadingEnterprises">
import DropDown from '@/components/Hunt/component/dropDown/index.vue'
import EntCard from '@/components/EntCard/index.vue'
import { handlestructure, handleData } from '@/components/Hunt/common/huntAbout'
import { getLeadingEntList } from '@/api/advanceSearch/index.js'

const { proxy } = getCurrentInstance()
const props = defineProps({
  showTotal: {
    type: Boolean,
    default: true,
  },
  //企业数量文本展示
  text: {
    type: String,
    default: undefined,
  },
  searchParams: {
    //多选弹窗那种要把原始数据带上 不然回填不上
    type: Object,
    default: () => {},
  },
  openNow: {
    type: Boolean,
    default: true,
  },
  //展示全部产业还是全部行业  false 展示行业
  showChain: {
    type: Boolean,
    default: false,
  },
  filters: {
    //排除更多筛选 当前模块筛选项目
    type: Array,
    default: () => [],
  },
  filterkEY: {
    // 排除高亮
    type: Array,
    default: () => [],
  },
  // 关键字 高亮公司名称
  highlightQuery: {
    type: String,
    default: undefined,
  },
  special: {
    //用于做一些个性化需求
    type: String,
    default: '',
  },
  ent_name: {
    type: String,
    default: '',
  },
})

const route = useRoute()
const emits = defineEmits(['clickItem'])

let params = reactive({ page_index: 1, page_size: 10 })
const total = ref(0)
const entList = ref([])
const disabled = ref(false)
const loading = ref(false) //是否处于加载状态
const finished = ref(false) //是否已加载完成
const refreshing = ref(false) //下拉刷新加载
const noData = ref(false)
const refreshParams = ref({})
const heightParams = ref({}) //高级搜索参数
const chainCodes = ref([])
const zhanWeiRef = ref(null) //拿到列表高度
const listHeight = ref(0)
const huntHeight = ref(0)

const onRefresh = () => {
  params.page_index = 1
  _getLeadingEntList(refreshParams.value)
}
const loadmore = function () {
  if (!entList.value.length) return
  finished.value = false
  if (total.value <= entList.value.length) {
    finished.value = true
    return
  }
  refreshParams.value.page_index += 1
  _getLeadingEntList(refreshParams.value)
}
const _getLeadingEntList = function (screenParams = {}) {
  if (screenParams?.isHeightParams) {
    delete screenParams['isHeightParams']
  }
  if (screenParams?.type === 'search') {
    delete screenParams['type']
    if (screenParams['regionData']) {
      delete screenParams['regionData']
    }
    screenParams['ent_name'] = props.ent_name
  }
  // 特殊需求 选择外资的时候 企业类型也要传值
  if (screenParams?.['foreign']?.length) {
    screenParams['ent_type'] = [...new Set([...screenParams['ent_type'], ...screenParams['foreign']])]
  }
  if (screenParams?.contactWays?.length) {
    const bol = screenParams.contactWays[0] === 'true'
    if (bol) {
      screenParams.contactWays = true
      screenParams['phone_data'] = screenParams?.['phone_data'] ? screenParams['phone_data'] : ['PHONE']
      screenParams['tel_data'] = screenParams?.['tel_data'] ? screenParams['tel_data'] : ['TEL']
    } else {
      screenParams.contactWays = false
      screenParams['phone_data'] = screenParams?.['phone_data'] ? screenParams['phone_data'] : ['NO_PHONE']
      screenParams['tel_data'] = screenParams?.['tel_data'] ? screenParams['tel_data'] : ['NO_TEL']
    }
  }
  loading.value = true
  noData.value = false
  // 过滤参数
  if (screenParams['register_time']?.length) {
    screenParams['register_time'].forEach(i => {
      delete i['name']
    })
  }
  // const fn = route.query.metaTitle ? getLeadingEntList :''
  const fn = getLeadingEntList

  fn({
    ...screenParams,
  })
    .then(res => {
      if (screenParams.page_index === 1) {
        entList.value = res.items || []
      } else {
        entList.value.push(...res.items)
      }
      total.value = res.count || 0
      noData.value = !res.count
      if (total.value === 0) {
        finished.value = true
      }
      proxy.$close()
    })
    .finally(() => {
      loading.value = false
      refreshing.value = false
    })
}

const visibleChange = visible => {
  disabled.value = visible
}
// 监听searchParams filters
// watch(
//   () => [props.searchParams, props.filterkEY],
//   ([params, arr]) => {
//     // 这种设计这里不管怎么都会触发 所以不行
//   },
//   {
//     immediate: true,
//     deep: true,
//   }
// )
watch(
  () => props.ent_name,
  val => {
    console.log('ccprops.ent_namec')
    const oldParams = { ...refreshParams.value, ent_name: val, page_index: 1, page_size: 10 }
    _getLeadingEntList(oldParams)
  }
)
watch(
  () => props.searchParams,
  val => {
    if (props.filterkEY) {
      let inObj = JSON.parse(JSON.stringify(val))
      Object.keys(inObj).length &&
        Object.keys(inObj).filter(i => {
          if (props.filterkEY.includes(i)) {
            delete inObj[i]
          }
        })
      heightParams.value = inObj
    }
    // console.log('props.searchParams', val)
    let oldParams = JSON.parse(JSON.stringify(val))
    oldParams = JSON.parse(handleData(oldParams))
    delete oldParams['regionData']
    delete oldParams['eleseic_data']
    delete oldParams['enttype_data']
    delete oldParams['all_cert_data']
    delete oldParams['chain_codes_data']
    let obj = {}
    for (let key in oldParams) {
      obj[key] = oldParams[key]
    }

    obj = oldParams?.isHeightParams ? handlestructure(oldParams) : oldParams
    obj = { ...obj, page_index: 1, page_size: 10 }
    refreshParams.value = obj
    _getLeadingEntList(obj)
  },
  {
    immediate: true,
    deep: true,
  }
)
// 弹窗回调
const filterSubmit = (obj, str) => {
  // console.log('filterSubmit', obj)
  entList.value = []
  total.value = 0
  finished.value = false
  let objs = handlestructure(obj.params)
  let paramsss
  if (props.searchParams?.isHeightParams) {
    // 从高级搜索过来  ---顺序需要考虑
    paramsss = { ...objs, page_index: 1, page_size: 10 }
  } else if (props.searchParams?.type === 'search') {
    // console.log(param, props.ent_name)
    paramsss = { ...objs, ent_name: props.ent_name, page_index: 1, page_size: 10 }
  } else {
    //其它企业
    paramsss = { ...objs, page_index: 1, page_size: 10 }
  }
  refreshParams.value = paramsss
  _getLeadingEntList(paramsss)
}
onMounted(() => {
  proxy.$loading('加载中...')
  nextTick(() => {
    // 计算列表滚动高度
    const { height: h } = useWindowSize()
    const { top, height } = useElementBounding(zhanWeiRef, { immediate: true, windowResize: true }) //第一次拿不到
    listHeight.value = h.value - top.value - height.value - (props.showTotal ? 48 : 10)
    huntHeight.value = h.value - top.value - height.value
  })
})
</script>

<style lang="scss" scoped>
.leading-page {
  height: 100%;
  background: #f7f7f7;
  .dropdown {
    // border-top: 2px solid #eee;
    display: flex;
    height: var(--van-dropdown-menu-height);
  }
  .list {
    // height: calc(100vh - 280px);

    overflow: auto;
    -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
  .total {
    font-size: 28px;
    line-height: 96px;
    height: 96px;
    color: #74798c;
    padding-left: 24px;
    .num {
      color: #e72410;
      margin: 0 8px;
    }
  }
}
.noData {
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 230px;
    height: 230px;
    transform: translateY(-180px);
  }
  div {
    color: #74798c;
    margin-top: 24px;
    transform: translateY(-180px);
  }
}
.null {
  height: 20px;
}
</style>
