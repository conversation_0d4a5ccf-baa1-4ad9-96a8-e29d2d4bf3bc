<template>
  <div class="flex flex-col items-center mt-24 bg-white">
    <!-- 标题 -->
    <div class="w-full title">
      <span class="tit_span">{{ title }}</span>
    </div>
    <!-- 饼图 -->
    <div :style="{ width: '90%', height: '251px' }" :id="id" />
    <!-- 列表 -->
    <ul class="w-full px-56 mb-50">
      <li
        v-for="(item, index) in list"
        :key="index"
        class="relative justify-between px-24 f-y-center h-76 rounded-8"
        :class="[currentIndexS == index ? '!bg-[#F7F7F7]' : 'bg-white']"
        @click="handleH(index)"
      >
        <div class="text-24" :class="[currentIndexS == index ? '!text-[#20263A]' : 'text-[#74798c]']">
          <span class="yuan" :style="{ backgroundColor: item.color }"></span>
          {{ item.name }}
        </div>
        <div class="text-28" :class="[currentIndexS == index ? '!text-[#20263A]' : 'text-[#525665] ']">
          {{ item.percentage }}
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, reactive } from 'vue'
import * as echarts from 'echarts'
import { setPx } from '@/utils/height'

const colors = [
  '#E34848',
  '#FD9331',
  '#26C8A7 ',
  '#48CA74',
  '#4AB8FF',
  '#227FEB',
  '#9C85DB',
  '#DB85D2',
  '#F5B01D',
  '#1B86E9',
  '#0C9B8D',
  '#445565',
]
const currentIndexS = ref(0)
const oldIndexS = ref(0)
const list = ref([])
const props = defineProps({
  data: {
    type: Object,
    default: () => [],
  },
  title: {
    type: String,
    default: '供应链企业行业占比',
  },
  id: {
    type: String,
    default: 'pies',
  },
})
const chartInstance = ref(null)

const highlightPie = (currentIndex, oldIndex) => {
  chartInstance.value.dispatchAction({
    type: 'downplay',
    seriesIndex: 0,
    dataIndex: oldIndex,
  })
  chartInstance.value.dispatchAction({
    type: 'highlight',
    seriesIndex: 0,
    dataIndex: currentIndex,
  })
}
const initMap = function (source) {
  nextTick(() => {
    chartInstance.value = echarts.init(document.querySelector(`#${props.id}`))
    if (!source.length) {
      // 说明没得数据 -- 实在不行 画布向做偏移一下
      chartInstance.value.showLoading({
        text: '-暂无数据-',
        effectOption: { backgroundColor: 'transparent' },
        maskColor: 'rgba(255, 255, 255, 0.8)',
        textColor: '#8a8e91',
        showSpinner: false,
        spinnerRadius: 0,
      })
      return
    }
    chartInstance.value.hideLoading()
    chartInstance.value.on('click', params => {
      currentIndexS.value = params.dataIndex
      highlightPie(currentIndexS.value, oldIndexS.value)
      oldIndexS.value = currentIndexS.value
    })
    chartInstance.value.setOption(getOptions(source))
    if (source.length) {
      highlightPie(oldIndexS.value, 1)
    }
  })
}
watch(
  () => props.data,
  (newVal, oldVal) => {
    if (newVal.length) {
      const totalSum = newVal.reduce((acc, cur) => acc + +cur.total, 0) // 计算所有total的总和
      list.value = newVal.map((i, idx) => {
        const percentage = (i.total / totalSum) * 100 // 计算每个项目的百分比
        return {
          name: i.name,
          value: +i.total,
          percentage: percentage.toFixed(2) + '%', // 保留两位小数
          color: colors[idx],
        }
      })
    }
    initMap(newVal)
  },
  {
    deep: true,
    immediate: true,
  }
)

const getOptions = source => {
  return {
    backgroundColor: '#fff',
    tooltip: {
      show: false,
    },
    color: colors,
    series: [
      // 外部真实圆环
      {
        name: '心率分布',
        type: 'pie',
        radius: ['50%', '70%'],
        center: ['center', 'center'],
        data: list.value,
        // 还原内部文字
        label: {
          show: false, // 确保标签显示
          position: 'center', // 设置标签位置为中间
          formatter: function (params) {
            let b = params.name || '' // {b} 对应的数据名
            let c = params['data'].percentage || '' // {c} 对应的数据值

            // 超过 6 个字符自动换行
            if (b.length > 6) {
              b = b.slice(0, 6) + '\n' + b.slice(6)
            }

            // 返回格式化后的字符串
            return `{b_style|${b}}\n{c_style|${c}}`
          },
          rich: {
            b_style: {
              padding: [0, 0, 0, 0], // 设置上方文本的间距
              fontSize: 14, // 设置上方文本的字体大小
              fontWeight: 600, // 设置字体粗细
              color: ' #74798C', // 设置颜色
            },
            c_style: {
              padding: [10, 0, 0, 0], // 设置上方文本的间距
              fontSize: 18, // 设置下方文本的字体大小
              fontWeight: 600, // 设置字体为加粗
              color: '#20263A', // 设置颜色
            },
          },
        },
        emphasis: {
          label: {
            show: true,
          },
        },
        // 间隔
        itemStyle: {
          normal: {
            borderWidth: 2,
            borderColor: '#fff',
          },
        },
      },
      // 内部绿色装饰圆环
      {
        type: 'pie',
        tooltip: {
          show: false,
        },
        center: ['center', 'center'],
        radius: ['45%', '45%'],
        label: {
          show: false, // 不展示data中的value和name
        },
        data: [
          {
            value: 1, // 此处的值无所谓是多少
            name: '', // 因为不展示label，可不填
            itemStyle: {
              // 边框样式，浅蓝色，颜色可自行修改
              borderWidth: 2, // 边框宽度
              borderColor: '#F7F7F7 ', // 边框颜色
            },
          },
        ],
      },
    ],
  }
}
const handleH = index => {
  currentIndexS.value = index
  highlightPie(index, oldIndexS.value)
  oldIndexS.value = index
}
</script>

<style lang="scss" scoped>
.title {
  @apply h-88 px-24  flex items-center;

  .tit_span {
    @apply pl-12 font-600 relative text-#20263A text-28;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 28px;
      background: linear-gradient(180deg, rgba(42, 114, 255, 0.99) 0%, #3994ff 74%);
    }
  }
}

.yuan {
  @apply inline-block mr-8 w-24 h-24 rounded-[50%] overflow-hidden;
}
</style>
