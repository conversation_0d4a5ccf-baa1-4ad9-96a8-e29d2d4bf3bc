<template>
  <VanbPop
    v-bind="$attrs"
    :isFooter="false"
    position="center"
    :class="['self-pop-center', 'self-pop-center-toast', className]"
    @close="emits('close')"
  >
    <div class="toast-wrapper">
      <div class="header" v-if="name">
        <p class="name">{{ name }}</p>
<!--        <img v-if="showClose" @click="emits('close')" src="@/assets/img/form/close-icon.png" alt="关闭" class="close" />-->
      </div>
      <div class="content">
        <slot></slot>
      </div>
    </div>
  </VanbPop>
</template>
<script setup>
import { ref, watch } from 'vue'
import VanbPop from '@/components/Van/VanbPop/index.vue'

const emits = defineEmits(['close'])
const props = defineProps({
  // title名称
  name: {
    type: String,
    default: '',
  },
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: false,
  },
  className: {
    type: String,
  },
})

const val = ref(['12', '00'])

watch(
  () => props.selectValue,
  newVal => {
    val.value = newVal
  }
)

function submit() {
  emits('handleSelect', { value: val.value, name: val.value.join(':') })
}
</script>
<style lang="scss" scoped>
.toast-wrapper {
  width: 584px;
  padding: 32px 40px 48px;
  background: #ffffff;
  border-radius: 12px;
  .header {
    width: 100%;
    position: relative;
    .name {
      width: 100%;
      text-align: center;
      height: 40px;
      line-height: 40px;
      font-size: 28px;
      color: #010101;
    }
    .close {
      position: absolute;
      width: 32px;
      height: 32px;
      right: 0;
      top: 0;
    }
  }
  .content {
    width: 100%;
    height: auto;
  }
}
</style>
