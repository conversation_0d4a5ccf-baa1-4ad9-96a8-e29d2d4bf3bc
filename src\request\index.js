import axios from 'axios'
import { handleConfigureAuth, handleGeneralError, handleNetworkError } from './tool'
import { removeToken } from '@/utils/auth'
import { useUserStoreWithOut } from '@/store/modules/user'
import { getQueryString } from '@/utils'
import { hydrogenUrl, CHAIN_URL, BUSINESS_URL } from '@/api/constants'

const { isCancel } = axios
// 请求队列，缓存发出的请求
const cacheRequest = {}
// 不进行重复请求拦截的白名单
const cacheWhiteList = [
  `${CHAIN_URL}/recommended/ent/list&post`,
  `${BUSINESS_URL}/maps&post`,
  `${BUSINESS_URL}/maps/park&post`,
  `${CHAIN_URL}/stocking/supply/chains&post`,
  `${CHAIN_URL}/stocking/patent&post`,
] // ['/ch_monitor/v0.2/monitor/monitorEntsListDetail&post']

const instance = axios.create({
  timeout: 30000,
})
/**
 * @desc 删除缓存队列中的请求
 * @param {String} reqKey 本次请求的唯一标识 url&method
 */
function removeCacheRequest(reqKey) {
  if (cacheRequest[reqKey]) {
    // 通过AbortController实例上的abort来进行请求的取消
    cacheRequest[reqKey].abort()
    delete cacheRequest[reqKey]
  }
}

instance.interceptors.request.use(config => {
  config = handleConfigureAuth(config)
  // 24.5.22
  const { url, method } = config
  // 请求地址和请求方式组成唯一标识，将这个标识作为取消函数的key，保存到请求队列中
  const reqKey = `${url}&${method}`
  // 如果存在重复请求，删除之前的请求
  if (cacheWhiteList.indexOf(reqKey) === -1) {
    removeCacheRequest(reqKey)
    // 将请求加入请求队列，通过AbortController来进行手动取消
    const controller = new AbortController()
    config.signal = controller.signal
    cacheRequest[reqKey] = controller
  }

  return config
})

instance.interceptors.response.use(
  response => {
    // 请求成功，从队列中移除
    const { url, method } = response.config
    removeCacheRequest(`${url}&${method}`)

    if (response.status !== 200) {
      return Promise.reject(response.data)
    }
    const errorType = handleGeneralError(response.data)
    // console.log('===response.data', response.data)
    if (errorType === 1) {
      return Promise.resolve(response.data)
    } else if (errorType === 2) {
      const debug = getQueryString('debug')
      if (debug) {
        removeToken()
        window.location.reload()
      } else {
        const userStore = useUserStoreWithOut()
      }
    } else {
      return Promise.reject(response.data)
    }
  },
  err => {
    if (isCancel(err)) {
      // 通过CancelToken取消的请求不做任何处理
      return Promise.reject({
        message: '重复请求，自动拦截并取消',
      })
    } else {
      // 正常请求发生错误,抛出异常等统一提示
      handleNetworkError(err?.response?.status || '500')
    }
    return Promise.reject(err)
  }
)
export const Get = (url, params = {}, headers = {}) =>
  new Promise((resolve, reject) => {
    instance
      .get(url, {
        params: {
          ...params,
        },
        headers,
      })
      .then(result => {
        resolve(result.data)
      })
      .catch(err => {
        reject(err)
      })
  })

export const Post = (url, data = {}, headers = {}) => {
  return new Promise((resolve, reject) => {
    instance
      .post(url, data, {
        headers,
      })
      .then(result => {
        resolve(result.data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

export const Put = (url, data = {}, headers = {}) => {
  return new Promise((resolve, reject) => {
    instance
      .put(url, data, {
        headers,
      })
      .then(result => {
        resolve(result.data)
      })
      .catch(err => {
        reject(err)
      })
  })
}

export const Delete = (url, params = {}, headers = {}) =>
  new Promise((resolve, reject) => {
    instance
      .delete(url, {
        params: {
          ...params,
        },
        headers,
      })
      .then(result => {
        resolve(result.data)
      })
      .catch(err => {
        reject(err)
      })
  })
