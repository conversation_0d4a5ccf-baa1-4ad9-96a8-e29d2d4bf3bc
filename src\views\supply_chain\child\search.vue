<template>
  <div class="cur_container h_container">
    <div class="search">
      <SearchInput
        v-model="inputValue"
        @clear="handleClear"
        @change="debounceChange"
        :showCancel="false"
        :placeholder="isMulti ? '请输入目标企业' : '请输入供应链名称'"
        @cancel="cancel"
        :disabled="false"
      />
    </div>
    <div class="list">
      <div
        v-for="item in allList"
        :key="item.ent_id"
        class="h-88 bg-white justify-between f-y-center sle border-b border-solid border-[#eee]"
        @click="handleClick(item)"
      >
        <div class="flex-1 sle" v-if="curId !== item.ent_id">
          <HightlightText :query="inputValue" :text="item.ent_name" />
        </div>
        <div class="flex-1 sle text-28 text-[#2A72FF] font-600" v-if="curId === item.ent_id">
          {{ item.ent_name }}
        </div>
        <img src="@/assets/image/gou.png" class="w-32 h-32" v-if="curId === item.ent_id" />
      </div>
    </div>
    <div class="noData" v-if="noData">
      <img src="@/assets/image/null.png" alt="" />
      <div>暂无内容</div>
    </div>

    <!-- 按钮 后面高度有问题可以用固定定位 -->
    <div class="btn">
      <div class="bg-cancel mr-11" @click="cancel">取消</div>
      <div class="ml-11 bg-confirm text-white" :class="[curId ? 'opacity-100' : 'opacity-50']" @click="sure">
        {{ isMulti ? '确认' : '查看供应链' }}
      </div>
    </div>
  </div>
</template>

<script setup name="AuthorityList">
import { reactive, onMounted, ref } from 'vue'
import SearchInput from '@/components/SearchInput/index.vue'
import HightlightText from '@/components/HightlightText/index.vue'
import { debounce } from '@/utils/index'
import { getGonYinList } from '@/api/supplyChain/index.js'
import { useRoute } from 'vue-router'
import { useMultiList } from '../hook/multiListHook.js'
import { pageChangeHandler } from '@/utils'
import { backHandler } from '@/utils'

const { addItem, isTenItem } = useMultiList()
const router = useRouter()
const route = useRoute()
const { proxy } = getCurrentInstance()
const noData = ref(false)
const inputValue = ref('')
const curId = ref() // 点击选中的列表
const curObj = ref({})

const allList = ref([])
const isMulti = ref(false)

const _getAuthorityList = function () {
  noData.value = false
  if (!inputValue.value.trim()) {
    allList.value = []
    curId.value = ''
    return
  }
  getGonYinList(inputValue.value).then(res => {
    allList.value = res
    if (!allList.value.length) {
      noData.value = true
    }
  })
}

const handleClear = () => {
  inputValue.value = ''
}
const cancel = () => {
  inputValue.value = ''
  curId.value = ''
  if (isMulti.value) {
    // 多选就设置一个参数返回的时候拿到tab好切换
    window.sessionStorage.setItem('isMulti', true)
  }
  backHandler()
}
const sure = () => {
  if (!curId.value) {
    return
  }
  if (isMulti.value) {
    // 多链
    const bol = addItem(curObj.value)
    if (!bol) {
      proxy.$showToast('该企业已添加!')
      return
    }
    window.sessionStorage.setItem('isMulti', true)
    backHandler()
    return
  }
  // 单链
  pageChangeHandler('/single-detail', { ent_id: curId.value, title: curObj.value.ent_name })
}
const debounceChange = debounce(val => {
  allList.value = []
  _getAuthorityList()
}, 500)

// 点击选中的列表
const handleClick = item => {
  curId.value = item.ent_id
  curObj.value = item
}

onMounted(() => {
  const { name = '' } = route.query
  if (name) {
    // 说明是多链主招商
    isMulti.value = true
  }
  _getAuthorityList()
})
</script>

<style lang="scss" scoped>
.cur_container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;

  .search {
    flex-shrink: 0;
    padding: 20px 24px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
  }

  .list {
    // height: calc(100vh - 207px);
    // overflow: auto;
    flex: 1;
    padding: 24px;
  }
}

.btn {
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom) + 20px);
  display: flex;
  width: 100%;
  padding: 0 24px;
  flex-shrink: 0;

  div {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;
    width: 340px;
    font-size: 32px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #20263a;
    border-radius: 8px;
    overflow: hidden;
  }
}
.noData {
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    transform: translateY(-200px);
    width: 230px;
    height: 230px;
  }
  div {
    transform: translateY(-200px);
    color: #74798c;
    margin-top: 24px;
  }
}
</style>
