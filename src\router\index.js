import { nextTick } from 'vue'
import { createRouter, create<PERSON>emoryHistory, createWebHistory } from 'vue-router'
// import { qiankunWindow } from 'vite-plugin-qiankun/dist/helper'
import  { useCommonInfoWithOut } from '@/store/modules/commonInfo'
import { pageChangeHand<PERSON>, getTokenHandler } from '@/utils'
import pageConfig from './page.config'

export const routes = [
  {
    path: '/',
    redirect: '/home',
    component: () => import('@/layout/page.vue'),
    children: pageConfig,
  },
  {
    name: 'login',
    path: '/login',
    component: () => import('@/views/login/index.vue'),
  },
  {
    path: '/:pathMatch(.*)',
    redirect: pageConfig[0], // todo PAGE_NOT_FOUND
  },
]
let commonInfoStore = null
async function commonInfoStoreHandler() {
  await commonInfoStore.getPresetInfo()
  // await commonInfoStore.getAreaInfo();
}

function permissionGuard(router) {
  commonInfoStore = useCommonInfoWithOut()
  const token = getTokenHandler();
  // console.log("token111:",token);

  router.beforeEach(async (to, from, next) => {
    if (!commonInfoStore.presetInfo && token.access_token) {
      try {
        await commonInfoStoreHandler()
      } catch (e) {
        // console.log(e)
      }
    }
    // if(!token.access_token) {
    //   console.log(8888)
    //   next('/login')
    // }
    // if (qiankunWindow.__POWERED_BY_QIANKUN__ && location.pathname !== to.href) {
    //   next(false)
    //   if (qiankunWindow.activeRule === location.pathname) {
    //     nextTick(() => {
    //       pageChangeHandler(to.path, null, true)
    //     })
    //   }
    //   return true
    // }
    if (!to.matched.length) {
      next('/404')
    }
    next()
  })
  return router
}

export function routerHandler() {
  return permissionGuard(
    createRouter({
      scrollBehavior(to, from, savedPosition) {
        return { top: 0 }
      },
      // history: base ? createMemoryHistory(base) : createWebHistory(),
      history: createWebHistory('/hdzhzs/'),
      routes,
    })
  )
}
