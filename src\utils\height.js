import { ref, watch } from 'vue'
const useBindRef = () => {
  const { width, height } = useWindowSize()
  const domRect = ref('')
  const domRef = ref(null)
  watch(domRef, val => {
    domRef.value = val
    domRect.value = domRef.value.$el.getBoundingClientRect()
  })

  return {
    domRef,
    domRect: domRect,
    width: width,
    height: height,
  }
}

const setPx = val => {
  const width = window.innerWidth
    ? window.innerWidth
    : document.documentElement.clientWidth
    ? document.documentElement.clientWidth
    : screen.width
  return (parseInt(val) * width) / 750
}

export { useBindRef, setPx }
