<script setup name="DropDown">
import MultiplecChoice from '@/components/Hunt/component/MultiplecChoice/btmIndex.vue'
import ChainPop from '@/components/Hunt/component/chainPop/BtmCom.vue'
import Hunt from '@/components/Hunt/index.vue'
import RadioSelect from '../radioSelect/index.vue'
import { reactive, watch, ref, nextTick, toRaw } from 'vue'
import { handleData, getHeightStatus, getNameFromPop, handleMultiple } from '../../common/huntAbout.js'
import { useRoute } from 'vue-router'

const route = useRoute()
const emits = defineEmits(['submit'])
const dropRef = ref(null)
const huntRef = ref(null)
const TwoMap = {
  chain_codes: {
    name: '所属产业',
    dataType: 'AllCertAry',
    type: 'chain_codes_data', //往外传的字段
  },
  eleseic_data: {
    name: '全部行业',
    // name: '所属行业',
    dataType: 'EleseicAry',
    type: 'eleseic_data', //往外传的字段
  },
  // 区领导对口联系服务重点企业
  cong_hua_ket_ent_flag: {
    name: '区领导对口联系服务重点企业',
    dataType: [
      { name: '重点企业', value: true },
      { name: '非重点企业', value: false },
    ], //数据源
    type: 'cong_hua_ket_ent_flag', //往外传的字段
  },
}
// ps 回显地区，产业链，行业，要原始数据
const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  isChain: {
    //这里改版了 还有其它情况
    type: String,
    default: 'chain_codes', //chain_codes:产业 , trade_types:行业，针对这次单多选
  },
  oldData: {
    type: Object,
    default: () => {},
  },
  filters: {
    type: Array,
    default: () => [],
  },
  filterH: {
    //更多筛选默认高度
    type: Number,
    default: 600,
  },
  asideList: {
    type: Array,
    default: undefined,
  },
  searchTermList: {
    type: Array,
    default: undefined,
  },
  hideMore: {
    type: Boolean,
    default: false,
  },
  huntHeight: {
    type: Number,
    default: 480,
  },
  isHunt: {
    type: Boolean,
    default: false,
  },
})

let filter = reactive([])
const options = reactive(
  [
    {
      // name: '所属地区',
      name: '全国',
      newName: '',
      mark: 'areas',
      dataType: 'DistrictAry',
      oldData: [],
      visible: false,
      isHeight: false,
    },
    {
      name: TwoMap[props.isChain].name,
      newName: '',
      mark: props.isChain,
      dataType: TwoMap[props.isChain].dataType,
      oldData: [], // 这里回弹时用  这里因为要适配多选 就是当前选中的原本数据 ，如果是单选从 最外面进来 需要自己组装一下数据 TwoMap[props.isChain].dataType弄成选项里面的样子
      visible: false,
      isHeight: false,
    },
    props.hideMore
      ? undefined
      : {
          name: '更多筛选',
          special: true,
          visible: false,
          isHeight: false,
          oldData: {}, //对象
        },
  ].filter(Boolean)
)
const temParams = reactive({
  isHeight: false,
  oldData: {},
})
// 最终往外传的参数
const allResult = str => {
  let obj = JSON.parse(JSON.stringify(options?.[2]?.oldData || []))
  obj['regionData'] = options[0].oldData
  obj[TwoMap[props.isChain].type] = options[1].oldData

  if (obj?.cong_hua_ket_ent_flag?.length) {
    //这个数据单独处理
    obj['cong_hua_ket_ent_flag'] = obj.cong_hua_ket_ent_flag[0].value
  } else if (obj.cong_hua_ket_ent_flag && !obj.cong_hua_ket_ent_flag.length) {
    delete obj.cong_hua_ket_ent_flag
  }
  let params = JSON.parse(handleData(obj))

  delete params['regionData']
  delete params['eleseic_data']
  delete params['enttype_data']
  delete params['all_cert_data']
  delete params['chain_codes_data']
  emits('submit', { params }, str)
}
// 关闭所有弹窗
const closeAllPop = () => {
  dropRef.value.forEach(i => i.toggle(false))
}
//弹窗关闭回调
const closed = (item, index) => {
  options[index].visible = false
}
// 弹窗打开回调
const opened = (item, index) => {
  options[index].visible = true
  if (index == 2 && Object.keys(item.oldData).length) {
    // 说明是更多筛选-回显
    nextTick(() => {
      huntRef.value[0].setBackfillData(JSON.parse(JSON.stringify(item.oldData)))
    })
  }
}
// 确定回调
const submitSub = ({ mark, checkedList }, str) => {
  let index = options.findIndex(option => option.mark === mark)
  if (index == -1) return
  let optionToUpdate = options[index]
  if (optionToUpdate) {
    optionToUpdate.oldData = checkedList
    optionToUpdate.isHeight = checkedList.length > 0
    optionToUpdate.newName = checkedList.length ? getNameFromPop(checkedList) : ''
  }
  options[index] = optionToUpdate
  closeAllPop()
  allResult(str)
}
// 确定回调
const closeSub = () => {
  closeAllPop()
}
//更多筛选确定
const submitParams = () => {
  if (!huntRef.value[0]?.checkoutSear(temParams.oldData)) {
    return
  }
  if (options?.[2]) {
    options[2].isHeight = temParams.isHeight
    options[2].oldData = JSON.parse(JSON.stringify(temParams.oldData))
  }
  closeAllPop()
  allResult('none')
}
const reset = () => {
  // 清空条件
  huntRef.value[0]?.clearSear()
  // if (options?.[2]) {
  //   options[2].isHeight = false
  //   options[2].oldData = {}
  // }
  // closeAllPop()
  // allResult('none')
}
const getParams = ({ isHeight, paramsData }) => {
  temParams.isHeight = isHeight
  temParams.oldData = paramsData
}
const ary = ['全国', TwoMap[props.isChain].name]
watch(
  () => props.filters,
  val => {
    // 数组去重
    const arr = Array.from(new Set(toRaw(val?.length ? [...ary, ...val] : ary)))
    filter = arr.map(i => {
      if (i === '全国') return '所在地区'
      if (i === '全部行业') return '所属行业'
      return i
    })
  },
  {
    immediate: true,
  }
)
watch(
  () => props.oldData,
  val => {
    nextTick(() => {
      let params = JSON.parse(JSON.stringify(val))
      if (val?.regionData?.length) {
        options[0].oldData = JSON.parse(JSON.stringify(val.regionData))
        options[0].isHeight = true
        options[0].newName = getNameFromPop(val.regionData)
        delete params['regionData']
        delete params['areas']
      }
      if (props.isChain === 'chain_codes' && val?.chain_codes_data?.length) {
        options[1].oldData = val.chain_codes_data
        options[1].isHeight = true

        delete params['chain_codes_data']
        delete params['chain_codes']
      }
      if (props.isChain === 'eleseic_data' && val?.eleseic_data?.length) {
        options[1].oldData = val.eleseic_data
        options[1].isHeight = true
        options[1].newName = getNameFromPop(val.eleseic_data)
        delete params['eleseic_data']
        delete params['trade_types']
      }
      if (props.isChain === 'cong_hua_ket_ent_flag' && typeof val?.cong_hua_ket_ent_flag === 'boolean') {
        // 单选数据回填的情况
        options[1].oldData = val?.cong_hua_ket_ent_flag
          ? [TwoMap.cong_hua_ket_ent_flag.dataType[0]]
          : [TwoMap.cong_hua_ket_ent_flag.dataType[1]]
      }
      if (params?.isHeightParams) {
        delete params['isHeightParams']
      }
      if (options?.[2]) {
        options[2].oldData = params
        options[2].isHeight = getHeightStatus(params)
      }
    })
  },
  {
    immediate: true,
    deep: true,
  }
)
</script>

<template>
  <div class="wrapss">
    <van-dropdown-menu style="width: 100%; height: 100%" active-color="#076EE">
      <van-dropdown-item
        v-for="(item, index) in options"
        :key="index"
        @opened="opened(item, index)"
        @closed="closed(item, index)"
        ref="dropRef"
        :title-class="item.isHeight ? 'activeMenuItem' : ''"
      >
        <template #title>
          <span>{{ item.newName || item.name }}</span>
        </template>
        <template #default>
          <template v-if="index == 0">
            <MultiplecChoice
              :visible="item.visible"
              :mark="item.mark"
              :dataType="item.dataType"
              @submit="val => submitSub(val)"
              :oldData="item.oldData"
              @close="closeSub"
            />
          </template>
          <template v-if="index == 1">
            <!-- 全部行业 -->
            <template v-if="isChain === 'eleseic_data'">
              <MultiplecChoice
                :visible="item.visible"
                :mark="item.mark"
                :dataType="item.dataType"
                @submit="val => submitSub(val)"
                :oldData="item.oldData"
                @close="closeSub"
              />
            </template>
            <!-- 全部产业 -->
            <template v-if="isChain === 'chain_codes'">
              <ChainPop
                :visible="item.visible"
                :mark="item.mark"
                :dataType="item.dataType"
                @submit="val => submitSub(val, 'chain')"
                :oldData="item.oldData"
                @close="closeSub"
              />
            </template>
            <!-- 新增 是否重点企业 -->
            <template v-if="isChain === 'cong_hua_ket_ent_flag'">
              <RadioSelect
                :dataType="item.dataType"
                :mark="item.mark"
                :visible="item.visible"
                @submit="val => submitSub(val, 'cong_hua_ket_ent_flag')"
                type="name"
                :oldData="item.oldData"
                @close="closeSub"
              />
            </template>
          </template>
          <template v-if="index == 2">
            <!-- 这里要不要动态后面再考虑 -->
            <div :style="{ height: huntHeight + 'px' }" class="hunt" @click.stop>
              <Hunt
                :height="huntHeight - 60 + 'px'"
                :asideList="asideList"
                :searchTermList="searchTermList"
                @submit="getParams"
                ref="huntRef"
                :filter="filter"
              />
              <div class="footer" style="height: 60px">
                <button class="close" @click="closeAllPop">取消</button>
                <button class="close" @click="reset">清空条件</button>
                <button class="submit" @click="submitParams">{{ isHunt ? '立即搜索' : '确认' }}</button>
              </div>
            </div>
          </template>
        </template>
      </van-dropdown-item>
    </van-dropdown-menu>
  </div>
  <!--  -->
</template>

<style lang="scss" scoped>
.wrapss {
  width: 100%;
  height: 100%;
}
:deep(.van-dropdown-menu__bar) {
  box-shadow: none;
  height: 100%;
}
:deep(.van-overlay) {
  background: rgba(0, 0, 0, 0.2);
}
.footer {
  width: 100%;
  display: flex;
  padding: 10px 0;
  justify-content: space-between;
  background: #fff;

  button {
    width: 340px;
    height: 80px;
    line-height: 80px;
    border: none !important;
    padding: 0;
    font-size: 32px;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 400;
    text-align: CENTER;
    color: #74798c;
    margin: 0 12px 0px;
    background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
    border-radius: 8px;
    &.close {
      background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
      color: #74798c;
    }
    &.submit {
      background: linear-gradient(272deg, #076ee4 0%, #53a2ff 100%) !important;
      color: #fff;
    }
  }
}
:deep(.activeMenuItem) {
  color: #3e7bfa !important;
  span {
    color: #3e7bfa !important;
  }
}
:deep(.activeMenuItem:after) {
  border-color: transparent transparent currentColor currentColor;
}
:deep(.van-dropdown-item__content) {
  position: absolute;
  max-height: 1600px !important;
}
</style>
