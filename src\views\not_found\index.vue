<template>
  <div class="not-found">
    <h3 class="title">404</h3>
    <van-cell-group>
      <van-cell is-link title="去首页" @click="pageChangeHandler('/')"></van-cell>
    </van-cell-group>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { pageChangeHandler } from '@/utils'

const router = useRouter()
</script>

<style lang="scss" scoped>
.not-found {
  .title {
    margin: 0 auto 40px;
    font-size: 30px;
    font-weight: bold;
    text-align: center;
    color: olivedrab;
  }
}
</style>
