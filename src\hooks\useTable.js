import { reactive } from 'vue'
import { Post } from '@/request/index.js'
import { CHAIN_URL } from '@/api/constants.js'

/**
 * @param {*} param0
 * @returns
 */
const useRequestHook = (apiUrl = '', dataCallBack = null, requestType, initParam = {}) => {
  const state = reactive({
    dataList: [],
    // 分页数据
    pageParams: {
      // 当前页数
      pageNum: 1,
      // 每页显示条数
      pageSize: 10,
    },
    // 是否正在加载
    loading: false,
    // 总条数
    total: 0,
    // 是否存在数据
    noDataflag: false,
    finished: false,
    refreshing: false,
    initParams: initParam,
    apiUrl: apiUrl,
  })

  /**
   * @returns 请求数据
   */
  const getDataList = async () => {
    if (!state.apiUrl) return false
    state.loading = true
    state.noDataflag = false
    const { pageNum, pageSize } = state.pageParams
    const { ...rest } = state.initParams
    let params = {
      ...rest,
    }
    if (requestType === '$limit') {
      params = {
        $count: true,
        ...params,
        $limit: pageSize,
        $offset: (pageNum - 1) * pageSize,
      }
    } else if (requestType === 'limit') {
      params = {
        $count: true,
        ...params,
        limit: pageSize,
        offset: (pageNum - 1) * pageSize,
      }
    } else if (requestType === 'pageIndex') {
      // 默认参数情况
      params = {
        pageSize: pageSize,
        pageIndex: pageNum,
        ...rest,
      }
    } else if (requestType === 'page_index') {
      // 默认参数情况
      params = {
        page_size: pageSize,
        page_index: pageNum,
        ...rest,
      }
    }
    try {
      const res = await fetchRelatedInfo(params)
      const temAry = res?.items || res?.datalist || res?.records || []
      // 测试写死
      // const res = { count: 0 }
      // const temAry = []
      if (state.pageParams.pageNum === 1) {
        state.dataList = []
        state.dataList = temAry
      } else {
        state.dataList.push(...temAry)
      }
      // ps这里外面拿到这个数据处理 --目前我这里拿到数据直接拷贝一份（要是只是转换格式记得返回）
      if (dataCallBack) {
        let temp = dataCallBack(state.dataList)
        // console.log('temp: ', temp)
        // 后续加判读
        state.dataList = Array.isArray(temp) && temp?.length ? temp : state.dataList
      }
      //   console.log(res)
      state.total = res?.count || res?.total || 0
      state.noDataflag = state.total === 0
      state.refreshing = false
    } catch (error) {
      console.error('Error:', error)
      state.finished = true
      state.noDataflag = true
    } finally {
      state.loading = false
    }
  }
  // 请求数据
  const fetchRelatedInfo = params => {
    const regex = /\/zi_ding_yi/ //加后缀自定义 就传完整路径
    let URL = regex.test(state.apiUrl) ? state.apiUrl.replace(regex, '') : `${CHAIN_URL}/${state.apiUrl}`
    //发请求
    return Post(URL, params)
      .then(response => response)
      .catch(error => {
        console.error('Error:', error)
        throw error
      })
  }
  // 刷新加载
  const onRefresh = (initParams = {}, changeUrl = '') => {
    if (changeUrl) {
      //外面动态改变url
      state.apiUrl = changeUrl
    }
    state.pageParams.pageNum = 1
    state.refreshing = true
    state.finished = false
    state.dataList = []
    state.initParams = initParams
    getDataList()
  }

  // 加载更多
  const loadmore = async () => {
    // 如果所有数据已经加载，阻止加载更多
    if (state.total <= state.dataList.length) {
      state.finished = true
      return
    }

    // 尝试加载更多数据
    try {
      state.pageParams.pageNum += 1
      await getDataList()
    } catch (error) {
      // 如果出现错误，适当地处理它
      console.error('Error:', error)
      // 可能需要回滚页数
      state.pageParams.pageNum -= 1
    }
  }

  return {
    ...toRefs(state),
    onRefresh,
    loadmore,
    getDataList,
  }
}

export default useRequestHook
