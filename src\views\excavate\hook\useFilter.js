import { useMethodMap } from './useMap'
import { handleMultiple } from '@/components/Hunt/common/huntAbout'

const useFilter = ({ dropRef, data, getData }) => {
  const { nameConversionLongLat, longLatConversionName } = useMethodMap()
  // filter相关数据
  const filterData = reactive({
    areaPop: false,
    tempArea: {}, // 地区临时选中的值
    visibleArea: false, // 回显需要
    isShowHunt: true,
    tempHunt: {},
    sureHunt: {}, //更多筛选确定数据-回填
    huntH: false, //高亮
    tempindustry: [], //行业
    chain_codes: [], // 产业链
    industry_visible: false, //是否显示行业弹窗
    chanye_visible: false, // 是否显示产业链弹窗
    industry_name: '',
  })
  const huntRef = ref(null)
  // 筛选点击事件逻辑
  const handleClick = async (type, value) => {
    // console.log('type', type, value?.checkedChainList)
    // 处理所有的点击事件
    switch (type) {
      case 'areaOpen':
        filterData.visibleArea = true
        break
      case 'areaClose':
        filterData.visibleArea = false
        nextTick(() => {
          dropRef.value?.close()
        })
        break
      case 'getArea':
        filterData.tempArea = value
        break

      case 'areaSure':
        nextTick(() => {
          dropRef.value?.close()
        })
        if (filterData.tempArea?.code) {
          filterData.areaPop = true
        }
        break
      case 'areaPopSure': // 地区最终确定
        // 赋值
        data.area_value = filterData.tempArea.code
        data.area_name = filterData.tempArea.name
        filterData.areaPop = false
        const { lng, lat } = await nameConversionLongLat(data.area_name)
        data.grids[0].lat = lat
        data.grids[0].lon = lng
        data.address = await longLatConversionName([lng, lat])
        // 处理发送请求的逻辑
        getData({ type: 'area' })
        break

      case 'sliderVal': // 距离
        data.sliderVal = +value
        nextTick(() => {
          dropRef.value?.close()
        })
        getData({ type: 'sliderVal' })
        break
      // 更多筛选
      case 'hunt':
        filterData.tempHunt = JSON.parse(JSON.stringify(value))
        break
      case 'huntClose':
        nextTick(() => {
          dropRef.value?.close()
        })
        break
      case 'huntOpen':
        // 回填数据
        console.log('huntOpen', value)
        huntRef.value?.setBackfillData(filterData.sureHunt)
        break
      case 'huntSure':
        data.huntData = {}
        filterData.sureHunt = { ...filterData.tempHunt.paramsData }
        filterData.hunH = filterData.tempHunt.isHeight
        if (filterData.tempHunt?.paramsData?.eleseic_data) {
          delete filterData.tempHunt.paramsData.eleseic_data
        }

        Object.assign(data.huntData, filterData.tempHunt.paramsData)
        nextTick(() => {
          dropRef.value?.close()
        })
        // 保留产业信息
        getData({ type: 'huntSure', chain_codes: filterData.chain_codes })
        break
      case 'industrySure': // 点击行业
        const list = value?.checkedList || []
        filterData.tempindustry = list
        data.industry_value = handleMultiple(list).map(i => i.code)
        filterData.industry_visible = false
        getData({ type: 'industry' })
        break
      case 'chanyeSure': // 点击产业
        const chanyeList = value?.checkedChainList || []
        filterData.chain_codes = chanyeList
        data.chanye_value = handleMultiple(chanyeList).map(i => i.chain_code)
        filterData.chanye_visible = false
        getData({ type: 'chanye', chain_codes: filterData.chain_codes })
        break

      default:
        break
    }
  }
  // tab点击逻辑
  const onTabFour = (name, index) => {
    switch (name) {
      case '行业':
        filterData.industry_visible = true
        break
      case '龙头':
        getData({ type: 'leading_ent' })
        break
      case '园区':
        getData({ type: 'park' })
        break
      case '产业':
        filterData.chanye_visible = true
        break
      default:
        break
    }
  }

  // 一开始进来获取重庆经纬度以及中心名字
  onMounted(async () => {
    const { lng, lat } = await nameConversionLongLat(data.area_name)
    data.grids[0].lat = lat
    data.grids[0].lon = lng
    data.address = await longLatConversionName([lng, lat])
    getData({ type: 'industry' })
  })
  return { handleClick, filterData, huntRef, onTabFour }
}

export { useFilter }
