<template>
  <div class="container">
    <div class="search">
      <SearchInput
        v-model="inputValue"
        @clear="handleClear"
        @change="debounceChange"
        :showCancel="false"
        placeholder="请输入榜单名称"
        @cancel="cancel"
        :isRightIcon="false"
      />
    </div>
    <div class="dropdown">
      <DropDown :options="options" @change="changeDrop" :params="params" />
    </div>
    <div class="list" ref="listRef">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          @load="loadmore"
          offset="1"
          :immediate-check="false"
          :finished="finished"
          :finished-text="authorityList.length ? '没有更多了' : undefined"
        >
          <div v-for="item in authorityList" :key="item.id">
            <Card :item="item" :keyword="params.keyword" />
          </div>
        </van-list>
      </van-pull-refresh>
      <div class="noData" v-if="noData">
        <img src="@/assets/image/null.png" alt="" />
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup name="AuthorityList">
import { reactive, onMounted, ref } from 'vue'
import SearchInput from '@/components/SearchInput/index.vue'
import DropDown from './dropDown.vue'
import Card from './card.vue'
import { debounce } from '@/utils/index'
import { getTypes, getAuthorityList } from '@/api/authority'
import { useRoute } from 'vue-router'
import { backHandler } from '@/utils'

const route = useRoute()
const router = useRouter()

const loading = ref(false) //是否处于加载状态
const finished = ref(false) //是否已加载完成
const refreshing = ref(false) //下拉刷新加载
const noData = ref(false)
const params = reactive({ page_index: 0, page_size: 10, theme_type: '', year: '', keyword: '' })
const years = reactive([])
const authorityList = ref([])
const total = ref(0)
const inputValue = ref('')
const date = new Date()
const listRef = ref(null)
for (let i = 1990; i <= date.getFullYear(); i++) {
  years.unshift({
    value: i,
    text: i,
  })
}
years.unshift({
  value: '',
  text: '不限',
})
const options = [
  {
    code: 'year',
    name: '选择年份',
    value: '',
    children: years,
  },
  {
    code: 'theme_type',
    name: '榜单类型',
    value: '',
    children: [],
  },
]

const _getAuthorityList = function () {
  loading.value = true
  noData.value = false
  if (refreshing.value) {
    authorityList.value = []
    refreshing.value = false
  }
  getAuthorityList(params)
    .then(res => {
      if (params.page_index === 0) {
        authorityList.value = res.items
      } else {
        authorityList.value.push(...res.items)
      }
      total.value = res.count || 0
      noData.value = !res.count
    })
    .finally(() => {
      loading.value = false
    })
}
const changeDrop = function (val, code) {
  if (code === 'year') params.year = val
  if (code === 'theme_type') params.theme_type = val
  finished.value = false
  authorityList.value = []
  params.page_index = 0
  _getAuthorityList()
}
const onRefresh = () => {
  params.page_index = 0
  _getAuthorityList()
  // refreshing.value = false
}
const loadmore = function (cb) {
  if (total.value <= authorityList.value.length) {
    finished.value = true
    return
  }
  params.page_index += 1
  _getAuthorityList()
}
const handleClear = () => {
  inputValue.value = ''
  finished.value = false
}
const cancel = () => {
  inputValue.value = ''
  finished.value = false
  backHandler()
}
const debounceChange = debounce(val => {
  params.keyword = val
  params.page_index = 0
  authorityList.value = []
  finished.value = false
  _getAuthorityList()
}, 500)
const _getTypes = function () {
  getTypes().then(res => {
    options[1].children = res.dictionary_data.map(item => {
      item.value = item.code
      item.text = item.message
      return item
    })
    options[1].children.unshift({ value: '', text: '全部' })
  })
}

onMounted(() => {
  if (route.query.keyword) {
    inputValue.value = route.query.keyword
    params.keyword = route.query.keyword
  }

  _getTypes()
  _getAuthorityList()
})

nextTick(() => {
  // 计算列表滚动高度
  const { height: h } = useWindowSize()
  const { top } = useElementBounding(listRef, { immediate: true, windowResize: true }) //第一次拿不到
  const remainingHeight = h.value - top.value
  listRef.value.style.height = remainingHeight + 'px'
})
</script>

<style lang="scss" scoped>
.container {
  background: #f7f7f7;

  .search {
    padding: 20px 24px;
    background-color: #fff;
  }
  .dropdown {
    border-top: 2px solid #eee;
    display: flex;
    height: 88px;
  }
  .list {
    overflow: auto;
  }
}
.noData {
  display: flex;
  flex-direction: column;
  align-items: center;
  // margin-top: 240px;
  img {
    width: 230px;
    height: 230px;
  }
  div {
    color: #74798c;
    margin-top: 24px;
  }
}
</style>
