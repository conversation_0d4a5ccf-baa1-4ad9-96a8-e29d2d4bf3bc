<template>
  <!-- h5拖拽会跟着滑动，只能做点击事件 -->
  <!--    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd" -->
  <!-- fExcavate为了让里面滚动，并且适配有导航的情况里面要计算高度 -->
  <div class="relative overflow-hidden w-full h-full" id="fExcavate">
    <!-- 筛选框 -->
    <div class="w-full" ref="filterRef">
      <Filter v-model="params" :isShowFour="isShowFour" />
    </div>
    <!-- 地图 -->
    <div class="h-[470px] bg-[#f7f7f7] relative">
      <MapCom ref="mapRef" />
    </div>
    <!-- 滑块 -->
    <div
      class="z-[160] movable-view absolute cursor-pointer w-full rounded-t-[40px] bg-white translate-y-[-24px]"
      :class="[isHidden ? 'overflow-hidden' : 'translate-y-0']"
      ref="movableViewRef"
    >
      <div class="h-full bg-white">
        <EntList v-if="!isPark" :params="reqParams" :isPage="false" :isRelease="isRelease" @clickItem="clickItem" />
        <YquList v-if="isPark" :params="reqParams" :isPage="false" @clickItem="clickItem" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useLayout } from './child/useLayout.js'
import Filter from './component/Filter.vue'
import EntList from './component/EntList.vue'
import YquList from './component/YquList.vue'
import MapCom from './component/MapCom.vue'
import { investmentList, parkList } from '@/api/excavate'
import { backHandler } from '@/utils'

const route = useRoute()
const router = useRouter()
const isPark = ref(false)
const mapRef = ref(null)

// console.log('接惨', route.query.chain_codes)

const {
  // handleTouchStart,
  // handleTouchMove,
  // handleTouchEnd,
  movableViewRef,
  isShowFour,
  filterRef,
  isHidden,
  clickItem,
} = useLayout()

const isRelease = ref(false)
const params = ref({})
const reqParams = ref({})
const mapParams = ref({}) //用来渲染地图的参数  有地 经纬度 ，半径 ,列表,类型

const getEnt = async val => {
  if (!Object.keys(val).length) return
  let { grids, radius } = val
  let mapObj = { radius: +radius, center: [+grids[0].lon, +grids[0].lat], markers: [], type: 'entArr' }
  const { items } = await investmentList({ ...val, page_index: 1, page_size: 50 })
  if (items?.length) {
    mapObj.markers = items.map(item => {
      return {
        position: [+item.location.lon, +item.location.lat],
        content: item.ent_name,
      }
    })
  }
  mapParams.value = mapObj
  setTimeout(() => {
    mapRef.value?.reFreshMap(mapParams.value)
  })
}
const getPark = async val => {
  let { grids, radius } = val
  let mapObj = { radius: +radius, center: [+grids[0].lon, grids[0].lat], type: 'park', markers: [] }
  const { datalist } = await parkList({ ...val, page_index: 1, page_size: 50 })
  if (datalist?.length) {
    mapObj.markers = datalist.map(item => {
      return {
        position: [+item.location.lon, +item.location.lat],
        content: item.pname,
        count: item.area,
      }
    })
  }
  mapParams.value = mapObj
  setTimeout(() => {
    mapRef.value?.reFreshMap(mapParams.value)
  })
}

watch(
  () => params,
  async val => {
    let obj = toRaw(val.value)

    if (!Object.keys(obj)?.length) {
      return;
    }
    const type = obj.type
    delete obj.type
    // 过滤参数
    if (obj['register_time']?.length) {
      obj['register_time'].forEach(i => {
        delete i['name']
      })
    }
    if (['industry', 'leading_ent', 'area', 'chanye'].includes(type)) {
      isPark.value = false
      if (obj.isArea) {
        delete obj.isArea
        const code = obj.area_code
        mapRef.value?.reFreshAreaMap({code, center: [obj.grids[0].lon, obj.grids[0].lat]})
        reqParams.value = {
          area_code: obj.area_code,
          area_name: obj.area_name,
        }
        return
      }
      reqParams.value = obj
      await getEnt(obj)
      return
    }
    if (['park'].includes(type)) {
      isPark.value = true
      reqParams.value = obj
      getPark(obj)
    }
  },
  {
    immediate: true,
    deep: true,
  }
)
</script>

<style lang="scss" scoped>
.huaKuai {
  transform: translateY(-30px);
  background: #fff;
}
</style>
