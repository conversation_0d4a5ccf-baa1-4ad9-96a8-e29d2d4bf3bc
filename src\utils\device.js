const WIN = window
const LOC = WIN.location
const NA = WIN.navigator
const UA = NA.userAgent.toLowerCase()
const test = needle => needle.test(UA)

const isTouch = 'ontouchend' in WIN
const isAndroid = test(/android|htc/) || /linux/i.test(NA.platform + '')
const isIPad = !isAndroid && test(/ipad/)
const isIPhone = !isAndroid && test(/ipod|iphone/)
const isIPhoneX =
  /iphone/gi.test(navigator.userAgent) &&
  ((screen.height === 812 && screen.width === 375) || (screen.width === 414 && screen.height === 896))
const isIOS = isIPad || isIPhone
const isWinPhone = test(/windows phone/)
const isWebapp = !!NA.standalone
const isXiaoMi = isAndroid && test(/mi\s+/)
const isUC = test(/ucbrowser/)
const isWeixin = test(/micromessenger/)
const isChrome = !!WIN.chrome
const isPC = !isAndroid && !isIOS && !isWinPhone
const isDebug = !!~('' + LOC.port).indexOf('0')

export {
  isTouch,
  isAndroid,
  isIPad,
  isIPhone,
  isIPhoneX,
  isIOS,
  isWinPhone,
  isWebapp,
  isXiaoMi,
  isUC,
  isWeixin,
  isChrome,
  isPC,
  isDebug,
}
