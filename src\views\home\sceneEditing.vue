<template>
  <div class="flex flex-col w-full h-full scene-editing-container bg-grey">
    <ChainTitle @change="handleChainChange" />

    <div class="flex flex-wrap px-40 pt-24 mb-24 bg-white">
      <div v-for="(item, index) in chainData" :key="item.id" class="flex items-center mb-24 mr-40">
        <span class="text-secondary text-28" :class="[active === index ? 'text-selected' : '']" @click="active = index">
          {{ item.name }}
        </span>
        <van-checkbox
          :checked="getChecked(item.id)"
          shape="square"
          class="ml-8"
          @click="checked => handleChange(!selectedKeys.includes(item.id), item)"
        ></van-checkbox>
      </div>
    </div>

    <div class="pb-168">
      <div
        v-for="item in chainData?.[active]?.children || []"
        :key="item.id"
        class="px-24 py-32 pb-16 bg-white border-b border-solid border-[#eee]"
      >
        <div class="flex mb-24">
          <span class="font-semibold text-primary text-28">{{ item.name }}</span>
          <van-checkbox
            :checked="getChecked(item.id)"
            shape="square"
            class="ml-8"
            @click="checked => handleChange(!selectedKeys.includes(item.id), item)"
          ></van-checkbox>
        </div>

        <div class="flex flex-wrap">
          <div
            v-for="node in [...item.children]"
            :key="node.id"
            class="px-16 py-12 mb-16 mr-16 text-tertiary text-26 bg-grey rounded-[8px]"
            :class="[selectedKeys.includes(node.id) ? 'highlight' : '']"
            @click="handleChange(!selectedKeys.includes(node.id), node)"
          >
            <span class="">{{ node.name }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="fixed bottom-0 flex justify-between px-24 py-10 bg-white pb-50">
      <div class="w-[340px] mr-22 cancel-btn" @click="handleCancel">取消</div>
      <div class="w-[340px] confirm-btn" @click="handleConfirm">确定</div>
    </div>
  </div>
</template>

<script setup>
import { PRESET_INDUSTRY } from '@/config'
import ChainTitle from './chainTitle.vue'
import {
  getStrongChainNodes,
  getSupplementaryChainNodes,
  getChainCodePlus,
  setStrongChain,
  setSupplementaryChain,
} from '@/api/home/<USER>'
import { useRouter, useRoute } from 'vue-router'
import { onMounted, getCurrentInstance, watch } from 'vue'
import useEmitter from '@/hooks/useEmitter'
import { backHandler } from '@/utils'
import { filterChainMenuLists } from "@/contant/dict";
import {useUserStore} from "@/store/modules/user";

const emitter = useEmitter()
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const { proxy } = getCurrentInstance()

const active = ref(0)
const chainData = ref([])
const selectedKeys = ref([])
const TREE_MAP = ref({})
const currentIndustry = ref({})

watch(selectedKeys, () => {
  setChecked(selectedKeys.value, true)
})

const clearTree = () => {
  selectedKeys.value = []
  TREE_MAP.value = {}
  chainData.value = []
}

const getSelectedNodes = () => {
  ;(Number(route.query.type) === 1 ? getStrongChainNodes : getSupplementaryChainNodes)(
    currentIndustry.value.value
  ).then(res => {
    selectedKeys.value = res
  })
}

const handleChainChange = chain => {
  currentIndustry.value = chain
  userStore.setChain(chain)
  active.value = 0
  clearTree()

  proxy.$loading('加载中')
  getChainCodePlus(chain.value)
    .then(res => {
      if (res?.length === 1 && res?.[0]?.name === '智能网联新能源整车') {
        chainData.value = res[0]?.children || []
      } else {
        chainData.value = res
      }

      initTree(chainData.value, null)
      setChecked(selectedKeys.value, true)
    })
    .finally(() => {
      proxy.$close()
    })

  getSelectedNodes()
}

onMounted(()=>{
  const chainCode = route.query.code;
  const chain = filterChainMenuLists.find(item => item.value === chainCode) || filterChainMenuLists[0];
  handleChainChange(chain)
})

const initTree = (data, parent) => {
  if (Array.isArray(data)) {
    data.forEach(item => {
      item.id = item.chain_code
      TREE_MAP.value[item.id] = item
      item.parent = parent
      initTree(item.children || [], item)
    })
  } else {
    data.id = data.chain_code
    TREE_MAP.value[data.id] = data
    initTree(data.children || [], data)
  }
}

const setChecked = (keys, value) => {
  keys.forEach(key => {
    ;(TREE_MAP.value[key] || {}).checked = value
  })
}

const getChildrenKeys = data => {
  let result = []
  data?.forEach?.(item => {
    if (item.level && item.level <= 4) {
      result.push(item.id)
    }
    result = [...result, ...getChildrenKeys(item.children)]
  })

  return result
}

const getChecked = id => {
  return TREE_MAP.value[id].checked
}

const handleChange = (checked, data) => {
  const value = checked

  const treeItem = TREE_MAP.value[data.id]
  treeItem.checked = value
  const childKeys = getChildrenKeys(treeItem.children)
  childKeys.length && setChecked(childKeys, value)

  setSiblingChecked(data, data.parent)

  selectedKeys.value = Object.keys(TREE_MAP.value).filter(key => TREE_MAP.value[key].checked)
  // emits('update:value', selectedKeys.value)
  // emits('select', selectedKeys.value, {
  //   value,
  //   current: data,
  //   selectedNodes: getNodes(selectedKeys.value),
  // })
}

const setSiblingChecked = (current /** 当前选择的一个 */, parent) => {
  const siblingKeys = current?.parent?.children?.map?.(item => item.id) || []
  if (parent) {
    TREE_MAP.value[parent.id].checked = siblingKeys.every(key => TREE_MAP.value[key].checked)
  }
}

const getNodes = keys => {
  const result = []
  keys.forEach(key => {
    result.push(TREE_MAP.value[key])
  })

  return result
}

const selectedNodes = ref([])
const setSelectedNodes = keys => {
  const nodes = getNodes(keys)
  selectedNodes.value = nodes
}

const handleCancel = () => {
  backHandler()
}

const handleConfirm = () => {
  proxy.$loading('保存中...')
  setSelectedNodes(selectedKeys.value)
  const params = selectedNodes.value.map(item => {
    return {
      code: item.id,
      name: item.name,
    }
  })

  ;(Number(route.query.type) === 1 ? setStrongChain : setSupplementaryChain)(currentIndustry.value.value, params).then(
    () => {
      proxy.$showToast('保存成功')
      emitter.emit('sceneChanged', {
        type: Number(route.query.type),
        selectedKeys: selectedKeys.value,
      })
      handleCancel()
    }
  )
}
</script>

<style lang="scss" scoped>
:deep(.van-checkbox__icon) {
  font-size: 32px;
}

.highlight {
  background-color: #f5f7fe !important;
  color: #1e75db !important;
}
</style>
