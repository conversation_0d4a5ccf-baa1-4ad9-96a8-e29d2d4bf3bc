import { MD5 } from 'crypto-js'
const { VITE_APP_ENV, VITE_IS_PROD_UC, MODE } = import.meta.env
import { UC_URL } from "@/api/constants";

export const LOGIN = `${UC_URL}/tokens`
export const SINGLE_LOGIN = `${UC_URL}/sign/login`
export const LOGOUT = '/user/logout'
export const GET_USER_INFO = '/user/get_user_info'

import { Post } from '@/request'

export function login(username, password) {
  return Post(`${UC_URL}/tokens`, { login_name: username, password: MD5(password).toString() })
}
export function code_login(code) {
  return Post(`${UC_URL}/external/lt/auth`, { code })
}

export function getInfo() {
  return Post(GET_USER_INFO)
}
export function logout() {
  return Post(LOGOUT)
}

export function signLogin(data) {
  return Post(SINGLE_LOGIN, data)
}


export default {
  login,
  code_login,
  getInfo,
  logout,
  signLogin,
}
