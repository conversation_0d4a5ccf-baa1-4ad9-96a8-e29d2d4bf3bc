import dayjs from 'dayjs'
import {goH5} from './utils'
import {getH5Address} from '@/api/advanceSearch/index.js'
import {useUserStore} from '@/store/modules/user'
import {getToken} from '@/utils/auth'
// import { qiankunWindow } from 'vite-plugin-qiankun/dist/helper'
import {dispatchHistoryBackEvent} from '@/dispatchEvent/historyBack';
import {router} from '@/main';

export function debounce(fn, delay) {
  let timer
  return function () {
    const that = this
    const args = arguments
    clearTimeout(timer)
    timer = setTimeout(function () {
      fn.apply(that, args)
    }, delay)
  }
}

const format = 'YYYY-MM-DD'

export const calcDate = (amount, unit) => {
  if (amount === undefined || unit === undefined) return
  return `${dayjs().subtract(amount, unit).format('x')}$${dayjs().format('x')}`
}

/** *
 * 通用查询参数组装 ， 默认eq
 * @param {[key: string]: string[] | number[] | string} params
 * @param {[key: string]: 'eq' | 'in' | 'like' | 'gt' | 'lt', 'between'} dict
 * @param {Boolean} contain 是否大于等于或小于等于
 * @returns {string}
 * numberDictFormatter
 * @example formatterParams(
 *   { BENEFIT_ASSESS: [0, 50] },
 *   { BENEFIT_ASSESS: 'gt'},
 *   false
 * )
 *
 * => formatterParams({ BENEFIT_ASSESS: [0, 50] }, {BENEFIT_ASSESS: 'gt'})
 */
export function formatterParams(params, dict, contain) {
  const filterParams = []
  for (const k in params) {
    let eq = dict?.[k] || 'eq'
    const value = eq === 'like' ? `%${params[k]}%` : params[k]
    if (params[k]) {
      let item = ''
      if (Object.prototype.toString.call(value) === '[object Array]') {
        if (eq === 'in') {
          const itm = params[k].join('|')
          if (itm) {
            // 过滤params[K]的数组长度为0
            if (params[k].length > 1) {
              item = `${k} ${eq} ${itm}`
              filterParams.push(item)
            } else {
              item = `${k} eq ${itm}`
              filterParams.push(item)
            }
          }
        } else {
          let interval = ['gt', 'lt'] // 区间值
          if (contain) {
            interval = ['ge', 'le'] // 如果包含两端，则大于等于，小于等于
          }
          value.map((itm, index) => {
            eq = interval[index]
            item = `${k} ${eq} ${itm}`
            filterParams.push(item)
          })
        }
      } else {
        item = `${k} ${eq} ${value}`
        filterParams.push(item)
      }
    }
  }
  return filterParams.join(' and ')
}

export function getUrlKey(k, ser = '') {
  let searchResult = {},
    hashResult = {}
  const {hash} = location
  const search = ser || location.search
  let r = null
  const format = validString => {
    const arr = validString.split('&')
    let temp = {}
    arr.forEach(param => {
      const paramKey = param.split('=')
      temp[paramKey[0]] = decodeURI(paramKey[1] || '')
    })
    return temp
  }
  if (search) {
    searchResult = format(search.slice(1))
  }
  if (hash) {
    if (hash.includes('?')) {
      const tagIndex = hash.indexOf('?')
      const hashPath = hash.slice(0, tagIndex)
      hashResult = format(hash.slice(tagIndex + 1))
      if (searchResult.historyPage && hashPath) {
        searchResult.historyPage = `${searchResult.historyPage}${hashPath}`
      }
    } else {
      // searchResult.historyPage = searchResult.historyPage ? `${searchResult.historyPage}${hash}` : `${hash}`
    }
  }
  r = {...searchResult, ...hashResult}
  return k ? r[k] : r
}

export function omit(target, keys) {
  const result = {}
  Object.keys(target).forEach(key => {
    !keys.includes(key) && (result[key] = target[key])
  })

  return result
}

//处理数字每隔三位,隔开
export function formatNumber(nStr) {
  var b = parseInt(nStr).toString()
  var len = b.length
  if (len <= 3) {
    return b
  }
  var r = len % 3
  return r > 0
    ? b.slice(0, r) + ',' + b.slice(r, len).match(/\d{3}/g).join(',')
    : b.slice(r, len).match(/\d{3}/g).join(',')
}

export const openReport = async item => {
  //这里后续加一个浏览历史调用的接口- 跳报告系统都要走这个方法并调用一次  从浏览历史哪里过来就不调用 加一个参数
  // const { h5_report_url } = await getH5Address(item.ent_id)
  // 测试 测试环境用不起 打包后文件丢失 需要后端看
  // window.open(`http://j-h5-report.ihdwork.com/?entId=KIkGexKCgH0&type=ZSDATA`, '_self')
  // window.open(h5_report_url + '&type=ZSDATA', '_self')
  const {h5_report_url} = await getH5Address(item.ent_id)
  // console.log(h5_report_url, 'h5_report_url')

  // 测试 测试环境用不起 打包后文件丢失 需要后端看
  // window.open(`http://j-h5-report.ihdwork.com/?entId=KIkGexKCgH0&type=QL`, '_self')
  window.open(h5_report_url + '&type=QL', '_self')
}

export function getQueryString(name) {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
  const r = window.location.search.substr(1).match(reg)

  return r !== null ? decodeURI(r[2]) : null
}

// 时间戳转日期
export function transformDate(num) {
  const number = new Date(num)
  const year = number.getFullYear()
  const month = number.getMonth() + 1
  const day = number.getDate()
  const date = year + '-' + month + '-' + day
  return date
}

/**
 * 根据数组每个对象中某个字段去重
 * @param {Array} arr
 * @param {String} field
 * @returns Array
 */
export function unique(arr, field) {
  var obj = {}
  return arr.reduce(function (acc, next) {
    obj[next[field]] ? '' : (obj[next[field]] = true && acc.push(next))
    return acc
  }, [])
}

export const transferChainCodeTree = target => {
  const result = []
  const target_ = cloneDeep(target)
  if (!target_) return []
  target_.forEach(item => {
    const {chain_name, chain_code} = item
    const top = []
    const middle = []
    const down = []
    item.children.forEach(item_ => {
      if (item_.belong_to === '1') {
        top.push(item_)
      } else if (item_.belong_to === '2') {
        middle.push(item_)
      } else if (item_.belong_to === '3') {
        down.push(item_)
      }
    })
    result.push({
      name: chain_name,
      chain_code,
      children: [
        {
          name: '上游',
          chain_code: `1%${chain_code}`,
          children: top,
        },
        {
          name: '中游',
          chain_code: `2%${chain_code}`,
          children: middle,
        },
        {
          name: '下游',
          chain_code: `3%${chain_code}`,
          children: down,
        },
      ],
    })
  })
  return result
}

export function getTokenHandler() {
  const userStore = useUserStore()
  // alert(`11tokenInfo: ${JSON.stringify(userStore.getTokenInfo)}`)
  // alert(`22tokenInfo: ${JSON.stringify(getToken())}`)
  return userStore.getTokenInfo || getToken() || null
}

/**
 * @description 跳转页面方法，接入微前端的应用需要使用此方法
 * @param { string } targetRouterPath 路由
 * @param { Object } query 参数对象
 * @param { boolean } isReplace 使用push还是replace
 */
export const pageChangeHandler = (targetRouterPath, query = {}, isReplace = false) => {
  let action = 'push'
  action = isReplace ? 'replace' : 'push'
  router[action]({path: targetRouterPath, query})
}

// 页面返回，接入微前端的应用需要使用此方法
export const backHandler = () => {
  // if (qiankunWindow.__POWERED_BY_QIANKUN__) {
  //   dispatchHistoryBackEvent();
  // } else {
  router.back();
  // }
};
