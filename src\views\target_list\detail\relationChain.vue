<template>
  <div>
    <section v-if="noData" class="h-[50vh] mt-24 bg-white flex items-center justify-center text-secondary">
      <!-- <Empty title="暂无企业关系链" icon-style="font-size: 280px; height: 166px">
      <template #title>
        <P style="font-size: 14px">未查询到企业关系链</P>
      </template>
    </Empty> -->
      暂无企业关系链
    </section>
    <div v-else class="h-full mt-24 bg-white relation-chain-container">
      <div style="margin-bottom: 30px">
        <div ref="relationChainChart" style="height: 800px" />
      </div>
    </div>
  </div>
</template>

<script>
// import Empty from '@/views/home/<USER>/components/Empty/index.vue';
import { getEntRelationChain } from '@/api/qualityReport'
import * as echarts from 'echarts'
import { useRoute } from 'vue-router'

const NODE_TYPE = {
  enterprise: '1',
  person: '4',
}

export default {
  // components: { Empty },
  data() {
    return {
      tableData: [],
      noData: false,
    }
  },
  mounted() {
    this._getEntRelationChain()
  },
  methods: {
    _getEntRelationChain() {
      const route = useRoute()
      getEntRelationChain(route.query.ent_id).then(res => {
        if (res?.length) {
          this.formatData(res)
          this.initChart(res)
        } else {
          this.noData = true
        }
      })
    },
    formatData(data) {
      data.forEach(item => {
        item.value = item.relation

        if (item?.children?.length) {
          this.formatData(item.children)
        }
      })
    },
    initChart(data) {
      const dom = this.$refs.relationChainChart
      const chart = echarts.init(dom, 'light')

      const option = {
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
        },
        series: [
          {
            type: 'tree',
            roam: true,
            data,
            value: 'elation',
            top: '1%',
            left: '7%',
            bottom: '1%',
            right: '20%',
            symbol: 'emptyCircle',
            symbolSize: 10,
            label: {
              position: 'left',
              verticalAlign: 'middle',
              align: 'right',
              fontSize: 12,
            },
            initialTreeDepth: -1,
            tooltip: {
              textStyle: {
                width: 200,
                overflow: 'break',
              },
              formatter: params => {
                const data = params.treeAncestors

                return data
                  .map((item, index) => {
                    const name = item.name.trim()
                    const value = data[index + 1]?.value?.trim?.() || ''

                    const str = name && value ? name + ' >' + value : name
                    return str.length >= 15 ? str + '<br />' : str
                  })
                  .filter(Boolean)
                  .join(' > ')
              },
            },
            leaves: {
              label: {
                position: 'right',
                verticalAlign: 'middle',
                align: 'left',
              },
            },
            emphasis: {
              focus: 'descendant',
            },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750,
          },
        ],
      }

      function isSymbolType(params) {
        const target = params.event.target
        return Boolean(target.path && target.shape)
      }

      chart.on('click', params => {
        const isSymbol = isSymbolType(params)

        if (!isSymbol) {
          if (params.data.node_type === NODE_TYPE.enterprise) {
            const data = params.data
            openEntReport(this, {
              ent_id: data.ent_id,
              region_code: data.area_code,
              ent_name: data.name,
            })
          }
        }
      })

      chart.on('mousedown', params => {
        const isSymbol = isSymbolType(params)

        const nodeId = params.data.id
        const curNode = chart._chartsViews[0]._data.tree._nodes.find(item => {
          const id = item.getId()
          return id === nodeId
        })

        /** 同时只展开一个节点, 避免数据过多时, 子节点重叠 */
        // if (isSymbol) {
        //   const depth = curNode.depth;
        //   const curIsExpand = curNode.isExpand;
        //   if (curNode.depth >= 3) {
        //     chart._chartsViews[0]._data.tree._nodes.forEach((item, index) => {
        //       const id = item.getId();
        //       if (item.depth === depth && id!== nodeId &&!curIsExpand) {
        //         item.isExpand = false;
        //       }
        //     });
        //   }
        // }

        /** 阻止点击文本节点展开收缩 */
        if (!isSymbol) {
          curNode.isExpand = !curNode.isExpand
        }

        return null
      })

      chart.setOption(option)
    },
  },
}
</script>

<style scoped lang="scss">
.intelligence-monitor-no-data-container {
  height: 490px;
}

.relation-chain-container {
  padding: 30px;
}

.hyperText {
  color: #076fe2;
  cursor: pointer;
  text-decoration: underline;
}
</style>
