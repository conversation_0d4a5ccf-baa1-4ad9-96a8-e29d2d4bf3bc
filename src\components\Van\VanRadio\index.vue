<script setup>
import VanBPop from '../VanbPop/index.vue'
import RevokeL from '../VanbRevokeL/index.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String, // 针对地图做处理
  },
})
const emits = defineEmits(['close', 'submit'])
const { proxy } = getCurrentInstance()
// 单选 临时选中的
const temObj = ref({})
const radioRef = ref(null)
const onSubmit = val => {
  if (Array.isArray(val)) {
    // 第一个是数组
    temObj.value = val[0]
    return
  }
  temObj.value = val
}
// 地图弹窗
const isSurePop = ref(false)
const onPopSubmit = () => {
  if (!props.type) {
    emits('submit', temObj.value)
    return
  }
  // 地区---还需要在弹一次
  isSurePop.value = true
}
const onSure = () => {
  isSurePop.value = false
  emits('submit', temObj.value)
}
</script>
<template>
  <div>
    <VanBPop @submit="onPopSubmit" @close="emits('close')" :visible="visible">
      <div class="min-h-[600px]">
        <RadioIndustry :visible="visible" v-bind="$attrs" fSure @close="emits('close')" @submit="onSubmit" />
      </div>
    </VanBPop>
  </div>
</template>

<style lang="scss">
// 确认弹窗样式
.h_sure_pop {
  background: #fff;
  .toast-wrapper {
    padding: 48px 0 0 !important;
    .footer {
      font-size: 32px;
      border-top: 1px solid #eeeeee;
      position: relative;
      &::before {
        content: '';
        width: 1px;
        background: #eee;
        height: 100%;
        position: absolute;
        left: 50%;
      }
      .button {
        background: transparent !important;
      }
      .submit-button {
        background: transparent !important;
        color: #076ee4;
      }
    }
  }
  .pop {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 8px;
    .tit {
      font-size: 32px;
      color: #20263a;
    }
    .cont {
      font-size: 28px;
      color: #74798c;
      line-height: 46px;
      margin-top: 40px;
      margin-bottom: 40px;
    }
  }
}
</style>
