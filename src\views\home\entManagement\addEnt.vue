<template>
  <div class="sticky top-0 p-20 bg-white">
    <van-search
      v-model="keyword"
      class="!p-0 w-full"
      :maxlength="16"
      :show-action="!!keyword"
      :clear-trigger="keyword ? 'always' : 'focus'"
      placeholder="请输入企业名称关键词"
      @input="
        () => {
          delayFn()
        }
      "
      @clear="
        () => {
          emptyflag = true
          keyword = ''
          filterDataSource = []
        }
      "
      @cancel="() => ((keyword = ''), (filterDataSource = []))"
    />
  </div>

  <div v-show="keyword" class="w-full overflow-y-auto ent-list">
    <div v-if="!filterDataSource.length && emptyflag" class="flex flex-col items-center justify-center w-full h-full">
      <SvgIcon name="empty-data" class="w-[230px] h-[230px] mb-24" />
      <p class="text-secondary text-28">暂未检索到相关信息</p>
    </div>

    <div v-else>
      <div
        v-for="item in filterDataSource"
        :key="item.id"
        class="relative flex items-center p-24 bg-white border-b border-solid border-[#f7f7f7]"
        @click="handleClickEntItem(item)"
      >
        <van-highlight
          :keywords="keyword"
          class="pr-20 text-28 text-primary"
          unhighlight-class="font-semibold"
          highlight-class="text-[#e72410] font-semibold"
          :source-string="item.ent_name"
        />
        <svg-icon v-if="item.ent_id === selectedItem?.ent_id" name="tick" class="absolute w-24 h-24 right-24" />
      </div>
    </div>
  </div>

  <div class="fixed bottom-0 flex justify-between px-24 py-10 bg-white pb-50">
    <div class="w-[340px] mr-22 cancel-btn" @click="handleCancel">取消</div>
    <div class="w-[340px] confirm-btn" @click="handleConfirmAdd">确定添加</div>
  </div>
</template>

<script setup>
import { getSearchEntList } from '@/api/home/<USER>'
import { debounce } from '@/utils/index'
import { getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { addPointEnt } from '@/api/home'
import { backHandler } from '@/utils'

const router = useRouter()
const { proxy } = getCurrentInstance()
const keyword = ref('')
const emptyflag = ref(true)
const filterDataSource = ref([])
const selectedItem = ref(null)

const handleSearch = item => {
  if (!keyword.value) {
    filterDataSource.value = []
    emptyflag.value = true
    return
  }
  proxy.$loading('加载中...')
  getSearchEntList({ ent_name: keyword.value })
    .then(res => {
      filterDataSource.value = res.datalist
      emptyflag.value = res.datalist.length === 0
    })
    .finally(() => {
      proxy.$close()
    })
}
const delayFn = debounce(handleSearch, 500)

const handleClickEntItem = item => {
  console.log(item)
  selectedItem.value = item
}

const handleCancel = () => {
  backHandler()
}

const handleConfirmAdd = () => {
  if (!selectedItem.value.ent_id) return
  addPointEnt(selectedItem.value).then(res => {
    proxy.$showToast('添加成功')
    selectedItem.value = null
    backHandler()
  })
}
</script>

<style lang="scss" scoped>
:deep(.van-search) {
  background-color: #f7f7f7;

  .van-search__action {
    margin-left: 24px;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 32px;
      background-color: #dedede;
    }
  }
}

.ent-list {
  height: calc(100vh - 170px - 112px);
}
</style>
