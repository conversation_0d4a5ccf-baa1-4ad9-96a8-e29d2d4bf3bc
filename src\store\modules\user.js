import { defineStore } from 'pinia'
import { setToken, removeToken } from '@/utils/auth'
import { login, getInfo, logout, code_login, signLogin } from '@/api/login'
import { dispatchSetSelectChainEvent } from '@/dispatchEvent/setSelectChain'
import { store } from '../index'
export const useUserStore = defineStore({
    id: 'userInfo',
    state: () => ({
        access_token: '',
        refresh_token: '',
        user_id: '',
        mac_key: '',
        mac_algorithm: '',
        expires_at: '',
        create_time: '',
        last_use_time: '',
        chain: {
            label: '智能网联新能源汽车',
            value: 'E1G1'
        },
        report_ent_id: '',
    }),
    getters: {
        getUserId: state => state.user_id,
        reportEntIdGet: state => state.report_ent_id,
        getTokenInfo: state => ({access_token: state.access_token, mac_key: state.mac_key})
    },
    actions: {
        Login(username, password) {
            return new Promise((resolve, reject) => {
                login(username, password)
                    .then(res => {
                        setToken(res)
                        this.access_token = res.access_token
                        this.refresh_token = res.refresh_token
                        this.user_id = res.user_id
                        this.mac_key = res.mac_key
                        this.expires_at = res.expires_at
                        this.create_time = res.create_time
                        this.last_use_time = res.last_use_time
                        this.mac_algorithm = res.mac_algorithm
                        resolve()
                    })
                    .catch(error => {
                        reject(error)
                    })
            })
        },
        // 通过code登录
        CodeLogin(code) {
            return new Promise((resolve, reject) => {
                signLogin(code)
                    .then(res => {
                        setToken(res)
                        this.access_token = res.access_token
                        this.refresh_token = res.refresh_token
                        this.user_id = res.user_id
                        this.mac_key = res.mac_key
                        this.expires_at = res.expires_at
                        this.create_time = res.create_time
                        this.last_use_time = res.last_use_time
                        this.mac_algorithm = res.mac_algorithm
                        resolve()
                    })
                    .catch(error => {
                        reject(error)
                    })
            })
        },
        GetInfo(token) {
            return new Promise((resolve, reject) => {
                getInfo(token)
                    .then(res => {
                        this.userId = res.data.userId
                        this.userName = res.data.userName
                        this.avatar = res.data.avatar
                        this.nickName = res.data.nickName
                        this.permissions = res.data.permissions
                        this.roles = res.data.roles
                        resolve(res)
                    })
                    .catch(error => {
                        reject(error)
                    })
            })
        },
        setChain(chain) {
            const {
                label,
                value
            } = chain
            dispatchSetSelectChainEvent({name: label, code: value})
            this.chain = chain
        },
        async exit() {
            removeToken()
            this.tokenInfo = null
            this.userInfo = null
        },
        // 存储报告id
        SetReportId(id) {
          this.report_ent_id = id;
        }
    },
})

export function useUserStoreWithOut() {
    return useUserStore(store)
}