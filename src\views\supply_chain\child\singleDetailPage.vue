<template>
  <div class="page">
    <div class="f-y-center border-b border-solid border-[#eee] bg-white justify-between px-24">
      <div class="w-500">
        <VanbTab :list="['供应链概况', '供应链详情']" v-model:active="curId" @handleClick="onChangeTab" />
      </div>
      <div class="f-y-center cursor-pointer">
        <img class="w-40 h-40 mr-8" src="@/assets/image/tupu.png" v-show="curId === 1" />
        <span class="text-28 text-[#74798C]" v-show="curId === 1" @click="goTupu">图谱</span>
      </div>
    </div>
    <!-- 供应链概况 -->
    <div v-if="curId === 0" ref="gylRef" class="gyl-container">
      <!-- 地图 -->
      <LMap :data="data.mapData" />
      <LPie :data="data.pieData" />
      <!--饼图 -->
    </div>
    <!-- 供应链详情 -->
    <div v-if="curId === 1">
      <ListOfBusinesses />
    </div>
  </div>
</template>

<script setup name="SingeleDetailPage">
import VanbTab from '@/components/Van/VanbTab/index.vue'
import LMap from '../component/Map.vue'
import LPie from '../component/Pie.vue'
import ListOfBusinesses from '@/views/advance_search/ListOfBusinesses.vue'
import { staticMap, staticChains } from '@/api/supplyChain/index.js'
import { useElementBounding } from '@vueuse/core'
import { provinces } from '../component/maps.js'
import { pageChangeHandler } from '@/utils'

const route = useRoute()
const router = useRouter()
const curId = ref(0)
const gylRef = ref(null)
const { height: winHeight } = useWindowSize()
const data = reactive({
  ent_id: null,
  mapData: [],
  pieData: [],
})
const { proxy } = getCurrentInstance()
proxy.$loading({ message: '加载中...', forbidClick: true, duration: 1000 })

const handLeftH = () => {
  const { top: gylHeight } = useElementBounding(gylRef)

  // 计算剩余高度
  const remainingHeight = winHeight.value - gylHeight.value
  // 设置局部滚动区域高度
  gylRef.value.style.height = `${remainingHeight}px`

  // 移除滚动条
  gylRef.value.style.overflowY = 'auto'
}
const onChangeTab = idx => {
  if (idx === 0) {
    nextTick(() => {
      handLeftH() // 重新计算高度
    })
  }
}
// 去图谱
const goTupu = () => {
  pageChangeHandler('/supply-atlas', { ent_id: data.ent_id, title: data.title, isSingle: true })
  // console.log('去图谱')
}
// 获取地图和饼图数据
const getLeftData = async () => {
  const params = {
    types: ['1', '2'],
    levels: ['1', '2'],
    is_faucet: false,
    is_market: false,
    is_local: false,
    method: 'region',
    ent_id: data.ent_id,
  }
  const [res1, res2] = await Promise.allSettled([staticMap(params), staticChains({ ...params, method: 'nic' })])
  // 处理结果
  if (res1.status === 'fulfilled') {
    const datas = {}
    Object.keys(res1.value).forEach(item => {
      const key = item.slice(0, 2) + '0000'
      if (datas[key]) {
        datas[key] += res1.value[item]
      } else {
        datas[key] = res1.value[item]
      }
    })
    data.mapData = provinces
      .filter(item => item.code !== '100000')
      .map(item => {
        return { id: item.code, name: item.fullName, value: datas[item.code] || 0, shortName: item.name }
      })
      .sort((a, b) => b.value - a.value)
  }

  if (res2.status === 'fulfilled') {
    data.pieData = res2.value?.datalist.sort((a, b) => b.total - a.total)
  }
}

onMounted(() => {
  const { ent_id, title } = route.query
  data.ent_id = ent_id
  data.title = title
  getLeftData()
  nextTick(() => {
    handLeftH()
    getLeftData()
  })
})
</script>

<style lang="scss" scoped>
.page {
  background: #f7f7f7;
}
.gyl-container {
  overflow-y: auto;
  /* 隐藏滚动条 */
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
  &::-webkit-scrollbar {
    display: none; /* Chrome、Safari 和 Opera */
  }
}
</style>
