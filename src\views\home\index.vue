<template>
  <div class="home-container h_container h-full bg-[#f7f7f7]">
    <!--    <ChainTitle @change="handleChainChange" />-->

    <van-sticky>
      <div class="p-20 bg-white">
        <van-search
          v-model="keyword"
          class="!p-0 w-full"
          :maxlength="16"
          :show-action="!!keyword"
          :clear-trigger="keyword ? 'always' : 'focus'"
          placeholder="请输入企业名称关键词"
          @input="
            () => {
              delayFn()
            }
          "
          @clear="
            () => {
              emptyflag = true
              keyword = ''
              filterDataSource = []
            }
          "
          @cancel="() => ((keyword = ''), (filterDataSource = []))"
        />
      </div>
    </van-sticky>

    <div v-show="keyword" class="bg-[#f7f7f7] w-full h-full p-24">
      <div v-if="!filterDataSource.length && emptyflag" class="flex flex-col items-center justify-center w-full h-full">
        <SvgIcon name="empty-data" class="w-[230px] h-[230px] mb-24" />
        <p class="text-secondary text-28">暂未检索到相关信息</p>
      </div>

      <div v-else>
        <div
          v-for="item in filterDataSource"
          :key="item.id"
          class="relative flex items-center px-24 py-16 mb-24 bg-white"
          @click="handleClickEntItem(item)"
        >
          <img :src="item.logo || EntDefault" class="w-[64px] h-[64px] mr-16" @error="() => (item.logo = EntDefault)" />
          <van-highlight
            :keywords="keyword"
            class="pr-20 text-28 text-primary"
            unhighlight-class="font-semibold"
            highlight-class="text-[#e72410] font-semibold"
            :source-string="item.ent_name"
          />
          <svg-icon name="arrow" class="absolute w-24 h-24 right-24" />
        </div>
      </div>
    </div>
    <div v-show="!keyword">
      <div class="flex flex-wrap w-full py-20 my-24 bg-white h-480">
        <div
          class="w-[33%] cursor-pointer flex justify-center items-center flex-col"
          v-for="(item, index) in routeList"
          :key="index"
          @click="go(item)"
        >
          <SvgIcon :name="`home-${index + 1}`" class="w-[112px] h-[112px] mb-14" />
          <span class="text-28 text-primary"> {{ item.name }}</span>
        </div>
      </div>

      <van-sticky>
        <div class="bg-white">
          <div class="p-24 flex items-center justify-between border-b border-solid border-[#eee]">
            <span class="font-semibold text-primary text-32">优质项目推荐</span>
            <div class="flex items-center text-28 text-secondary" @click="handleEdit">
              {{ LABEL_MAP[active] || '' }}
              <SvgIcon v-if="active !== '1'" class="ml-10" name="edit"></SvgIcon>
            </div>
          </div>

          <div class="w-702 h-72 bg-[#f7f7f7] mx-auto my-20 flex items-center justify-center">
            <van-popover
              class="w-702"
              v-model:show="showChainPopover"
              placement="bottom-end"
              @select="handleChainChange"
            >
              <template #reference>
                <span class="flex items-center justify-between text-28 text-tertiary w-702 px-24">
                  {{ defaultChain.text }}
                  <svg-icon
                    name="arrow-fill"
                    class="ml-8 transition-all duration-300"
                    :class="[showChainPopover ? 'rotate-180' : '']"
                  />
                </span>
              </template>

              <div class="w-full bg-white">
                <div
                  v-for="item in filterChainList"
                  :key="item.text"
                  class="p-24"
                  :class="[orderby.value === item.value && 'text-selected']"
                  @click="handleChainChange(item)"
                >
                  {{ item.text }}
                </div>
              </div>
            </van-popover>
          </div>
          <div>
            <van-tabs v-model:active="active" @change="handleTabChange">
              <van-tab :title="item.label" v-for="item in items" :key="item.key" :name="item.key">
                <div class="flex flex-wrap px-24 py-32 pb-12 pr-4 bg-white">
                  <div
                    class="w-[20%] bg-[#f7f7f7] p-20 flex flex-grow-1 justify-center items-center flex-grow break-keep text-primary text-26 mr-10 mb-20"
                    :class="[currentMenu?.key === sub.key ? 'highlight' : '']"
                    v-for="sub in item.children"
                    :key="sub.key"
                    @click="
                      () => {
                        pagination.current = 1
                        handleClickItem(sub)
                      }
                    "
                  >
                    {{ sub.label }}
                  </div>
                </div>
              </van-tab>
            </van-tabs>
          </div>

          <div class="flex items-center justify-between p-24 text-28 bg-grey">
            <div class="flex items-center">
              共为您推荐
              <span class="px-8 font-semibold text-selected">{{ pagination.total }}</span>
              家企业
            </div>

            <van-popover v-model:show="showPopover" placement="bottom-end" @select="onOrderSelect">
              <template #reference>
                <span class="flex items-center text-28 text-tertiary">
                  {{ orderby.text }}
                  <svg-icon
                    name="arrow-down"
                    class="ml-8 transition-all duration-300"
                    :class="[showPopover ? 'rotate-180' : '']"
                  />
                </span>
              </template>

              <div class="w-full bg-white">
                <div
                  v-for="item in actions"
                  :key="item.text"
                  class="p-24"
                  :class="[orderby.value === item.value && 'text-selected']"
                  @click="onOrderSelect(item)"
                >
                  {{ item.text }}
                </div>
              </div>
            </van-popover>
          </div>
        </div>
      </van-sticky>

      <div class="bg-grey min-h-[500px]">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh" v-if="pagination.total > 0">
          <van-list
            v-model:loading="loading"
            @load="loadmore"
            offset="1"
            :immediate-check="false"
            :finished="finished"
            :finished-text="entList.length ? '没有更多了' : undefined"
          >
            <EntCard :entList="entList" type="noThreeAddCollect" special />
          </van-list>
        </van-pull-refresh>
        <div v-else class="w-full h-500 flex justify-center items-center">
          <img src="@/assets/image/null.png" class="w-230 h-230" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="Home">
import EntDefault from '@/assets/image/ent-default.png'
import { getSearchEntList, getStrongChainNodes, getSupplementaryChainNodes } from '@/api/home/<USER>'
import { debounce, openReport } from '@/utils/index'
import { getCurrentInstance, watch } from 'vue'
import ChainTitle from './chainTitle.vue'
import EntCard from '@/components/EntCard/index.vue'
import { getLeadingEntList } from '@/api/advanceSearch/index'
import useEmitter from '@/hooks/useEmitter'
import { pageChangeHandler } from '@/utils'
import { filterChainMenuLists } from '@/contant/dict'
import { useUserStore } from '@/store/modules/user'
import { useRoute, useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()
const emitter = useEmitter()
const userStore = useUserStore()

const LABEL_MAP = {
  2: '强链环节选择',
  3: '补链环节选择',
  4: '存量企业管理',
}

const filterChainList = filterChainMenuLists.map(item => {
  return {
    value: item.value,
    text: item.label,
  }
})
const defaultChain = ref(filterChainList[0])
const showChainPopover = ref(false)

const actions = [
  { text: '默认排序', value: '' },
  { text: '成立日期从晚到早', value: 'ESDATE$DESC' },
  { text: '成立日期从早到晚', value: 'ESDATE$ASC' },
  { text: '注册资本从高到低', value: 'REGCAP$DESC' },
  { text: '注册资本从低到高', value: 'REGCAP$ASC' },
]
const currentIndustry = ref({})

const routeList = reactive([
  { name: '企业猎搜', path: '/high-search', hide: false },
  // { name: '产业链招商', path: '/chains', hide: false }, // 原强链补链
  { name: '产业链招商', path: '/industrial_chain', hide: false },
  { name: '供应链招商', path: '/supply-chain', hide: false }, // 原供应链查询
  { name: '榜单招商', path: '/authority', hide: false }, // 产业榜单
  // todo: 动态code
  { name: '地图招商', path: '/excavate', hide: false }, // 榜单招商
  { name: '储备项目', path: '/target-list', hide: false }, // 招商目标
])
const active = ref('1')
const filterDataSource = ref([])
const emptyflag = ref(true)
const showPopover = ref(false)

const leading_ent = {
  prop: 'leading_ent',
  value: true,
}

const technology = {
  prop: 'technology_types',
  value: ['HN', 'MST', 'G', 'PSN', 'U', 'TG'],
}

const listed_status = {
  prop: 'listed_status',
  value: ['LISTED_A', 'LISTED_HK', 'LISTED_TH', 'LISTED_NEW_THIRD_BOARD'],
}

const items = [
  {
    label: '智能推荐',
    key: '1',
    children: [
      { label: '高成长性企业', key: '1-1', prop: 'expand_status', value: ['A'] },
      { label: '龙头企业', key: '1-2', ...leading_ent },
      { label: '近期扩招', key: '1-3', prop: 'expand_status', value: ['C'] },
      { label: '近期扩张', key: '1-4', prop: 'expand_status', value: ['D'] },
    ],
  },
  {
    label: '强链场景',
    key: '2',
    children: [
      { label: '全部', key: '2-1' },
      { label: '龙头企业', key: '2-2', ...leading_ent },
      { label: '科技型企业', key: '2-3', ...technology },
      { label: '上市企业', key: '2-4', ...listed_status },
    ],
  },
  {
    label: '补链场景',
    key: '3',
    children: [
      { label: '全部', key: '3-1' },
      { label: '龙头企业', key: '3-2', ...leading_ent },
      { label: '科技型企业', key: '3-3', ...technology },
      { label: '上市企业', key: '3-4', ...listed_status },
    ],
  },
  {
    label: '存量挖掘',
    key: '4',
    children: [
      { label: '全部', key: '4-1', popupClassName: 'dropdown-menu-class' },
      { label: '龙头企业', key: '4-2', ...leading_ent },
      { label: '科技型企业', key: '4-3', ...technology },
      { label: '上市企业', key: '4-4', ...listed_status },
    ],
  },
]

const keyword = ref('')

const router = useRouter()

const handleChainChange = chain => {
  currentIndustry.value = chain
  showChainPopover.value = false
  defaultChain.value = chain
  const storeChain = {
    label: chain.text,
    value: chain.value,
  }
  userStore.setChain(storeChain)

  getChainCodes()
  pagination.current = 1
  getList({
    chain_codes: [currentIndustry.value?.value],
    expand_status: ['A'],
  })
}

onMounted(() => {
  handleChainChange(defaultChain.value)
})

const handleEdit = () => {
  const record = items.find(item => item.key === active.value)
  if (['2', '3'].includes(record.key)) {
    // pageChangeHandler('/scene-editing', {
    //   type: record.key === '2' ? 1 : 2,
    //   code: currentIndustry.value?.value,
    // })
    router.push({
      path: '/scene-editing',
      query: {
        type: record.key === '2' ? 1 : 2,
        code: currentIndustry.value?.value,
      },
    })
  }
  record.key === '4' && pageChangeHandler('/ent-management')
}

const entListParams = ref({})
const currentMenu = ref({})

const setFirstMenu = key => {
  const find = items.find(item => item.key === key)
  if (find) {
    currentMenu.value = find.children[0]
  }
}

// 设置初始选中的推荐筛选
setFirstMenu('1')

const handleTabChange = (name, title) => {
  setFirstMenu(name)
  pagination.current = 1
  pagination.total = 0
  handleClickItem(currentMenu.value)
}

const strongChainNodes = ref([])
const supplementaryChainNodes = ref([])

const getChainCodes = () => {
  const code = currentIndustry.value?.value
  if (currentIndustry.value?.value) {
    getStrongChainNodes(code).then(res => {
      strongChainNodes.value = res || []
    })

    getSupplementaryChainNodes(code).then(res => {
      supplementaryChainNodes.value = res || []
    })
  }
}

const handleClickItem = origin => {
  currentMenu.value = origin
  const params = {
    chain_codes: [currentIndustry.value?.value],
  }
  if (origin.prop) {
    params[origin.prop] = origin.value
  }
  if (origin.key.startsWith('2')) {
    params.chain_codes = strongChainNodes.value.length ? strongChainNodes.value : [currentIndustry.value?.value]
    params.exclude_codes = ['500000']
  }
  if (origin.key.startsWith('3')) {
    params.chain_codes = supplementaryChainNodes.value.length
      ? supplementaryChainNodes.value
      : [currentIndustry.value?.value]
    params.exclude_codes = ['500000']
  }

  entListParams.value = params
  getList(params)
}

emitter.on('sceneChanged', ({ type, selectedKeys }) => {
  if (Number(type) === 1) {
    strongChainNodes.value = selectedKeys
  } else {
    supplementaryChainNodes.value = selectedKeys
  }
  handleClickItem(currentMenu.value)
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
})
const entList = ref([])
const orderby = ref(actions[0])

const onOrderSelect = item => {
  orderby.value = item
  showPopover.value = false

  pagination.current = 1
  handleClickItem(currentMenu.value)
}

const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)

const getListPayloadHandler = params => {
  const payload = {
    page_size: pagination.pageSize,
    page_index: pagination.current || 1,
    sort: {
      name: orderby.value.value?.split('$')[0],
      order: orderby.value.value?.split('$')[1],
    },
    ...params,
  }
  if (!payload.sort.name) delete payload.sort
  return payload
}

const onRefresh = () => {
  pagination.current = 1
  handleClickItem(currentMenu.value)
  // refreshing.value = false
}
const loadmore = cb => {
  if (pagination.total <= entList.value.length) {
    finished.value = true
    return
  }
  pagination.current += 1
  handleClickItem(currentMenu.value)
}

// 解决储备项目返回首页，收藏状态问题
const route = useRoute()
let previousRoutePath = null
watch(
  () => route.path,
  (newPath, oldPath) => {
    if (oldPath === '/target-list' && newPath === '/home') {
      onRefresh()
    }
    previousRoutePath = oldPath
  },
  { immediate: true }
)

const getList = params => {
  proxy.$loading('加载中...')
  loading.value = true
  // noData.value = false
  if (refreshing.value) {
    entList.value = []
    refreshing.value = false
  }
  if (pagination.current === 1) {
    entList.value = []
  }
  const payload = getListPayloadHandler(params)
  getLeadingEntList(payload)
    .then(res => {
      if (pagination.current === 1) {
        entList.value = res.items
      } else {
        entList.value.push(...res.items)
      }
      pagination.total = res.count || 0
    })
    .finally(() => {
      loading.value = false
      proxy.$close()
    })
}

const handleSearch = item => {
  if (!keyword.value) {
    filterDataSource.value = []
    emptyflag.value = true
    return
  }
  proxy.$loading('加载中...')
  getSearchEntList({ ent_name: keyword.value })
    .then(res => {
      filterDataSource.value = res.datalist
      emptyflag.value = res.datalist.length === 0
    })
    .finally(() => {
      proxy.$close()
    })
}
const delayFn = debounce(handleSearch, 500)

const handleClickEntItem = item => {
  openReport(item)
}

const go = item => {
  const { path, hide } = item
  if (hide) {
    proxy.$showToast('当前功能暂未开放')
    return
  }
  const obj = { path: path, query: {} }
  // 要参数的话
  // if (path.includes('/excavate')) {
  //   obj.query = { chain_codes: currentIndustry.value.value }
  // }
  pageChangeHandler(path, obj.query)
}
</script>

<style lang="scss" scoped>
:deep(.van-tabs__wrap) {
  border-bottom: 1px solid #eee;

  // .van-tabs__nav {
  //   height: 88px;
  // }

  .van-tab {
    color: #74798c;
    &.van-tab--active {
      color: #2a72ff !important;
    }
  }
}

:deep(.van-tabs__line) {
  background: linear-gradient(90deg, #2a72ff 0%, #3994ff 71%);
  width: 40px;
  height: 6px;
}

:deep(.van-search) {
  background-color: #f7f7f7;

  .van-search__action {
    margin-left: 24px;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 32px;
      background-color: #dedede;
    }
  }
}

.highlight {
  background-color: #f5f7fe !important;
  color: #1e75db !important;
}
</style>
