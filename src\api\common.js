import { Post, Get } from '@/request'
import { hydrogenUrl, CHAIN_URL } from "@/api/constants";

const Api = {
    GetPreset: '/preset',
    AreaInfo: '/area-resource/area/info'
}

/**
 * @description: 获取预设产业链信息
 */
export function getPresetInfo() {
    return Get(`${CHAIN_URL}${Api.GetPreset}`)
}

/**
 * @description: 获取预设地区信息
 */
export function getAreaInfo() {
    return Get(`${hydrogenUrl}${Api.AreaInfo}`)
}
/**
 * 获取产业链（包含上中下游）
 */
// export const getChainCodes = data => Post(`${hydrogenUrl}/industryAnalysis/treeNode`, data)

export const getChainCodes = data => Get(`${CHAIN_URL}/industry/chain/code/A1A1/plus/tag?area_code=440306`)
