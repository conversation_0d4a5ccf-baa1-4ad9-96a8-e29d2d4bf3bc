<template>
    <div class="loading-component">
        <van-loading
            type="spinner"
            :size="size"
            :color="color"
            :text-size="textSize"
            :text-color="textColor"
            :vertical="vertical"
            >{{ text }}</van-loading
        >
    </div>
</template>
<script setup>
defineProps({
    size: {
        type: String,
        default: '24px'
    },
    color: {
        type: String,
        default: '#000'
    },
    text: {
        type: String,
        default: '加载中'
    },
    textSize: {
        type: String,
        default: '14px'
    },
    textColor: {
        type: String,
        default: '#000'
    },
    vertical: {
        type: Boolean,
        default: false
    }
})
</script>
<style lang="scss" scoped>
.loading-component {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: rgba(247, 247, 247, 1);
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
