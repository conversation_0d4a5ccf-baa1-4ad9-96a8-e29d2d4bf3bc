<template>
  <div class="leading-page">
    <van-sticky>
      <div class="p-20 bg-white">
        <van-search
          v-model="keyword"
          class="!p-0 !pr-12 w-full"
          :maxlength="16"
          :clear-trigger="keyword ? 'always' : 'focus'"
          placeholder="请输入企业名称"
          @input="() => {}"
          @search="handleSearch"
          @clear="
            () => {
              keyword = ''
              handleSearch()
            }
          "
        />
      </div>
      <div class="dropdown" ref="zhanWeiRef">
        <!-- <DropDown @submit="filterSubmit" :old-data="{}" isChain="eleseic_data" :filters="filters" /> -->
        <DropdownMenu @submit="handleSubmit" />
      </div>
      <div class="flex justify-between p-24 text-28 text-secondary">
        <span>
          共找到
          <span class="text-selected">{{ total }}</span>
          家
          <span>{{ text }}</span>
          企业
        </span>

        <van-popover v-model:show="showPopover" placement="bottom-end" @select="onOrderSelect">
          <template #reference>
            <span class="flex items-center text-28 text-tertiary">
              {{ orderby.label }}
              <svg-icon
                name="arrow-down"
                class="ml-8 transition-all duration-300"
                :class="[showPopover ? 'rotate-180' : '']"
              />
            </span>
          </template>

          <div class="w-full bg-white">
            <div
              v-for="item in actions"
              :key="item.label"
              class="p-24"
              :class="[orderby.value === item.value && 'text-selected']"
              @click="onOrderSelect(item)"
            >
              {{ item.label }}
            </div>
          </div>
        </van-popover>
      </div>
    </van-sticky>

    <!-- <div class="null" v-else></div> -->
    <div :style="{ height: listHeight + 'px' }" class="list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          @load="loadmore"
          offset="1"
          :immediate-check="false"
          :finished="finished"
          :finished-text="entList.length ? '没有更多了' : undefined"
        >
          <EntCard
            :entList="entList"
            :query="keyword"
            special
            :max-tag-count="6"
            showContacts
            :onClickEntName="onClickEntName"
            type="noThreeAddCollect"
          >
          <template #after-name="{data}">
            <span class="break-keep text-24 border self-start border-#eee px-10 ml-4 text-secondary">{{ getQualityLevel(data.score) }}</span>
          </template>
        </EntCard>
        </van-list>
      </van-pull-refresh>
      <div class="noData" v-if="noData">
        <img src="@/assets/image/null.png" alt="" />
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup name="LeadingEnterprises">
import EntCard from '@/components/EntCard/index.vue'
import { getReserveList } from '@/api/home/<USER>'
import { formatterParams } from '@/utils/index'
import DropdownMenu from './dropdownMenu.vue'
import { useRouter } from 'vue-router'
import { pageChangeHandler } from '@/utils'

const { proxy } = getCurrentInstance()
const props = defineProps({
  showTotal: {
    type: Boolean,
    default: true,
  },
  //企业数量文本展示
  text: {
    type: String,
    default: undefined,
  },
  openNow: {
    type: Boolean,
    default: true,
  },
  special: {
    //用于做一些个性化需求
    type: String,
    default: '',
  },
})

const router = useRouter()
const emits = defineEmits(['clickItem'])

let params = reactive({ page_index: 1, page_size: 10 })
const total = ref(0)
const entList = ref([])
const loading = ref(false) //是否处于加载状态
const finished = ref(false) //是否已加载完成
const refreshing = ref(false) //下拉刷新加载
const noData = ref(false)
const refreshParams = ref({})
const zhanWeiRef = ref(null) //拿到列表高度
const listHeight = ref(0)
const showPopover = ref(false)
const keyword = ref('')
const searchParams = ref({})

const actions = [
  { label: '默认排序', value: '' },
  { label: '推荐程度由高到低', value: 'score desc' },
  { label: '推荐程度由低到高', value: 'score asc' },
  { label: '成立日期从晚到早', value: 'register_date desc' },
  { label: '成立日期从早到晚', value: 'register_date asc' },
  { label: '注册资本从高到低', value: 'register_capital desc' },
  { label: '注册资本从低到高', value: 'register_capital asc' },
]
const orderby = ref(actions[0])

const HIGH_QUALITY = '高质量';
const MIDDLE_QUALITY = '中等质量';
const LOW_QUALITY = '低质量';

const getQualityLevel = (score) => {
  if (score >= 70) {
    return HIGH_QUALITY;
  } else if (score < 70 && score >= 40) {
    return MIDDLE_QUALITY;
  } else {
    return LOW_QUALITY;
  }
};


const onRefresh = () => {
  params.page_index = 1
  getList()
}

const loadmore = function () {
  if (total.value <= entList.value.length) {
    finished.value = true
    return
  }
  params.page_index += 1
  getList()
}

const handleSearch = () => {
  params.page_index = 1
  getList()
}

const getList = function () {
  if (params.page_index === 1) {
    entList.value = []
  }
  proxy.$loading('加载中')
  loading.value = true
  noData.value = false
  if (refreshing.value) {
    entList.value = []
    refreshing.value = false
  }

  const filter = formatterParams(
    {
      enterprise_name: keyword.value,
      ...searchParams.value,
      // ...omit(params, ['start', 'end']),
      // ...checkParams,
      // register_capital: getregisterCapitalParam(params.start, params.end),
    },
    {
      enterprise_name: 'like',
      region_code: 'in',
      register_capital: 'between',
      nic_code: 'in',
    },
    undefined,
    true
  )
  const payload = {
    $filter: filter,
    $count: true,
    $offset: (params.page_index - 1) * params.page_size,
    $limit: params.page_size,
  }

  if (orderby.value) {
    payload.$orderby = orderby.value.value
  }

  getReserveList(payload)
    .then(res => {
      const items = res.items.map(item => {
        return {
          collect: true,
          ...item,
          ...item.ent_vo,
        }
      })
      if (params.page_index === 1) {
        entList.value = items || []
      } else {
        entList.value.push(...items)
      }
      total.value = res.count || 0
      noData.value = !res.count
      proxy.$close()
    })
    .finally(() => {
      loading.value = false
      refreshing.value = false
      proxy.$close()
    })
}

const onOrderSelect = item => {
  orderby.value = item
  showPopover.value = false

  params.page_index = 1
  getList()
}

const handleSubmit = value => {
  params.page_index = 1
  searchParams.value = value
  getList()
}

const onClickEntName = item => {
  pageChangeHandler('/target-detail', {
    ent_id: item.ent_id,
    ent_name: item.ent_name,
    tab: 0,
  })
}

onMounted(() => {
  getList()

  nextTick(() => {
    // 计算列表滚动高度
    const { height: h } = useWindowSize()
    const { top, height } = useElementBounding(zhanWeiRef, { immediate: true, windowResize: true }) //第一次拿不到
    listHeight.value = h.value - top.value - height.value - (props.showTotal ? 48 : 10)
  })
})
</script>

<style lang="scss" scoped>
.leading-page {
  height: 100%;
  background: #f7f7f7;
  .dropdown {
    // border-top: 2px solid #eee;
    display: flex;
    height: var(--van-dropdown-menu-height);
  }
  .list {
    // height: calc(100vh - 280px);

    overflow: auto;
    -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
}
.noData {
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 230px;
    height: 230px;
    transform: translateY(-180px);
  }
  div {
    color: #74798c;
    margin-top: 24px;
    transform: translateY(-180px);
  }
}
.null {
  height: 20px;
}

:deep(.van-search) {
  background-color: #f7f7f7;

  .van-search__action {
    margin-left: 24px;
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 2px;
      height: 32px;
      background-color: #dedede;
    }
  }
}
</style>
