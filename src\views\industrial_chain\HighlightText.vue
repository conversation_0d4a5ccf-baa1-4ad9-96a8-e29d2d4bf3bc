<template>
  <span v-html="highlightedText"></span>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  text: {
    type: String,
    required: true,
  },
  keyword: {
    type: String,
    default: '',
  },
  highlightClass: {
    type: String,
    default: 'highlight',
  },
})

// 计算高亮后的文本
const highlightedText = computed(() => {
  if (!props.keyword || !props.keyword.trim()) {
    return props.text
  }

  const keyword = props.keyword.trim()
  const regex = new RegExp(`(${keyword})`, 'gi')

  return props.text.replace(regex, `<span class="${props.highlightClass}">$1</span>`)
})
</script>

<style scoped>
:deep(.highlight) {
  color: #076ee4 !important;
}
</style>
