.upandown-page {
  position: fixed;
  width: 100%;
  height: 100%;
  background-color: white;
  left: 0;
  top: 0;
}
rect {
  cursor: pointer;
}
.flow-out {
  fill: none;
  stroke: #bfb8b8;
  stroke-width: 1.5;
  stroke-dasharray: 10 10;
  stroke-dashoffset: 0;
  animation: strokeout 2s infinite linear;
}

@keyframes strokeout {
  100% {
    stroke-dashoffset: -100;
    /* 如果反向移动改为-500 */
  }
}

.flow-in {
  fill: none;
  stroke: #bfb8b8;
  stroke-width: 1.5;
  stroke-dasharray: 10 10;
  stroke-dashoffset: 0;
  animation: strokein 2s infinite linear;
}

@keyframes strokein {
  100% {
    stroke-dashoffset: 100;
    /* 如果反向移动改为-500 */
  }
}
.tooltipUp {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 4.5333rem;
  height: auto;
  font-family: simsun;
  font-size: 0.1867rem;
  background: #ffffff;
  border-radius: 0.1067rem;
  transition: 0.5s;
  transition-property: opacity, z-index;
  opacity: 0;
  -moz-user-select: none; /*火狐*/

  -webkit-user-select: none; /*webkit浏览器*/

  -ms-user-select: none; /*IE10*/

  -khtml-user-select: none; /*早期浏览器*/

  user-select: none;
}

.tooltip-box {
  max-width: 4.5333rem;
  overflow: hidden;
  border-radius: 0.1067rem;
  box-shadow: 0px 0px 0.1067rem 1px rgba(32, 38, 58, 0.3);
  background-color: #fff;
  padding: 0.2667rem;
  display: flex;
  img {
    width: 0.72rem;
    height: 0.72rem;
    box-shadow: 0 0 0.0533rem 0 rgba(0, 0, 0, 0.086);
  }
  .info {
    margin-left: 0.2rem;
    p {
      font-size: 0.1867rem;
      color: #6f6f79;
      margin-top: 0.0667rem;
      &:first-child {
        margin-top: 0;
      }
      .value {
        color: #3d3d47;
      }
    }
    .tit {
      font-size: 0.2133rem;
      font-weight: 600;
      color: #282d30;
      display: flex;
      -moz-user-select: none; /*火狐*/

      -webkit-user-select: none; /*webkit浏览器*/

      -ms-user-select: none; /*IE10*/

      -khtml-user-select: none; /*早期浏览器*/

      user-select: none;
      span:first-child {
        width: 2.4rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }
      .tag {
        font-size: 0.16rem;
        font-weight: normal;
        padding: 0 0.0533rem;
        line-height: 0.32rem;
        -moz-user-select: none; /*火狐*/

        -webkit-user-select: none; /*webkit浏览器*/

        -ms-user-select: none; /*IE10*/

        -khtml-user-select: none; /*早期浏览器*/

        user-select: none;
      }
    }
  }
}
.personal-info {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  max-width: 2.4rem;
  padding: 0.2667rem;
  font-size: 0.1867rem;
  color: #6f6f79;
  overflow: hidden;
  box-shadow: 0px 0px 0.1067rem 1px rgba(32, 38, 58, 0.3);
  border-radius: 0.1067rem;
  background-color: #fff;
  -moz-user-select: none; /*火狐*/

  -webkit-user-select: none; /*webkit浏览器*/

  -ms-user-select: none; /*IE10*/

  -khtml-user-select: none; /*早期浏览器*/

  user-select: none;
  p {
    margin-top: 0.0667rem;
    &:first-child {
      margin-top: 0;
    }
    .value {
      color: #3d3d47;
    }
  }
}

// 这里只考虑横屏（也是单页），没做竖屏
// loading样式
// .van-popup--center-cover {
//   top: 50%;
//   left: 50%;
//   transform: translate3d(-50%, -50%, 0) scale(0.5) !important;
// }
// .van-popup--center {
//   top: 50%;
//   left: 50%;
//   transform: translate3d(-50%, -50%, 0) scale(0.5) !important;
// }
