<template>
  <div class="w-full h-full flex flex-col bg-[#f7f7f7]">
    <div class="flex-shrink-0 bg-white relative">
      <!--  -->
      <div class="h-[28px] flex justify-center items-end" v-if="!isPage">
        <div class="h-[8px] w-[200px] bg-[#dedede]"></div>
      </div>
      <div
        class="h-[88px] flex items-center text-[28px] text-#74798C pl-[24px]"
        :class="[isPage ? 'justify-start' : 'justify-center']"
      >
        附近找到<u class="text-#3E7BFA px-[8px] font-semibold">{{ total }}</u
        >家相关企业
      </div>
      <!--  -->
      <div class="h-[20px] bg-#f7f7f7 w-full" v-if="isPage"></div>
      <div class="h-[1px] bg-#eee w-full" v-if="!isPage"></div>
      <!-- 盖一层用来判断拖拽  id就是外面用来判断的-->
      <div
        @click="emits('clickItem')"
        class="absolute w-full h-full z-1 left-0 top-0 bg-transparent"
        id="tuoZhaui"
      ></div>
    </div>
    <!-- 列表 -->
    <div
      class="h-full overflow-y-scroll scroll-smooth"
      ref="listRef"
      style="scrollbar-width: none; -ms-overflow-style: none"
    >
      <PullDownList
        ref="pullListRef"
        apiUrl="/hdzhzs/business_admin/v0.1/maps/zi_ding_yi"
        :queryParams="queryParams"
        :requestAuto="false"
        :callback="callback"
        requestType="page_index"
      >
        <template #data="{ sourceData }">
          <!--根据传来的距离 动态计算出距离 -->
          <EntCard
            type="noThreeAddCollect"
            :entList="sourceData"
            :query="highlightQuery"
            :openNow="false"
            :special="release"
          />
        </template>
      </PullDownList>
    </div>
  </div>
</template>

<script setup>
import PullDownList from '@/components/PullDownList/index.vue'
import EntCard from '@/components/EntCard/index.vue'
import { openReport } from '@/utils'

const emits = defineEmits(['clickItem'])
const props = defineProps({
  isPage: {
    type: Boolean,
    default: true,
  },
  params: {
    type: Object,
    default: () => ({}),
  },
  isRelease: {
    type: Boolean,
    default: false,
  },
})
const route = useRoute()
const highlightQuery = ref('')
const queryParams = ref({})
const pullListRef = ref(null)
const total = ref(0)
const grids = ref([0, 0])

const release = computed(() => {
  return props.isRelease
})
// 计算距离 km
const GetDistance = (lat1, lng1, lat2, lng2) => {
  const radLat1 = (lat1 * Math.PI) / 180.0
  const radLat2 = (lat2 * Math.PI) / 180.0
  const a = radLat1 - radLat2
  const b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0
  let s =
    2 *
    Math.asin(
      Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2))
    )
  s = s * 6378.137 // EARTH_RADIUS;
  s = Math.round(s * 10000) / 10000
  return s
}
const callback = source => {
  let lng, lat
  lng = queryParams.value?.grids?.[0]?.lon
  lat = queryParams.value?.grids?.[0]?.lat

  if (!lng || !lat) return source
  source.forEach(item => {
    const distance = GetDistance(item.location.lat, item.location.lon, lat, lng)
    item.distance = (+distance).toFixed(2)
  })
  return source
}

watch(
  () => props.params,
  val => {
    if (!Object.keys(val).length) return
    const oldParams = JSON.parse(JSON.stringify(val))
    queryParams.value = oldParams
    nextTick(() => {
      pullListRef.value?.onRefreshFn()
    })
  },
  {
    deep: true,
    immediate: true,
  }
)
watch(
  () => pullListRef.value?.total,
  val => {
    total.value = val || 0 // 这里0也是要的
  }
)
</script>

<style lang="scss" scoped>
:deep(.noData) {
  justify-content: flex-start;
  background: white !important;
  height: 100% !important;
  padding-top: 60px;
}
</style>
