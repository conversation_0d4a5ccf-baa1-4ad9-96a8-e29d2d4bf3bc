<template>
  <div @touchmove.prevent.stop>
    <!-- 当前地图位置  -->
    <div class="flex items-center px-[24px] pt-[46px] pb-[28px]">
      <SvgIcon name="location" class="w-[32px] h-[32px] mr-[12px]" />
      <div class="text-[28px] sle w-[660px]" style="color: #20263a">{{ address }}</div>
    </div>
    <!-- 滑块 + 位置 -->
    <div class="flex pl-[24px] pr-[20px] items-center">
      <div class="w-[582px] mr-[20px] h-[12px]">
        <van-slider
          v-model="newSliderVal"
          :max="10"
          :min="1"
          :step="1"
          bar-height="6"
          active-color=" linear-gradient( 272deg, #076EE4 0%, #53A2FF 100%)"
          inactive-color="#F7F7F7"
          @change="val => emits('change', val)"
        >
          <template #button>
            <img src="@/assets/image/yzwj/dot.png" class="w-[28px] h-[28px] ml-[26px]" />
          </template>
        </van-slider>
      </div>
      <div class="text-#74798C text-[28px] ml-[20px]">{{ newSliderVal }}km</div>
    </div>
    <div class="flex px-[24px] justify-between mt-[24px]">
      <div
        v-for="i in distance"
        :key="i.value"
        class="w-[122px] h-[56px] flex items-center justify-center text-[24px] text-#20263a bg-#f7f7f7 rounded-[8px]"
        :class="[newSliderVal === i.value && 'bg-[#3E7BFA] text-[white]']"
        @click="emits('change', i.value)"
      >
        {{ i.name }}
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  sliderVal: {
    type: Number,
  },
  address: {
    type: String,
    default: '',
  },
})
const distance = ref([
  { name: '2km', value: 2 },
  { name: '4km', value: 4 },
  { name: '6km', value: 6 },
  { name: '8km', value: 8 },
  { name: '10km', value: 10 },
])
const emits = defineEmits(['change', 'update:sliderVal'])
const newSliderVal = useVModel(props, 'sliderVal', emits)
// console.log('ddd', newSliderVal)
</script>

<style lang="scss" scoped></style>
