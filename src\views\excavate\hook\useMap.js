import { ref, onMounted } from 'vue'
import centerMarker from '@/assets/image/yzwj/centerMarker.png'
import yuanqu from '@/assets/image/yzwj/yuanqu.png'
import qiye from '@/assets/image/yzwj/qiye.png'

// 地图的一些方法
const useMethodMap = () => {
  if (!window?.AMap) return
  const genCoder = ref(null)
  const isInitialized = ref(false)

  // 确保首次正确加载genCoder
  const initGeocoder = () => {
    return new Promise((resolve) => {
      if (isInitialized.value) {
        resolve(genCoder.value)
        return
      }

      AMap.plugin(['AMap.Geocoder'], () => {
        genCoder.value = new AMap.Geocoder({ city: '全国' })
        isInitialized.value = true
        resolve(genCoder.value)
      })
    })
  }

  // 传地区得到地区经纬度
  const nameConversionLongLat = async (area_name) => {
    // 确保genCoder有值
    await initGeocoder()

    return new Promise((resolve, reject) => {
      genCoder.value.getLocation(area_name, (status, result) => {
        if (status === 'complete' && result.geocodes.length) {
          resolve(result.geocodes[0].location)
        } else {
          reject(new Error(`Geocoding failed: ${status}`))
        }
      })
    })
  }

  // 通过经纬度拿到中心位置地址
  const longLatConversionName = lnglat => {
    return new Promise((resolve, reject) => {
      genCoder.value.getAddress(lnglat, (status, result) => {
        if (status === 'complete' && result.info === 'OK') {
          resolve(result.regeocode.formattedAddress)
        }
      })
    })
  }

  onMounted(() => {
    // 创建地理编码对象
    AMap.plugin(['AMap.Geocoder'], () => {
      genCoder.value = new AMap.Geocoder({ city: '全国' })
    })
  })
  return { genCoder, nameConversionLongLat, longLatConversionName }
}

// 首页地图相关
const useIndexMap = ({ emits, map, data }) => {
  const curPosition = ref([])
  // 初始化 并在地图上加一个mark -------- 地图首页添加中心坐标
  const addCenterMarker = center => {
    // 显示定位按钮
    data.geolocation = new AMap.Geolocation({
      showButton: true,
      enableHighAccuracy: true, // 开启高精度定位
      panToLocation: false, //定位成功后是否自动移动到响应位置
      GeoLocationFirst: false,
      timeout: 10000, // 超时时间
      zoomToAccuracy: false, // 定位成功后是否自动调整级别
      position: {
        right: '5px',
        top: '290px',
      },
    })
    const centerPosition = new AMap.LngLat(center[0], center[1])
    map.value.setCenter(centerPosition)
    map.value.setZoom(12)
    if (data.centerMarker) {
      data.centerMarker.setMap(null)
    }
    const centerIcon = new AMap.Icon({
      image: centerMarker,
      size: new AMap.Size(30, 39),
      imageSize: new AMap.Size(30, 39),
    })
    data.centerMarker = new AMap.Marker({
      position: centerPosition,
      icon: centerIcon,
      map: map.value,
      zIndex: 100,
      offset: [-15, -20],
    })
    // 获取位置
    data.geocoder.getAddress(centerPosition, function (status, result) {
      if (status === 'complete' && result.regeocode) {
        const address = result.regeocode.formattedAddress
        emits('update:modelValue', address)
      }
    })
  }
  return { addCenterMarker, curPosition }
}

// 地图列表页
const useListMap = ({ emits, map, data }) => {
  const markersAry = []
  const tempAreaNode = ref(null)

  const clearDistrictExplorer = () => {
    if (data.districtExplorer) {
      data.districtExplorer.clearFeaturePolygons()
    }
  }
  const drawCircle = center => {
    // 清空地图
    clearDistrictExplorer()

    const AMapcenter = new AMap.LngLat(center[0], center[1])
    // 设置地图中心位置
    map.value.setCenter(AMapcenter)
    // 清除之前的圆
    if (data.circle) {
      data.circle.setMap(null)
    }
    // 绘制新的圆
    data.circle = new AMap.Circle({
      center: AMapcenter, // 圆心位置
      radius: data.radius, // 半径，单位：米
      strokeColor: '#076EE4', // 线颜色
      strokeWeight: 1, // 线宽
      fillColor: '#076EE4', // 填充色
      fillOpacity: 0.2, // 填充透明度
    })
    map.value.add(data.circle)
    // 绘制圆心的标记点
    const centerIcon = new AMap.Icon({
      image: centerMarker,
      size: new AMap.Size(30, 39),
      imageSize: new AMap.Size(30, 39),
    })

    if (data.centerMarker) {
      data.centerMarker.setMap(null)
    }
    if (data.type !== 'ent') {
      data.centerMarker = new AMap.Marker({
        position: AMapcenter,
        icon: centerIcon,
        map: map.value,
        zIndex: 100,
        offset: [-15, -20],
      })
    }
    // 获取位置
    data.geocoder.getAddress(AMapcenter, function (status, result) {
      if (status === 'complete' && result.regeocode) {
        const address = result.regeocode.formattedAddress
        // console.log('ggggg', address)
        emits('update:modelValue', address)
      }
    })
  }

  const drawMarkers = (center, markers) => {
    // 清除之前的标记点
    markersAry.forEach(marker => {
      if (marker) {
        marker.setMap(null)
      }
    })

    // 绘制新的标记点
    markers.forEach(marker => {
      const distance = AMap.GeometryUtil.distance(marker.position, center)
      if (Math.floor(distance) <= data.circle.getRadius()) {
        const icon = new AMap.Icon({
          image: data.type === 'park' ? yuanqu : qiye,
          size: new AMap.Size(30, 39),
          imageSize: new AMap.Size(30, 39),
        })

        const newMarker = new AMap.Marker({
          position: marker.position,
          icon: icon,
          map: data.circle.getMap(),
          offset: [-15, -39],
        })

        newMarker.on('click', () => {
          if (data.infoWindow) {
            data.infoWindow.close()
          }
          data.infoWindow = new AMap.InfoWindow({
            anchor: 'bottom-center',
            content:
              data.type === 'park'
                ? `<div style="font-size: 12px;color:#74798C;text-align: left">
                <div style="font-size: 14px;color:#20263A">${marker.content}</div>
                <div>${marker.count}</div>
                </div>`
                : `<div style="font-size: 12px;color:#74798C;text-align: left">
                <div style="font-size: 14px;color:#20263A">${marker.content}</div>
                </div>`,
            offset: [0, 0],
            autoMove: true,
          })
          data.infoWindow.open(data.circle.getMap(), marker.position)
          setTimeout(() => {
            if (data.infoWindow) {
              data.infoWindow.close()
            }
          }, 2000) // 2秒后关闭信息窗口
        })
        markersAry.push(newMarker)
      }
    })
  }

  const areaFw = (adcode, center) => {
    // 清空圆和mark
    if (data.circle) {
      data.circle.setMap(null)
    }
    if (data.centerMarker) {
      data.centerMarker.setMap(null)
    }
    markersAry.forEach(marker => {
      if (marker) {
        marker.setMap(null)
      }
    })

    if (!adcode) return

    const centerPosition = new AMap.LngLat(center[0], center[1])
    data.geocoder.getAddress(centerPosition, function (status, result) {
      if (status === 'complete' && result.regeocode) {
        const address = result.regeocode.formattedAddress
        emits('update:modelValue', address)
      }
    })
    //加载DistrictExplorer，loadUI的路径参数为模块名中 'ui/' 之后的部分
    AMapUI.loadUI(['geo/DistrictExplorer'], function (DistrictExplorer) {
      //启动页面
      if (!data.districtExplorer) {
        data.districtExplorer = new DistrictExplorer({
          map: map.value, //关联的地图实例
        })
      }
      //清除已有的绘制内容
      data.districtExplorer.loadAreaNode(adcode, function (error, areaNode) {
        if (error) {
          console.error(error)
          return
        }
        clearDistrictExplorer()
        tempAreaNode.value = areaNode
        //绘制父级区划，仅用黑色描边
        data.districtExplorer.renderParentFeature(areaNode, {
          cursor: 'default',
          bubble: true,
          strokeColor: '#076EE4', // 线颜色
          // fillColor: null,
          strokeWeight: 1, //线宽
          fillColor: '#076EE4', // 填充色
          fillOpacity: 0.2, // 填充透明度
        })

        //更新地图视野以适合区划面
        map.value.setFitView(data.districtExplorer.getAllFeaturePolygons())
      })
    })
  }

  const init = ({ newData, adcode }) => {
    // const newData = {
    //   center: [116.397428, 39.90923], // 新的圆心坐标
    //   markers: [
    //     // 新的标记数组
    //     { position: [116.397528, 39.90923], content: 'Marker 2', count: 0 },
    //   ],
    // }

    if (adcode) {
      // 地区 画行政区域
      areaFw(adcode, newData.center)
      return
    }
    // 绘制圆
    drawCircle(newData.center)
    // 添加标记点
    drawMarkers(newData.center, newData.markers)
  }
  return { init }
}

export { useIndexMap, useListMap, useMethodMap }
