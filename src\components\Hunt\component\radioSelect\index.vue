<script setup>
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  mark: {
    type: String,
  },
  // 选项数据
  dataType: {
    type: Array,
    default: () => [],
  },
  // 当前选项值 --- 布尔或者字符串
  oldData: {
    type: Array,
    default: () => [],
  },
  type: {
    // 用来自定回填用数据里那个字段
    type: String,
    require: true,
  },
})
const emits = defineEmits(['close', 'submit'])
const sourceData = ref({})

const handleSelect = names => {
  sourceData.value.forEach(element => {
    const { name, active } = element
    element.active = false
    if (names === name) {
      element.active = !active
    }
  })
  emits('submit', { mark: props.mark, checkedList: sourceData.value.filter(i => i.active) })
  emits('close')
}
watch(
  () => props.visible,
  val => {
    if (!val) return
    sourceData.value = JSON.parse(JSON.stringify(props.dataType))
    if (props.oldData?.length) {
      sourceData.value = sourceData.value.map(item => {
        item.active = false
        if (item[props.type] === props.oldData[0][props.type]) {
          item.active = true
        }
        return item
      })
    }
  }
)
</script>
<template>
  <div>
    <div class="self-popup-picker">
      <div
        :class="['item', { 'item-active': active }]"
        v-for="{ name, value, active } in sourceData"
        @click="handleSelect(name)"
        :key="value"
      >
        {{ name }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/*选择项样式*/
.self-popup-picker {
  .item {
    width: 100%;
    height: 104px;
    line-height: 104px;
    font-weight: 400;
    font-size: 28px;
    color: #4c4c4c;
    position: relative;
    // padding-left: 32px;
    text-align: center;
    &::after {
      content: '';
      display: inline-block;
      width: calc(100% - 48px);
      height: 0.5px;
      background: #eeeeee;
      position: absolute;
      bottom: 0;
      left: 24px;
    }
    &:last-child {
      &::after {
        background: #fff;
      }
    }
    &-active {
      background: #e7f1fd;
      color: #076ee4;
    }
  }
}
</style>
