import { onUnmounted, nextTick } from 'vue'

const instances = new Set()

const handleClickOutside = event => {
  for (const instance of instances) {
    const isInput = event.target.nodeName === 'INPUT'
    const isInside = instance.el.contains(event.target)
    const isChildOfEl = instance.el.contains(event.target) || event.target === instance.el

    if (!isChildOfEl && !isInput) {
      instance.callback()
    }
  }
}
document.addEventListener('click', handleClickOutside)

const clickOutside = {
  mounted(el, binding, ctx) {
    const instances = new Set()

    const setHandler = () => {
      const handler = event => {
        if (el.contains(event.target)) {
          return
        }

        const vm = ctx.instance
        if (vm) {
          vm[binding.value]()
        }
      }

      instances.add(handler)
      document.addEventListener('click', handler)
    }

    const removeHandler = () => {
      instances.forEach(handler => {
        document.removeEventListener('click', handler)
      })
      instances.clear()
    }

    el._clickOutsideSetHandler = setHandler
    el._clickOutsideRemoveHandler = removeHandler
    el._removeHandler = removeHandler

    nextTick(() => {
      setHandler()
    })
  },
  unmounted(el) {
    document.removeEventListener('click', el.__clickOutsideHandler)
    document.removeEventListener('click', el._clickOutsideSetHandler)
    el._removeHandler()
    delete el._removeHandler
  },
}

export default clickOutside
// input指令
let fixedInput = {
  mounted: function (el) {
    var ua = navigator.userAgent.toLowerCase()
    var isIOS = /iphone|ipad|ipod/.test(ua)
    var isAndroid = /android/.test(ua)
    var input = el.tagName.toLowerCase() === 'input' ? el : el.querySelector('input')
    var prevScrollTop = 0
    var parentScrollTop = 0

    function getParentScrollTop(el) {
      // 获取父级滚动条的位置
      while (el && el.parentNode) {
        el = el.parentNode
        if (el.scrollTop) {
          parentScrollTop = el.scrollTop
          break
        }
      }
    }

    if (isIOS || isAndroid) {
      input.addEventListener('focus', function () {
        // 获取当前页面的滚动位置和父级滚动条的位置
        prevScrollTop = document.documentElement.scrollTop || document.body.scrollTop
        getParentScrollTop(input)
        // 计算输入框在键盘弹起后应该被移动的距离
        var inputOffsetTop = input.getBoundingClientRect().top + prevScrollTop - parentScrollTop
        var keyboardHeight = window.innerHeight * 0.5
        var moveDistance = inputOffsetTop - keyboardHeight
        // 将页面和父级滚动到正确的位置，使输入框不被键盘遮挡
        if (moveDistance > 0) {
          document.documentElement.scrollTop = moveDistance
          document.body.scrollTop = moveDistance
          if (parentScrollTop > 0) {
            el.parentNode.scrollTop = parentScrollTop + moveDistance - inputOffsetTop
          }
        }
      })
      input.addEventListener('blur', function () {
        // 恢复页面和父级滚动的位置
        document.documentElement.scrollTop = prevScrollTop
        document.body.scrollTop = prevScrollTop
        if (parentScrollTop > 0) {
          el.parentNode.scrollTop = parentScrollTop
        }
      })
    }
  },
}

export { fixedInput, clickOutside }
