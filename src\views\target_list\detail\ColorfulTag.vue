<template>
  <section v-show="data.length !== 0" class="flex label-list-wrapper">
    <!-- width: `${breadth}%`, -->
    <div class="label-list" :style="{  ...wrapperStyle }">
      <div v-for="(item, index) in list" :key="index" class="item" :style="itemStyle">
        <!-- <a-tooltip placement="bottom" :getPopupContainer="el => el.parentNode">
          <template v-if="tooltip && item.note" #title>
            {{ item.note }}
          </template>
          <span v-if="item.name" :style="{ cursor: pointer }" :class="['label', item.className]">
            {{ item.name }}
          </span>
        </a-tooltip> -->
        <span :class="['label', item.className]">{{ item.name }}</span>
      </div>
    </div>
  </section>
</template>

<script>
const COLOR_TAG_MAP = {
  darkBlue: ['世界500强', '中国500强'],
  wathetBlue: ['央企', '地方重点国企', '央企一级子公司', '央企二级子公司'],
  yellow: ['纳税A级企业', '纳税B级企业'],
  green: ['5A旅行社'],
  purple: [
    '国家高新技术企业',
    '中关村高新技术企业',
    '科技型中小企业',
    '国家高新技术企业(过期)',
    '技术先进型服务企业',
    '瞪羚企业',
    '牛羚企业',
    '独角兽企业',
    '众创空间',
    '科技企业孵化器',
    '企业技术中心',
    '科技小巨人',
    '专精特新企业',
    '雏鹰企业',
    '专精特新小巨人企业',
    '技术创新示范企业',
  ],
  brown: ['工信部制造业单项冠军'],
  onBusiness: ['在营', '临时', '待迁入'],
  comColor: ['企业', '组织机构', '个体', '全球企业'],
  miniCom: ['微型', '大型', '小型', '中型'],
  logOff: [
    '注销',
    '吊销',
    '迁出',
    '撤销',
    '清算中',
    '停业',
    '拟注销',
    '非正常户',
    '吊销未注销',
    '吊销已注销',
    '正在注销',
    '经营期限届满',
    '其他',
  ],
}
export default {
  props: {
    /**
     * interface TagItem {
     *  name?: string,
     *  note?: string,
     *  className?: string,
     * }
     *
     * type Data = TagItem[]
     */
    data: {
      type: Array,
      default: () => [],
      require: false,
    },
    wrapperStyle: {
      type: Object,
      default: () => ({}),
    },
    itemStyle: {
      type: Object,
      default: () => {},
    },
    tooltip: {
      type: Boolean,
      default: false,
    },
    /* 控制标签行 盒子的百分比 */
    // breadth: {
    //   type: Number,
    //   default: 100,
    //   validator: value => {
    //     return value >= 50 && value <= 90
    //   },
    // },
    pointer: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    list() {
      const result = this.data
        .map(tag => {
          const item = { ...tag }
          // 给不同tag添加不同颜色
          for (const color in COLOR_TAG_MAP) {
            if (COLOR_TAG_MAP[color].includes(tag.name)) {
              item.className = color
              break
            } else {
              // 未匹配的使用普遍颜色
              item.className = 'normal'
            }
          }

          return item
        })
        .filter(item => Boolean(item.name))

      return result
    },
  },
  methods: {},
}
</script>

<style lang="less" scoped>
// @import url('@/style/mixins.less');

.label-list-wrapper {
  position: relative;
  width: 100%;

  .label-list {
    .item {
      display: inline-block;
      margin-right: 12px;

      .label {
        display: flex;
        align-items: center;
        padding: 0px 10px;
        font-size: 24px;
        min-height: 36px;
        word-break: normal;
      }
    }
  }

  // /deep/.ant-tooltip-arrow::before {
  //   width: 6px;
  //   height: 6px;
  //   background-color: #fff;
  // }

  // /deep/.ant-tooltip-inner {
  //   max-width: 174px;
  //   max-height: 100px;
  //   padding: 14px;
  //   overflow-y: auto;
  //   background-color: #fff;
  //   color: #74798c;

  //   // .scrollbar();
  // }
}

//标签tag颜色
.normal {
  background: #ecf5ff;
  color: #076ee4 !important;
}

.darkBlue {
  background: rgb(7 110 228 / 10%);
  color: #076ee4 !important;
}

.wathetBlue {
  background: rgb(74 184 255 / 10%);
  color: #4ab8ff !important;
}

.yellow {
  background: rgb(255 185 62 / 12%);
  color: #ffb93e !important;
}

.green {
  background: rgb(38 200 167 / 10%);
  color: #26c8a7 !important;
}

.purple {
  background: rgb(156 133 219 / 12%);
  color: #9c85db !important;
}

.brown {
  background: rgb(203 155 155 / 10%);
  color: #cb9b9b !important;
}

.onBusiness {
  background: rgb(1 144 225 / 10%);
  color: #0190e1 !important;
}

.comColor {
  background: rgb(0 173 211 / 10%);
  color: #00add3 !important;
}

.miniCom {
  background: rgb(248 129 21 / 10%);
  color: #fd9332 !important;
}

.logOff {
  background: rgb(92 96 112 / 10%);
  color: #7f8498 !important;
}
</style>
