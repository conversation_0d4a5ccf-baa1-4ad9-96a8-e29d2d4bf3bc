<template>
  <div class="tag">
    <span v-for="itm in Tags" :key="itm.tagName" :class="itm.tagColor">{{ itm.tagName }}</span>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
const Tags = ref([])
const props = defineProps({
  tags: {
    type: Array,
    default: () => [],
  },
})
const setTagColor = function (tagArr) {
  tagArr = tagArr
    .filter(i => i && i !== '-')
    .map(tag => {
      let tagObj = {
        tagName: tag,
        tagColor: '',
      }

      // 给不同tag添加不同颜色
      for (let color in COLOR_TAG_MAP) {
        if (COLOR_TAG_MAP[color].includes(tag)) {
          tagObj.tagColor = color
          break
        }

        tagObj.tagColor = 'normal'
      }
      return tagObj
    })
  return tagArr
}
const COLOR_TAG_MAP = reactive({
  darkBlue: ['世界500强', '中国500强'],
  wathetBlue: ['央企', '地方重点国企', '央企一级子公司', '央企二级子公司'],
  yellow: ['纳税A级企业', '纳税B级企业'],
  green: ['5A旅行社'],
  purple: [
    '国家高新技术企业',
    '中关村高新技术企业',
    '科技型中小企业',
    '国家高新技术企业(过期)',
    '技术先进型服务企业',
    '瞪羚企业',
    '牛羚企业',
    '独角兽企业',
    '众创空间',
    '科技企业孵化器',
    '企业技术中心',
    '科技小巨人',
    '专精特新企业',
    '雏鹰企业',
    '专精特新小巨人企业',
    '技术创新示范企业',
  ],
  brown: ['工信部制造业单项冠军'],
  onBusiness: ['在营', '临时', '待迁入'],
  comColor: ['企业', '组织机构', '个体', '全球企业'],
  miniCom: ['微型', '大型', '小型', '中型'],
  logOff: [
    '注销',
    '吊销',
    '迁出',
    '撤销',
    '清算中',
    '停业',
    '拟注销',
    '非正常户',
    '吊销未注销',
    '吊销已注销',
    '正在注销',
    '经营期限届满',
    '其他',
  ],
})
watch(
  () => props.tags,
  newVal => {
    Tags.value = setTagColor(newVal)
  },
  {
    immediate: true,
  }
)
</script>

<style lang="scss" scoped>
.tag {
  span {
    display: inline-flex;
    padding: 2px 12px;
    margin-right: 16px;
    margin-bottom: 12px;
    font-size: 24px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
  }
}
//标签tag颜色
.normal {
  color: #076ee4 !important;
  background: #ecf5ff;
}
.darkBlue {
  background: rgba(7, 110, 228, 0.1);
  color: #076ee4 !important;
}
.wathetBlue {
  background: rgba(74, 184, 255, 0.1);
  color: #4ab8ff !important;
}
.yellow {
  background: rgba(255, 185, 62, 0.12);
  color: #ffb93e !important;
}
.green {
  background: rgba(38, 200, 167, 0.1);
  color: #26c8a7 !important;
}
.purple {
  background: rgba(156, 133, 219, 0.12);
  color: #9c85db !important;
}
.brown {
  background: rgba(203, 155, 155, 0.1);
  color: #cb9b9b !important;
}
.onBusiness {
  background: rgba(1, 144, 225, 0.1);
  color: #0190e1 !important;
}
.comColor {
  background: rgba(0, 173, 211, 0.1);
  color: #00add3 !important;
}
.miniCom {
  background: rgba(248, 129, 21, 0.1);
  color: #fd9332 !important;
}
.logOff {
  background: rgba(92, 96, 112, 0.1);
  color: #7f8498 !important;
}
</style>
