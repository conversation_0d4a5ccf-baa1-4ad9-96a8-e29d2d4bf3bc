let dateTagList = [] // 日期tag列表

// 时间戳转日期
function transformDate(num) {
  const number = new Date(num)
  const year = number.getFullYear()
  const month = number.getMonth() + 1
  const day = number.getDate()
  const date = year + '-' + month + '-' + day
  return date
}

/**
 * 计算日期
 * @param {number} start - 开始时间
 * @param {number} end - 结束时间
 * @returns {string} - 日期组合
 */
export function calcDate(start, end) {
  const curDate = new Date().getTime() // 当前时间的时间戳
  const oneYear = 365 * 24 * 60 * 60 * 1000 // 一年时间的时间戳
  const startDate = curDate - end * oneYear // 开始时间
  const endDate = curDate - start * oneYear // 结束时间
  const dateCombination = (end ? transformDate(startDate) : '') + '$' + transformDate(endDate)
  dateTagList.push(dateCombination)
  return dateCombination
}

let searchLeftLists = [
  {
    title: '基础筛选',
    map: {
      ent_name: '企业名称',
      // ent_address: '企业地址',
      trade_types: '所属行业',
      areas: '所在地区',
    },
  },
  {
    title: '产业优选',
    map: {
      leading_ent: '龙头企业', //22.10.30新
      expand_status: '疑似扩张', //22.10.30新
      ent_scale: '企业规模',
      chain_codes: '所属产业链', //
      benefit_assess: '效益评估', //临时删除
      foreign: '外资企业',
      technology_types: '科技型企业',
    },
  },
  {
    title: '工商信息',
    map: {
      ent_type: '企业类型',
      shit_type: '实体类型', //22.10.30新
      ent_status: '企业状态',
      register_time: '成立年限',
      register_capital: '注册资本',
      super_dimension_social_num: '参保人数',
    },
  },
  {
    title: '经营/资产信息',
    map: {
      ent_cert: '企业许可',
      super_dimension_patent_category: '专利信息',
      tel_data: '联系电话',
      contactWays: '固定电话',
      phone_data: '手机号码',
      email_data: '联系邮箱',
      // "": "实缴资本",
      // "": "建筑资质",
      // "": "资质证书",
      // "": "进出口信用",
      // "land": "国有土地受让"
      // super_dimension_biding: '招投标',
      // super_dimension_job_info: '招聘',
      // tax_credit: '纳税信用',
    },
  },
  {
    title: '融资信息',
    map: {
      financing_info: '融资信息',
      listed_status: '上市状态', //22.10.30
    },
  },
  {
    title: '知识产权',
    map: {
      super_dimension_trademark: '商标信息',
      software_copyright_data: '软件著作权',
      work_copyright_data: '作品著作权',
      super_dimension_website: '官网信息',
      super_dimension_icp: 'ICP备案',
      super_dimension_android_app: '安卓APP',
      super_dimension_ios_app: '苹果APP',
      super_dimension_mini_app: '小程序',
      super_dimension_wx_extension: '微信公众号',
      super_dimension_weibo_extension: '微博',
      // super_dimension_patent: '专利信息',
    },
  },
  {
    title: '风险信息',
    map: {
      untrustworthy_info_data: '失信信息',
      // "": "破产重整",
      judgment_doc_data: '裁判文书',
      adminstrative_penalties_data: '行政处罚',
      // "": "清算信息",
      // "": "环保处罚",
      chattel_mortage_data: '动产抵押',
      abnormal_operation_data: '经营异常',
      // "": "股权冻结"
    },
  },
]
let searchTermList = [
  {
    title: '企业名称',
    type: 'ent_name', //到时候这个需要和下面那个合并成 phone_data
    special: 'input',
    content: '',
  },
  // {
  //   title: '企业地址',
  //   type: 'ent_address', //到时候这个需要和下面那个合并成 phone_data
  //   special: 'input',
  //   content: '',
  // },
  {
    title: '所在地区',
    type: 'areas',
    special: 'pop',
    content: '',
  },
  {
    title: '所属行业',
    type: 'trade_types',
    special: 'pop',
    content: '',
  },
  {
    title: '企业规模',
    type: 'ent_scale',
    icon: true,
    list: [
      { id: '0$500', name: '0-500:微型' },
      { id: '500$1000', name: '500-1000:小型A' },
      { id: '1000$2000', name: '1000-2000:小型B' },
      { id: '2000$5000', name: '2000-5000:中型A' },
      { id: '5000$10000', name: '5000-10000:中型B' },
      { id: '10000$20000', name: '10000-20000:大型A' },
      { id: '20000$30000', name: '20000-30000:大型B' },
      { id: '30000$', name: '大于30000:特大型' },
    ],
  },
  {
    title: '科技型企业',
    type: 'technology_types',
    isOpenIcon: true,
    isOpen: true,
    list: [
      { name: '高新技术企业', id: 'HN' },
      { name: '科技型中小企业', id: 'MST' },
      { name: '瞪羚企业', id: 'G' },
      { name: '专精特新企业', id: 'PSN' },
      { name: '独角兽企业', id: 'U' },
      { name: '科技小巨人企业', id: 'TG' },
    ],
  },
  {
    title: '外资企业',
    type: 'foreign',
    list: [
      { name: '外商投资企业', id: '5000' },
      { name: '港、澳、台投资企业', id: '6000' },
    ],
  },
  {
    title: '成立年限',
    type: 'register_time',
    min: '最低年限',
    max: '最高年限',
    unit: '年',
    genre: 'tpop', //区分类型
    list: [
      {
        id: calcDate(0, 1),
        name: '1年内',
      },
      {
        id: calcDate(1, 2),
        name: '1-2年',
      },
      {
        id: calcDate(2, 3),
        name: '2-3年',
      },
      {
        id: calcDate(3, 5),
        name: '3-5年',
      },
      {
        id: calcDate(5, 10),
        name: '5-10年',
      },
      {
        id: calcDate(10, 0),
        name: '10年以上',
      },
    ],
  },
  {
    title: '注册资本',
    type: 'register_capital',
    min: '最低资本',
    max: '最高资本',
    unit: '万元',
    genre: 'input', //区分类型
    list: [
      {
        id: '0$100',
        name: '100万以下',
      },
      {
        id: '100$200',
        name: '100-200万',
      },
      {
        id: '500$1000',
        name: '500-1000万',
      },
      {
        id: '1000$5000',
        name: '1000-5000万',
      },
      {
        id: '5000$',
        name: '5000万以上',
      },
    ],
  },
  {
    title: '企业状态', //企业经营状态
    type: 'ent_status',
    list: [
      {
        id: 'RUN',
        name: '在营',
      },
      {
        id: 'REVOKE',
        name: '吊销',
      },
      {
        id: 'LOGOUT',
        name: '注销',
      },
      {
        id: 'OTHER',
        name: '其他',
      },
    ],
  },
  {
    title: '实体类型',
    type: 'shit_type',
    list: [
      {
        id: 'ENTERPRISE',
        name: '企业(不包含个体户)',
      },
      {
        id: 'INDIVIDUAL',
        name: '个体户',
      },
      // {
      //   id: 'ORGANIZATION',
      //   name: '组织机构'
      // },
      // {
      //   id: 'GLOBAL_ENTERPRISE',
      //   name: '全球企业'
      // }
    ],
  },
  {
    title: '企业类型',
    type: 'ent_type',
    special: 'pop',
    content: '',
  },
  {
    title: '企业许可',
    type: 'ent_cert',
    special: 'pop',
    content: '',
  },
  {
    title: '联系电话',
    type: 'contactWays',
    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },
  {
    title: '固定电话',
    type: 'tel_data',
    list: [
      {
        id: 'TEL',
        name: '有',
      },
      {
        id: 'NO_TEL',
        name: '无',
      },
    ],
  },
  {
    title: '手机号码',
    type: 'phone_data',
    list: [
      {
        id: 'PHONE',
        name: '有',
      },
      {
        id: 'NO_PHONE',
        name: '无',
      },
    ],
  },
  {
    title: '联系邮箱',
    type: 'email_data',
    list: [
      {
        id: 'EMAIL',
        name: '有',
      },
      {
        id: 'NO_EMAIL',
        name: '无',
      },
    ],
  },
  {
    title: '参保人数',
    type: 'super_dimension_social_num',
    max: '最高人数',
    min: '最低人数',
    unit: '人',
    genre: 'input',
    list: [
      {
        id: '0$49',
        name: '0-49',
      },
      {
        id: '50$99',
        name: '50-99',
      },
      {
        id: '100$499',
        name: '100-499',
      },
      {
        id: '500$999',
        name: '500-999',
      },
      {
        id: '1000$4999',
        name: '1000-4999',
      },
      {
        id: '5000$',
        name: '5000人以上',
      },
    ],
  },
  {
    title: '融资信息',
    type: 'financing_info',
    isOpen: true,
    isOpenIcon: true,

    list: [
      { name: '天使轮', id: '天使轮' },
      { name: 'A轮', id: 'A轮' },
      { name: 'B轮', id: 'B轮' },
      { name: 'C轮', id: 'C轮' },
      { name: 'D轮', id: 'D轮' },
      { name: 'E轮及以上', id: 'E轮及以上' },
      { name: 'IPO', id: 'IPO' },
      { name: '定向增发', id: '定向增发' },
    ],
  },
  {
    title: '招投标',
    type: 'super_dimension_biding',

    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },
  {
    title: '招聘',
    type: 'super_dimension_job_info',

    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },
  {
    title: '纳税信用',
    type: 'tax_credit',

    list: [
      {
        id: 'ALEVEL',
        name: 'A级',
      },
      {
        id: 'NONALEVEL',
        name: '非A级',
      },
    ],
  },
  {
    title: '商标信息',
    type: 'super_dimension_trademark',
    isParent: true,

    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },
  {
    title: '专利信息',
    type: 'super_dimension_patent',
    isParent: true,

    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },
  {
    title: '专利信息',
    type: 'super_dimension_patent_category',
    list: [
      {
        id: '4',
        name: '发明授权',
      },
      {
        id: '1',
        name: '发明公告',
      },
      {
        id: '2',
        name: '实用新型',
      },
      {
        id: '3',
        name: '外观设计',
      },
    ],
  },
  {
    title: '安卓APP',
    type: 'super_dimension_android_app',
    isParent: true,
    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },
  {
    title: '苹果APP',
    type: 'super_dimension_ios_app',
    isParent: true,
    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },
  {
    title: '小程序',
    type: 'super_dimension_mini_app',
    isParent: true,
    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },
  {
    title: '微信公众号',
    type: 'super_dimension_wx_extension',
    isParent: true,
    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },
  {
    title: '微博',
    type: 'super_dimension_weibo_extension',
    isParent: true,
    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },
  {
    title: '失信信息',
    type: 'untrustworthy_info_data',

    list: [
      {
        id: 'UNTRUSTWORTHY_INFO',
        name: '有',
      },
      {
        id: 'NO_UNTRUSTWORTHY_INFO',
        name: '无',
      },
    ],
  },
  {
    title: '裁判文书',

    type: 'judgment_doc_data',
    list: [
      {
        id: 'JUDGMENT_DOC',
        name: '有',
      },
      {
        id: 'NO_JUDGMENT_DOC',
        name: '无',
      },
    ],
  },
  {
    title: '行政处罚',

    type: 'adminstrative_penalties_data',
    list: [
      {
        id: 'ADMINISTRATIVE_PENALTIES',
        name: '有',
      },
      {
        id: 'NO_ADMINISTRATIVE_PENALTIES',
        name: '无',
      },
    ],
  },
  {
    title: '动产抵押',

    type: 'chattel_mortage_data',
    list: [
      {
        id: 'CHATTEL_MORTAGE',
        name: '有',
      },
      {
        id: 'NO_CHATTEL_MORTAGE',
        name: '无',
      },
    ],
  },
  {
    title: '经营异常',

    type: 'abnormal_operation_data',
    list: [
      {
        id: 'ABNORMAL_OPERATION',
        name: '有',
      },
      {
        id: 'NO_ABNORMAL_OPERATION',
        name: '无',
      },
    ],
  },
  {
    title: '软件著作权',

    type: 'software_copyright_data',
    list: [
      {
        id: 'SOFTWARE_COPYRIGHT',
        name: '有',
      },
      {
        id: 'NO_SOFTWARE_COPYRIGHT',
        name: '无',
      },
    ],
  },
  {
    title: '作品著作权',

    type: 'work_copyright_data',
    list: [
      {
        id: 'WORK_COPYRIGHT',
        name: '有',
      },
      {
        id: 'NO_WORK_COPYRIGHT',
        name: '无',
      },
    ],
  },
  {
    title: '官网信息',
    type: 'super_dimension_website',
    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },
  {
    title: 'ICP备案',
    type: 'super_dimension_icp',
    list: [
      {
        id: 'true',
        name: '有',
      },
      {
        id: 'false',
        name: '无',
      },
    ],
  },

  {
    title: '龙头企业',
    type: 'leading_ent',
    list: [
      {
        id: 'true', // 以前这里是字符串，现在和pc保持一致
        name: '是龙头企业',
      },
      {
        id: 'false',
        name: '非龙头企业',
      },
    ],
  },
  {
    title: '疑似扩张',

    type: 'expand_status',
    list: [
      {
        id: 'A',
        name: '近期融资',
      },
      {
        id: 'B',
        name: ' 近期新增分支机构',
      },
      {
        id: 'C',
        name: '近期疑似扩招',
      },
      {
        id: 'D',
        name: '近期对外投资',
      },
      {
        id: 'E',
        name: '近期新增专利',
      },
      // {
      //   id: 'F',
      //   name: '近期新增专利申请',
      // },
    ],
  },
  {
    title: '上市状态',

    type: 'listed_status',
    list: [
      {
        id: 'LISTED_A',
        name: 'A股',
      },
      // {
      //   id: 'LISTED_B',
      //   name: 'B股',
      // },
      {
        id: 'LISTED_NEW_THIRD_BOARD',
        name: '新三板',
      },
      {
        id: 'LISTED_HK',
        name: '港股',
      },
      {
        id: 'LISTED_TH',
        name: '科创板',
      },
      // {
      //   id: 'LISTED_USA',
      //   name: '美股',
      // },
      // {
      //   id: 'LISTED_NONE',
      //   name: '非上市',
      // },
    ],
  },
  {
    title: '效益评估',
    type: 'benefit_assess',
    isOpenIcon: true,
    isOpen: true,
    list: [
      {
        id: '$50',
        name: '0-50:小型A',
      },
      {
        id: '50$100',
        name: '50-100:小型B',
      },
      {
        id: '100$200',
        name: '100-200:中型A',
      },
      {
        id: '200$300',
        name: '200-300:中型B',
      },
      {
        id: '300$500',
        name: '300-500:中型C',
      },
      {
        id: '500$1000',
        name: '500-1000:中型D',
      },
      {
        id: '1000$2000',
        name: '1000-2000:大型A',
      },
      {
        id: '2000$3000',
        name: '2000-3000:大型B',
      },
      {
        id: '3000$5000',
        name: '3000-5000:大型C',
      },
      {
        id: '5000$10000',
        name: '5000-10000:大型D',
      },
      {
        id: '10000$',
        name: '大于10000:特大型',
      },
    ],
  },
  // ps这里有点特俗
  {
    title: '所属产业链',
    type: 'chain_codes',
    special: 'pop',
    content: '',
  },
  // 新增的
  {
    title: '穿透方向',
    type: 'types',
    list: [
      { name: '上游', id: '1' },
      { name: '下游', id: '2' },
    ],
  },
  {
    title: '穿透层级',
    type: 'levels',
    list: [
      { name: '一级', id: '1' },
      { name: '二级', id: '2' },
    ],
  },
  {
    title: '查看范围', //这个要处理 -不是单纯的多选
    type: 'ckfw',
    list: [
      { name: '只看龙头企业', id: 'is_faucet' },
      { name: '只看上市企业', id: 'is_market' },
      { name: '只看本地企业', id: 'is_local' },
    ],
  },
  // todo 这是写死的情况
  // {
  //   title: '所属产业链',
  //   type: 'chain_codes',
  //   list: [
  //     { name: '智能装备产业', id: 'C1I1' },
  //     { name: '生物医药大健康产业', id: 'A1H1' },
  //     { name: '家具制造产业', id: 'F1A1' },
  //     { name: '建筑材料产业', id: 'F1R1' },
  //     { name: '金属加工产业', id: 'F1J1' },
  //     { name: '智能家居产业', id: 'F1Q1' },
  //     { name: '新能源与节能环保产业', id: 'D1F1' },
  //   ],
  // },
]

export { dateTagList, searchLeftLists, searchTermList }
