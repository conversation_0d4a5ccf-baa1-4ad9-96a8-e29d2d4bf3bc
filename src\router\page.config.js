export default [
  {
    name: 'Home',
    path: '/home',
    // redirect: '/home/<USER>',
    component: () => import('@/views/home/<USER>'),
    meta: {
      isFooter: true,
      KeepAlive: true,
      isNavBar: true,
    },
  },
  {
    path: '/high-search',
    name: 'HighSearch',
    component: () => import('@/views/advance_search/BeforeIndex.vue'),
    meta: {
      title: '企业猎搜',
      isNavBar: true,
    },
  },
  {
    path: '/advance-search',
    name: 'SearchAdvance',
    component: () => import('@/views/advance_search/index.vue'),
    meta: {
      title: '企业猎搜',
      isNavBar: true,
    },
    beforeEnter: (to, from, next) => {
      if (!['/search'].includes(from.path)) {
        localStorage.removeItem('backParams')
      }
      next()
    },
  },
  {
    path: '/supply-chain',
    name: 'SupplyChain',
    component: () => import('@/views/supply_chain/index.vue'),
    meta: {
      title: '供应链招商',
      isNavBar: true,
    },
  },
  {
    path: '/supply-search',
    name: 'SupplySearch',
    component: () => import('@/views/supply_chain/child/search.vue'),
    beforeEnter: (to, from, next) => {
      to.meta.title = to.query?.name ? String(to.query.name) : '供应链招商'
      next()
    },
    meta: {
      isNavBar: true,
    },
  },
  {
    path: '/supply-multiDetail',
    name: 'SupplyMultiDetail',
    component: () => import('@/views/supply_chain/child/multiDetailPage.vue'),
    meta: {
      title: '供应链详情',
      isNavBar: true,
    },
  },
  {
    path: '/supply-atlas',
    name: 'SupplyAtlas',
    component: () => import('@/views/supply_chain/child/Atlas.vue'),
    meta: {
      title: '供应链图谱',
      isNavBar: true,
    },
  },
  {
    path: '/single-detail',
    name: 'SingeleDetailPage',
    component: () => import('@/views/supply_chain/child/singleDetailPage.vue'),
    meta: {
      isNavBar: true,
    },
    beforeEnter: (to, from, next) => {
      to.meta.title = String(to.query?.title)
      next()
    },
  },
  {
    path: '/scene-editing',
    name: 'SceneEditing',
    component: () => import('@/views/home/<USER>'),
    meta: {},
    beforeEnter: (to, from, next) => {
      to.meta.title = (Number(to.query.type) === 1 ? '强链' : '补链') + '场景编辑'
      next()
    },
  },
  {
    path: '/ent-management',
    name: 'EntManage',
    component: () => import('@/views/home/<USER>/index.vue'),
    meta: {
      title: '重点存量企业管理',
    },
  },
  {
    path: '/add-ent',
    name: 'AddEnt',
    component: () => import('@/views/home/<USER>/addEnt.vue'),
    meta: {
      title: '批量导入重点企业',
    },
  },
  {
    path: '/import-ent',
    name: 'ImportEnt',
    component: () => import('@/views/home/<USER>/importEnt.vue'),
    meta: {
      title: '批量导入重点企业',
    },
  },
  {
    path: '/search',
    name: 'Search',
    component: () => import('@/views/advance_search/searchList.vue'),
    meta: {
      title: '查询结果',
      isNavBar: true,
    },
  },
  {
    path: '/authority',
    name: 'AuthorityList',
    component: () => import('@/views/authority_list/index.vue'),
    meta: {
      title: '权威榜单',
      isNavBar: true,
    },
  },
  {
    path: '/authority-ent-list',
    name: 'AuthorityEntList',
    component: () => import('@/views/authority_list/authorityEntList.vue'),
    meta: {
      title: '权威榜单',
      isNavBar: true,
    },
  },
  {
    path: '/chains',
    name: 'Chains',
    component: () => import('@/views/chains/index.vue'),
    meta: {
      title: '强链补链',
      isNavBar: true,
    },
  },
  {
    path: '/chainDetail',
    name: 'ChainDetail',
    component: () => import('@/views/chains/chainDetail.vue'),
    meta: {
      isNavBar: true,
    },
  },
  {
    path: '/chain-list',
    name: 'ChainList',
    component: () => import('@/views/chains/list.vue'),
    meta: {
      isNavBar: true,
    },
  },
  {
    path: '/excavate',
    name: 'Excavate',
    component: () => import('@/views/excavate/index.vue'),
    meta: {
      title: '智慧招商',
      // KeepAlive: true,
      isNavBar: true,
    },
  },
  {
    path: '/excavate/list',
    name: 'ExcavateList',
    component: () => import('@/views/excavate/child/list.vue'),
    beforeEnter: (to, from, next) => {
      to.meta.title = to.query?.name || '产业地图'
      next()
    },
    meta: {
      isNavBar: true,
    },
  },
  {
    path: '/target-list',
    name: 'TargetList',
    component: () => import('@/views/target_list/index.vue'),
    meta: {
      title: '储备项目',
      isNavBar: true,
    },
  },
  {
    path: '/target-detail',
    name: 'TargetDetail',
    component: () => import('@/views/target_list/detail/index.vue'),
    meta: {
      title: '企业详情',
      isNavBar: true,
      // keepAlive: true,
    },
  },
  {
    path: '/supply-chain/fullscreen',
    name: 'SupplyChainFullScreen',
    component: () => import('@/views/target_list/detail/gragh/index.vue'),
    meta: {
      title: '供应链关系',
    },
  },
  {
    path: '/target-detail/patent',
    name: 'TargetDetailPatent',
    component: () => import('@/views/target_list/detail/patentList.vue'),
    meta: {
      title: '专利列表',
    },
  },
  {
    path: '/target-detail/investment',
    name: 'TargetDetailInvestment',
    component: () => import('@/views/target_list/detail/investmentList.vue'),
    meta: {
      title: '对外投资列表',
    },
  },
  // 产业链招商
  {
    path: '/industrial_chain',
    name: 'IndustrialChain',
    component: () => import('@/views/industrial_chain/index.vue'),
    meta: {
      title: '产业链招商',
      isNavBar: true,
    },
  },
  {
    path: '/industrial_chain_node',
    name: 'IndustrialChainNode',
    component: () => import('@/views/industrial_chain/chain_node/index.vue'),
    meta: {
      title: '产业链图谱',
      isNavBar: true,
    },
  },
  {
    path: '/industrial_chain_map',
    name: 'IndustrialChainMap',
    component: () => import('@/views/industrial_chain/chain_map/index.vue'),
    meta: {
      title: '产业链图谱',
      isNavBar: true,
    },
  },
]
