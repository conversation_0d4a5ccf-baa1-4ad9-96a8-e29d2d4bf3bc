<template>
  <!-- 宽高交给父亲 -->
  <div class="box">
    <VanImage :src="imgSrc" v-if="isImg && imgSrc" fit="fill" style="width: 100%; height: 100%">
      <template #loading>
        <VanLoading type="spinner" size="20" />
      </template>
      <template #error>
        <slot name="err">
          <img src="@/assets/image/common.png" style="width: 100%; height: 100%" />
        </slot>
      </template>
    </VanImage>
    <VanImage src="@/assets/image/company.png" v-if="!title && !imgSrc" fit="fill" style="width: 100%; height: 100%">
      <template #loading>
        <VanLoading type="spinner" size="20" />
      </template>
    </VanImage>
    <!-- 文字的情況 -->
    <div class="text" v-if="!isImg" :style="{ background: bgColor, fontSize: fontSize }">
      {{ Text }}
    </div>
  </div>
</template>
<script>
export default {
  name: 'HImage',
}
</script>

<script setup>
import { watch, ref } from 'vue'
import { setPx } from '@/utils/height'
const colors = ['#4AB8FF', '#3483F1', '#3FD6B7']

const props = defineProps({
  src: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
  size: {
    type: String,
    default: '24',
  },
})
const isImg = ref(true)
const Text = ref('')
const imgSrc = ref(props.src)
const bgColor = ref('#fff')
const fontSize = ref(0)
watch(
  () => props.src,
  val => {
    if (val) {
      imgSrc.value = val
      return
    }
    bgColor.value = colors[Math.floor(Math.random() * 3)]
    fontSize.value = setPx(+props.size) + 'px'
    // 说明没有值
    if (!props.title) {
      //说明名字也没有
      return
    }
    // 说明有名字
    isImg.value = false
    Text.value = props.title.substring(0, 1)
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  height: 100%;

  img {
    width: 100%;
    height: 100%;
    // box-shadow: 0px 0px 4px 1px rgba(32, 38, 58, 0.1);
  }
  .text {
    width: 100%;
    height: 100%;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    // font-size: 30px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 600;
    // border-radius: 4px;
    box-shadow: 0px 0px 4px 1px rgba(32, 38, 58, 0.1);
  }
}
</style>
