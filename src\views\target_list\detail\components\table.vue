<template>
  <div v-if="data.length" class="relative overflow-x-auto w-704">
    <div class="flex w-fit h-74" v-for="item in columns" :class="item.class">
      <div
        :class="`
            !w-208 bg-grey border z-10 border-[#eee] p-20 text-24 h-74 
            whitespace-nowrap text-left break-keep text-tertiary sticky left-0 shadow-sm
          `"
      >
        {{ item.title }}
      </div>
      <div class="flex">
        <div
          class="border !w-156 border-[#eee] p-20 text-24 text-tertiary"
          :class="item.cellClass"
          v-for="report in data"
        >
          {{ report[item.prop] }}
        </div>
      </div>
    </div>
  </div>
  <div v-if="!data.length" class="flex flex-col items-center justify-center w-full h-full py-60">
    <SvgIcon name="empty-data" class="w-[230px] h-[230px] mb-24" />
    <p>暂无数据</p>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
import { ref, defineProps } from 'vue'

const route = useRoute()

const props = defineProps({
  columns: {
    type: Array,
    default: () => [],
  },
  data: {
    type: Array,
    default: () => [],
  },
})
</script>
