import { showToast } from 'vant'
import { removeToken } from '@/utils/auth'
import { HmacSHA256, enc } from 'crypto-js'
import { getTokenHandler, getUrlKey } from '@/utils'
import { router } from '@/main'

const { VITE_APP_HOST, VITE_APP_ENV } = import.meta.env
const initAuthorization = function (config, tokenObj) {
  let { url, method, headers } = config
  const { mac_key, access_token } = tokenObj
  // headers['Content-Type'] = method === 'get' ? 'application/x-www-form-urlencoded' : 'application/json;charset=UTF-8'
  if (method === 'get') {
    headers['Content-Type'] = 'application/x-www-form-urlencoded'
  } else {
    headers['Content-Type'] = config.headers['Content-Type'] || 'application/json;charset=utf-8';
  }
  config.url = url;
  let chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678',
    host = '',
    randomStr = '',
    nonce = '',
    mac = '',
    hash = '',
    macAssign = ''
  for (let i = 0; i < 8; ++i) {
    randomStr += chars.charAt(Math.floor(Math.random() * 8))
  }
  host = ['development', 'test'].includes(VITE_APP_ENV) ? VITE_APP_HOST : window.location.host
  nonce = `${new Date().getTime()}:${randomStr}`
  let ur = url.replace('/hdzhzs', '');
  mac = `${nonce}\n${method.toUpperCase()}\n${ur}\n${host}\n`
  hash = HmacSHA256(mac, mac_key)
  macAssign = enc.Base64.stringify(hash)
  headers['Authorization'] = `MAC id=${access_token}, nonce=${nonce}, mac="${macAssign}"`
  return config
}

export const handleConfigureAuth = config => {
  let { url, method, headers } = config
  if (method === 'get') {
    headers['Content-Type'] = 'application/x-www-form-urlencoded'
  } else {
    headers['Content-Type'] = config.headers['Content-Type'] || 'application/json;charset=utf-8';
  }
  headers['HD-App-Code'] = 'hd.zs3-business.v1.0'
  if (url.includes('/v0.1/sign/login')) {
    return config
  } else {
    const tokenData = getTokenHandler()
    const { access_token, mac_key } = tokenData
    if (access_token && mac_key) {
      return initAuthorization(config, { access_token, mac_key })
    } else {
      router.push('/login')
    }
  }
}

export const handleNetworkError = errStatus => {
  const networkErrMap = {
    400: '错误的请求', // token 失效
    401: '未授权，请重新登录',
    403: '拒绝访问',
    404: '请求错误，未找到该资源',
    405: '请求方法未允许',
    408: '请求超时',
    500: '服务器端出错',
    501: '网络未实现',
    502: '网络错误',
    503: '服务不可用',
    504: '网络超时',
    505: 'http版本不支持该请求',
  }
  if (errStatus) {
    showToast(networkErrMap[errStatus] ?? `其他连接错误 --${errStatus}`)
    return
  }
  showToast('无法连接到服务器！')
}

export const handleGeneralError = data => {
  let { code, message } = data
  let errorType = 1
  if (code !== '0000') {
    if (message === '请前往设置预设!') {
      message = '权限不足!'
    }
    const tokenInvalid = ['UC/AUTH_TOKEN_EXPIRED', 'UC/TOKEN_NOT_FOUND'] // token失效
    const wx = ['UC/WX_NOT_FOUND_ERROR', 'UC/WX_AUTHORIZATION_ERROR']
    const locked = ['UC/USER_LOCKED']
    const macSignError = ['UC/MAC_SIGN_INVALID']
    if (tokenInvalid.includes(code) || macSignError.includes(code)) {
      const debug = getUrlKey('debug')
      if (debug) {
        removeToken()
      }
      errorType = 2
    } else {
      errorType = 3
      if (code === 'UC/MEMBER_PASSWORD_ERROR') {
        // todo: error page
      } else if (tokenInvalid.includes(code)) {
        // todo: login page
        // return;
      } else if (wx.includes(code)) {
        // todo: check
      } else if (locked.includes(code)) {
        // todo: check
      }
      showToast(message || '服务器异常！')
    }
  }
  return errorType
}
