import { Post, Get, Delete } from '@/request'
import { CHAIN_URL } from '../constants'

export const getRecommended = (params = {}) => {
  return Post(`${CHAIN_URL}/recommended/ent/list`, params)
}

export const getGonYinList = ent_name => {
  return Get(`${CHAIN_URL}/industry/ent/name?ent_name=${encodeURI(ent_name)}`)
}

// 单-地图
export const staticMap = (params = {}) => {
  return Post(`${CHAIN_URL}/stocking/supply/chains/static/map`, params)
}
// 单-饼图
export const staticChains = (params = {}) => {
  return Post(`${CHAIN_URL}/stocking/supply/chains/static`, params)
}
// 单-供应链详情列表
export const getLeadingEntList = (params = {}) => {
  return Post(`${CHAIN_URL}/stocking/supply/chains/detail`, params)
}

export const getMultiLeadingEntList = (params = {}) => {
  return Post(`${CHAIN_URL}/industry/chain/supplyChains/detail`, params)
}

// 图谱 企业
export const getsupplyList = (params = {}) => {
  return Post(`${CHAIN_URL}/stocking/supply/chains/detail`, params)
}

// industrialChainPreset
export const industrialChainPreset = (params = {}) => {
  return Get(`${CHAIN_URL}/preset`, params)
}
export default { getsupplyList, getMultiLeadingEntList }
