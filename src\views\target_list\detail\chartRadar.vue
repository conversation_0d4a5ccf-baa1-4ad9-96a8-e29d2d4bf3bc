<template>
  <section class="h-[100%] w-702" :id="props.id"></section>
</template>

<script setup>
const dimensions = [
  { label: '企业关联程度', cont: 0.15, name: '企业与目标地区关联程度' },
  { label: '企业历史投资', cont: 0.15 },
  { label: '区域行业扩张能力', cont: 0.1 },
  { label: '企业规模', cont: 0.06 },
  { label: '企业效益情况', cont: 0.09 },
  { label: '企业成长能力', cont: 0.18 },
  { label: '企业扩张能力', cont: 0.21 },
  { label: '企业抗风险能力', cont: 0.06 },
]

import { ref, onMounted, onUpdated, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  id: { type: String, default: () => 'chart' },
  data: { type: Array, default: () => [] },
  title: { type: String, default: () => '' },
})

const myChart = ref(null)

watch(
  () => props.data,
  newData => {
    if (myChart.value) {
      myChart.value.clear()
    }
    drawChart()
  },
  { deep: true }
)

onMounted(() => {
  drawChart()
})

function getOptions() {
  const MAX = 100
  const indicator = dimensions.map(item => ({ name: item.name ?? item.label, max: MAX }))

  const options = {
    backgroundColor: 'transparent', // 背景色
    title: {
      text: props.title,
      bottom: 'center',
      left: 'center',
      textStyle: {
        color: 'rgba(7, 110, 228, 1)',
        fontSize: 32,
      },
    },
    // tooltip: {
    //     show: true,
    //     trigger: 'item',
    //     confine: true,
    //     textStyle: {
    //         color: "#fff",
    //         fontSize: 13,
    //     },
    // },
    legend: {
      show: false,
      data: ['企业能力雷达'], // 载入图例数据
      selectedMode: true,
      itemWidth: 15,
      itemHeight: 9,
      textStyle: {
        color: 'rgba(111,82,38, 1)',
      },
      x: 'center',
      y: 'bottom',
    },
    radar: {
      shape: 'circle', // polygon | circle
      center: ['50%', '52%'],
      radius: '50%', // 外半径为可视区尺寸
      axisName: {
        textStyle: {
          color: 'rgba(111,82,38, 1)',
        },
      },
      // axisLabel: {
      //     show: true,
      //     color: 'rgba(143,142,140,.618)',
      //     fontSize: 12,
      // },
      indicator: indicator,
    },
    series: [
      {
        type: 'radar',
        label: {
          show: false,
          position: 'top',
          color: 'rgba(111,82,38,.8)',
          fontSize: 13,
          fontWeight: 'bold',
        },
        lineStyle: {
          width: 1,
        },
        symbol: 'circle',
        symbolSize: 7,
        itemStyle: {
          color: 'rgba(7, 110, 228, 1)',
        },
        areaStyle: {
          opacity: 0.2,
        },
        emphasis: {
          label: {
            show: true,
            color: 'rgba(111,82,38, 1)',
            fontSize: 13,
            formatter: '{c}', // 鼠标悬浮时展示数据
          },
        },
        data: [
          {
            name: '企业能力雷达',
            value: changeDataOrder(props.data),
          },
        ],
      },
    ],
  }
  return options
}

function drawChart() {
  if (myChart.value) {
    myChart.value.dispose()
    myChart.value = null
  }
  myChart.value = echarts.init(document.getElementById(props.id))
  const option = getOptions()
  myChart.value.setOption(option, true)

  window.onresize = () => {
    drawChart()
  }
}

function changeDataOrder(list) {
  if (!Array.isArray(list)) return list
  return list.reduce((result, items, index, arr) => {
    if (!index) return [...result, items]
    result[arr.length - index] = items
    return result
  }, [])
}
</script>
