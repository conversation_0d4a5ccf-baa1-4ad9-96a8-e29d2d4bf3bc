<template>
  <div class="flex flex-col w-full min-h-full bg-grey">
    <van-sticky :offset-top="headerHeight + 'px'">
      <div class="bg-white">
        <van-tabs swipeable @click-tab="handleClickTab">
          <van-tab
            v-for="(item, index) in chainData"
            :title="item.name"
            :code="index"
            :key="item.id"
            class="flex items-center mr-40"
          >
            <span class="text-secondary text-28" :class="[active === index ? 'text-selected' : '']"> </span>
          </van-tab>
        </van-tabs>
      </div>
    </van-sticky>

    <div class="pb-168">
      <div class="flex justify-center p-24 mt-24 bg-white border-b border-[#eee]">
        <div v-for="(item, index) in types" class="flex items-center" :class="`${index === 0 ? 'mr-48' : ''}`">
          <span :class="`w-20 h-20 mr-8 block ${index === 0 ? 'bg-[#26C8A7]' : 'bg-[#FD9331]'}`"></span>
          {{ item.name }}
        </div>
      </div>
      <template v-if="chainData?.[active]?.children?.length">
        <div
          v-for="item in chainData?.[active]?.children || []"
          :key="item.id"
          class="px-24 py-32 pb-16 bg-white border-b border-solid border-[#eee]"
        >
          <div class="flex mb-24">
            <span class="font-semibold text-primary text-28" @click="handleClickNode(item)">{{ item.name }}</span>
          </div>

          <div class="flex flex-wrap">
            <div
              v-for="(node, i) in [...item.children]"
              :key="node.id"
              class="px-16 py-12 text-26 mb-16 text-tertiary min-h-60 bg-grey rounded-[8px] w-[48%] max-w-[49%] flex justify-center items-center flex-grow relative node-item"
              :class="`${i % 2 === 0 ? 'mr-16' : ''} ${
                strongData.includes(node.chain_code) ? 'after:bg-[#26C8A7]' : ''
              } ${supplementaryData.includes(node.chain_code) ? 'after:bg-[#FD9331]' : ''}
              `"
              @click="handleClickNode(node)"
            >
              <span class="">{{ node.name }}</span>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="noData">
          <img src="@/assets/image/null.png" alt="" />
          <div>暂无数据</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
import {
  getStrongChainNodes,
  getSupplementaryChainNodes,
  getChainCodePlus,
  setStrongChain,
  setSupplementaryChain,
} from '@/api/home/<USER>'
import { useRouter, useRoute } from 'vue-router'
import { onMounted, getCurrentInstance, watch } from 'vue'
import { pageChangeHandler } from '@/utils'
import { useAppHeaderHeight } from '@/hooks/useAppHeaderHeight'

const router = useRouter()
const route = useRoute()
const { proxy } = getCurrentInstance()
const headerHeight = useAppHeaderHeight()

const active = ref(0)
const chainData = ref([])
const selectedKeys = ref([])
const strongData = ref([])
const supplementaryData = ref([])

const types = [
  { name: '强链环节', color: '#26C8A7' },
  { name: '补链环节', color: '#FD9331' },
]

onMounted(() => {
  const code = route.query.code
  proxy.$loading('加载中')
  getChainCodePlus(code)
    .then(res => {
      if (res?.length === 1 && res?.[0]?.name === '智能网联新能源整车') {
        chainData.value = res[0]?.children || []
      } else {
        chainData.value = res
      }
    })
    .finally(() => {
      proxy.$close()
    })

  getStrongChainNodes(code).then(res => {
    strongData.value = res
  })

  getSupplementaryChainNodes(code).then(res => {
    supplementaryData.value = res
  })
})

const handleClickTab = ({ name }) => {
  active.value = name
}

const handleClickNode = node => {
  pageChangeHandler('/chain-list', { code: node.chain_code, name: node.name })
}
// const getSelectedNodes = () => {
//   ;(Number(route.query.type) === 1 ? getStrongChainNodes : getSupplementaryChainNodes)(
//     currentIndustry.value.value
//   ).then(res => {
//     selectedKeys.value = res
//   })
// }
</script>

<style lang="scss" scoped>
:deep(.van-checkbox__icon) {
  font-size: 32px;
}

.highlight {
  background-color: #f5f7fe !important;
  color: #1e75db !important;
}

.node-item {
  &::after {
    content: '';
    display: block;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 4px;
    // background: #fd9331;
    border-radius: 4px 4px 4px 4px;
  }
}

:deep(.van-tab--active) {
  .van-tab__text {
    color: #2a72ff;
  }
}

:deep(.van-tabs__line) {
  background: linear-gradient(90deg, #2a72ff 0%, #3994ff 71%);
  width: 40px;
  height: 6px;
}
</style>
