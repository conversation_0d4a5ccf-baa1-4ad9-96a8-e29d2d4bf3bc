<template>
  <div class="w-full h-full bg-grey">
    <div class="p-24 bg-white">
      <div class="flex items-center justify-between">
        <span class="font-semibold text-headline text-32">请选择上传方式</span>
        <span class="flex items-center ml-8 text-28 text-selected" @click="handleDownload">
          <svg-icon name="download" class="w-36 h-36" />
          下载模板
        </span>
      </div>
      <div class="text-24 mt-12 text-[#fd9331] bg-white">上传文件需按照模板要求，点击下载模板</div>
    </div>

    <div class="p-24">
      <div
        v-for="(item, index) in methods"
        :key="item.title"
        class="relative flex items-center px-24 mb-24 bg-white py-28 rounded-12"
        @click="item.onClick"
      >
        <template v-if="index === 0">
          <van-uploader accept=".xlsx, .xls" max-count="1" class="w-full" :after-read="afterReadExcel">
            <div class="relative flex items-center">
              <svg-icon :name="item.icon" class="w-64 h-64 mr-16" />
              <span class="text-30 text-primary">{{ item.title }}</span>
              <svg-icon name="arrow" class="absolute right-0 w-24 h-24" />
            </div>
          </van-uploader>
        </template>

        <template v-else>
          <svg-icon :name="item.icon" class="w-64 h-64 mr-16" />
          <span class="text-30 text-primary">{{ item.title }}</span>
          <svg-icon name="arrow" class="absolute w-24 h-24 right-24" />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { Uploader } from 'vant'
import { uploadPointEnt } from '@/api/home'
import { backHandler } from '@/utils'
const router = useRouter()
const { proxy } = getCurrentInstance()

const methods = [
  {
    title: '手机文件上传',
    icon: 'mobile-phone',
  },
  // { title: '微信上传', icon: 'wechat' },
  // { title: '从其它应用分享上传', icon: 'appstore' },
]
const handleDownload = () => {
  const a = document.createElement('a')
  a.href = `https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/oem/%E5%AD%98%E9%87%8F%E6%8C%96%E6%8E%98Template.xlsx`
  a.download = '企业导入模板.xlsx'
  a.click()
}

const afterReadExcel = file => {
  if (file.file.size > 5 * 1024 * 1024) {
    return proxy.$showToast('文件大小不能超过5M')
  }
  const suffix = /\.xls(x)$/i
  if (!suffix.test(file.file.name)) {
    return proxy.$showToast('文件格式不匹配, 请传入excel文件')
  }

  proxy.$loading('上传中...')
  const formData = new FormData()
  formData.append('file', file.file)
  uploadPointEnt(formData)
    .then(res => {
      if (res.error_msg) {
        proxy.$showToast({
          message: res.error_msg,
        })
      } else {
        proxy.$showToast('导入成功')
        backHandler()
      }
    })
    .catch(() => {
      proxy.$close()
    })
}
</script>

<style lang="scss" scoped>
:deep(.van-uploader__input-wrapper) {
  width: 100%;
}
</style>
