<template>
  <div>
    <div class="ent-card">
      <div>
        <div class="ent-name-tag">
          <HImage
            class="imgs flex justify-center rounded-[8px] overflow-hidden"
            :src="
              ent_info.logo
                ? ent_info.logo
                : 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png'
            "
          />
          <div class="ent-name">{{ ent_info.ent_name || '-' }}</div>
        </div>
        <div class="card_c">
          <div class="card_c_i">
            <span class="name">法人代表人</span>
            <span class="cont">{{ ent_info.legal_person || ent_info.fa_ren }}</span>
          </div>
          <div class="card_c_i card_c_is">
            <span class="name">注册资本</span>
            <span class="cont">{{ ent_info.register_capital || ent_info.reg_cap || '--' }}万</span>
          </div>
          <div class="card_c_i">
            <span class="name">成立日期</span>
            <span class="cont">{{ ent_info.register_date || ent_info.es_date || '--' }}</span>
          </div>
        </div>
        <div class="operations">
          <button v-if="ent_info.official_website || ent_info.website" @click="handleClickOfficialWebsite(ent_info)">
            <SvgIcon name="official-website" />
            官网
          </button>
          <button v-if="ent_info.register_address" @click="handleClickLocation(ent_info.register_address)">
            <SvgIcon name="location" />
            地址
          </button>
          <button @click="toReport(ent_info)">
            <SvgIcon name="breport" />
            企业报告
          </button>
        </div>
      </div>
    </div>
    <!-- 地图 -->
    <VanDialog v-model:show="locationDialogShow" :showTitle="false" :showConfirmButton="false" class="location-dialog">
      <div class="location-dialog-body" @touchmove.prevent.stop>
        <p class="title">地址</p>
        <div class="map">
          <Map :default-address="entAddress"></Map>
        </div>

        <span class="address">{{ entAddress }}</span>
        <div class="btn" @click="copyAddress(entAddress)">复制地址</div>
        <!-- <div class="btn navigation" @click="navigation">导航</div> -->
        <div class="btn" @click="locationDialogShow = false">取消</div>
      </div>
    </VanDialog>
  </div>
</template>

<script setup>
import useClipboard from 'vue-clipboard3'
import { openReport } from '@/utils/index'

const props = defineProps({
  source: {
    type: Object,
    default: () => {},
  },
})
const locationDialogShow = ref(false)
const entAddress = ref('')
const { proxy } = getCurrentInstance()
const { toClipboard } = useClipboard()

const ent_info = computed(() => {
  return props.source
})
// 发送请求浏览历史 --- 到报告系统
const toReport = async item => {
  openReport(item)
}

const handleClickLocation = address => {
  entAddress.value = address
  locationDialogShow.value = true
}

const copyAddress = address => {
  toClipboard(address)
    .then(res => {
      locationDialogShow.value = false
      proxy.$showToast('复制成功')
    })
    .catch(err => {
      console.log(err)
    })
}
const handleClickOfficialWebsite = item => {
  const startWith = /^(http|https):\/\/\S*/i
  // const websitePrefix = /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~\/])+$/
  if (item.official_website || item.website) {
    let url = item.official_website || item.website
    if (!startWith.test(url)) url = 'http://' + url
    window.open(url, '_self')
  }
}
</script>

<style lang="scss" scoped>
.ent-card {
  padding: 32px 24px 0 32px;
  background: #fff;
  margin-bottom: 20px;
  .ent-name-tag {
    display: flex;
    align-items: center;
    .ent-name {
      width: calc(100vw - 160px);
      color: #20263a;
      p {
        font-size: 32px;
        font-weight: 600;
      }
    }
    .imgs {
      width: 60px;
      height: 60px;
      margin-right: 16px;
      flex-shrink: 0;
    }
  }
  .card_c {
    display: flex;
    justify-content: space-around;
    padding: 24px 0 24px;
    .card_c_i {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      .name {
        font-size: 24px;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: center;
        color: #74798c;
        padding-bottom: 12px;
      }
      .cont {
        font-size: 28px;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: center;
        color: #20263a;
      }
      .contblue {
        color: #1e75db;
      }
    }
    .card_c_is::after {
      position: absolute;
      content: '';
      left: 0;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      border-right: 1px solid rgba(238, 238, 238, 0.8);
      width: 0;
      height: 48px;
    }
    .card_c_is::before {
      position: absolute;
      content: '';
      right: 0;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      border-right: 2px solid #eee;
      width: 0;
      height: 48px;
    }
  }
}
.operations {
  display: flex;
  justify-content: flex-end;
  position: relative;
  height: 92px;
  align-items: center;

  &::after {
    content: ' ';
    width: calc(100% + 48px);
    height: 1px;
    background: #eee;
    /* background: red; */
    position: absolute;
    top: 0;
    left: -24px;
    transform: scaleY(0.5);
  }

  button {
    border-radius: 100px;
    padding: 8px 12px;
    margin-right: 16px;
    font-family: 'PingFang SC';
    font-size: 24px;
    color: #20263a;
    display: flex;
    border: 1px solid #dedede;
    border-radius: 8px;

    &:last-of-type {
      margin-right: 0;
    }

    svg {
      width: 32px;
      height: 32px;
      margin-right: 8px;
    }
  }
}
:deep(.location-dialog) {
  border-radius: 8px !important;
  width: 600px !important;

  .location-dialog-body {
    background: white !important;
    p {
      text-align: center;
      margin-bottom: 24px;
      color: #20263a;
      font-weight: 500;
      font-size: 34px;
    }

    .address {
      color: #74798c;
      display: block;
      font-size: 32px;
      padding: 0 48px;
      line-height: 44px;
      text-align: center;
      margin: 32px 0;
    }
  }
}
.btn {
  width: 100%;
  text-align: center;
  padding: 32px 0;
  font-size: 28px;
  color: #076ee4;
  &.navigation {
    color: #3e7bfa;
    font-weight: 500;
  }
}
</style>
