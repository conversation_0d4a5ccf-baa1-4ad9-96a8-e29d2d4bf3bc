import { BUSINESS_URL, CHAIN_URL } from '../constants'
import { Post, Get, Delete } from '@/request/index'

// 企业地址列表

export function getMapSearchList(data) {
  return Post(`${BUSINESS_URL}/maps/investment/maps/list/new`, data)
}

// 获取收藏分组列表接口

export function getAllReserveItemsClassificationDat(data) {
  return Get(`${CHAIN_URL}/ReserveItemsClassification/getAllReserveItemsClassificationData`, data)
}

// 收藏接口

export function batch(data) {
  return Post(`${CHAIN_URL}/collect/common/collectEnterprise/batch`, data)
}

// 取消收藏接口

export function removeBatch(data) {
  return Post(`${CHAIN_URL}/collect/common/removeEnterprise/batch`, data)
}

//  获取企业
export function investmentList(data) {
  return Post(`${BUSINESS_URL}/maps`, data)
}
// 获取园区
export function parkList(data) {
  return Post(`${BUSINESS_URL}/maps/park`, data)
}
