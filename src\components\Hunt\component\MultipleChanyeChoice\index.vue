<template>
  <VanBPop @submit="submit" @close="close" :visible="data.visible">
    <div class="multilevel-checkbox-container">
      <div class="content pt-32">
        <div
          v-for="parentItem in chainList"
          :key="parentItem.chain_code"
          class="parent-group"
        >
          <!-- 父级标题 -->
          <div class="parent-title">
            {{ parentItem.name }}
          </div>

          <!-- 子级选项 -->
          <div class="children-container">
            <div class="children-wrap">
<!--            <div class="flex flex-wrap justify-around">-->
              <div
                v-for="childItem in parentItem.children"
                :key="childItem.chain_code"
                class="child-item"
              >
                <div
                  class="checkbox-wrapper"
                  @click="handleChildChange(childItem.chain_code, !isChildChecked(childItem.chain_code))"
                >
                  <div :class="{ checked: isChildChecked(childItem.chain_code) }">
                    {{ childItem.name }}
                  </div>
                  <div
                    :class="['checkbox', isChildChecked(childItem.chain_code) ? 'checked' : '']"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </VanBPop>
</template>

<script setup name="MultipleChanye">
import VanBPop from '../../../Van/VanbPop/index.vue'
import { reactive, watch, nextTick } from 'vue'
import { chainNewAll } from "@/api/advanceSearch";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  mark: {
    type: String,
    default: '',
  },
  modelValue: {
    type: Array,
    default: () => [],
  },
  oldData: {
    type: Array,
    default: () => [],
  },
})

const emits = defineEmits(['close', 'submit', 'update:modelValue'])

const data = reactive({
  visible: false,
  checkedChainList: [],
})

let chainList = reactive([])

onMounted(async ()=>{
  chainList = await chainNewAll()
})

// 上一次选中
watch(
  () => [props.visible, props.modelValue, props.oldData],
  ([bool, modelValue, oldData]) => {
    data.visible = bool
    if (bool) {
      const initialValue = modelValue && modelValue.length > 0 ? modelValue : oldData
      nextTick(() => {
        data.checkedChainList = [...(initialValue || [])]
      })
    }
  },
  {
    immediate: true,
  }
)

// 处理复选框变化
const handleChildChange = (childValue, checked) => {
  if (checked) {
    if (!data.checkedChainList.includes(childValue)) {
      data.checkedChainList.push(childValue)
    }
  } else {
    data.checkedChainList = data.checkedChainList.filter(value => value !== childValue)
  }
}

// 子级是否被选中
const isChildChecked = (childValue) => {
  return data.checkedChainList.includes(childValue)
}

/**
 * 弹窗回调
 */
const submit = () => {
  data.visible = false
  emits('submit', {
    checkedChainList: data.checkedChainList,
    mark: props.mark,
    visible: false
  })
  emits('update:modelValue', data.checkedChainList)
}

/**
 * 关闭弹窗
 */
const close = () => {
  data.visible = false
  emits('close', { visible: false })
}
</script>

<style lang="scss" scoped>
// 定义颜色变量
$primary-color: #20263a;
$secondary-color: #3e7bfa;
$border-color: #dedede;
$background-color: #fff;
$disabled-color: rgba(247, 247, 247, 0.6);

// 定义尺寸变量
$font-size: 27px;
$checkbox-size: 29px;
$checkbox-border-radius: 6px;
$checkbox-margin-left: 20px;
$list-width: 250px;
$list-padding: 30px 31px 30px 35px;

.content {
  height: 712px;
  width: 100%;
  color: $primary-color;
  font-size: $font-size;
  overflow: scroll;

  .parent-group {
    margin-bottom: 40px;
  }

  .parent-title {
    font-weight: bold;
    margin-bottom: 20px;
    padding: 0 20px;
  }

  .children-container {
    .children-wrap {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      align-items: center;
      gap: 16px;
    }
  }

  .checkbox-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 15px 20px;
    border-radius: 8px;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(62, 123, 250, 0.1);
    }
  }

  .checked {
    color: $secondary-color;
  }

  .checkbox {
    flex-shrink: 0;
    width: $checkbox-size;
    height: $checkbox-size;
    border: 1px solid $border-color;
    border-radius: $checkbox-border-radius;
    position: relative;

    &.checked {
      width: 32px;
      height: 33px;
      background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABSVJREFUWMN1Vz9v3EUQfXP3i32XlAlEfIngQAQI4QqBFCIq+kCkoBgi0VA534IAiSMKhPgC2A1pSQMIkogGiQoXFIgQoRR27DvfexSzM7t7GEvn+/3Zm9mZee/NrF26q0sCvqRwRgJEmgRIJknw6/Kd7yC/lxGQGOvatTJf17yjTBQISNJjQe/ZW3f1F4UzoKzYQTXYfC9kEkBAoEwwiTTBRNK6Dcc9ZZLga4pxCCQAARQfDxm5/9hEwG/bnQOESaIJBt9jFykkmTIb5hkCIPjG4k9sA7PTgyR/WBYKdQGpshuUfx41skSQUDbuzjPFiI2Vn6Ytqq4FNHgEbpgwN5lOgYi0XkdEqpHUjEmRonzuzjPL5ZlvAhgkK1bLmx4DJjXRsDHQlqAYa2oOuhfHVQK0te1YGdqdC0CCMZ7TQSS4YS9ZcdpEkgzJmhcc5POyboktg+QO25x1Oz6OFVFDAWwz1JSsZYBEKxeV3g0GAFhijR2dVMoQlAv+Vy1Ag1PGPWmAqQF0AV2DtZKpEVU4upBp4SjuaxzgMXWRZbQCF4kRC0ekkqLvv7hip09a2kQER2AEylAUrRMiNqgNpyVyFucoDmFNSaCGzsDmayv28asTfP3OKZw5aTWzxebgumPIHy1c3ZaFKIWmx4k6dCPUz7O1ub6Kd9dWAQB/PCGe7DN0wplKJQsaJzXVIwBHQT3AqvaHBXeGdFpBtrm+auH8u9/n+GBnX0+PXBohGQsmR2oQ7KmhibSr5wbceuMEJoOJ5V1VOIW4SCrNBYHsJee7c2zs7OtgXkoWcZbyjY4D2vlnR7h6bsDa2TFuvn7CpmMYE3j/ERbzTbvxG+uTTPu93SNtbO/rcNZoBGUUo/FpVOtdpfX+nwt88cuRAGDt7BifvrmC6bhtx1rCgadzc31il59fsUj7te09HMyVUk3SmdMgfqQFMzo19Nh6OMedB3MAwPmzY9y6ONV0bLX7OZJTsj3ylVrzcN40tjpnVIEbCb3Ws2HA1sM5th7M4JkY2a2LE0wHkzrlAzbXJ7i85pHf2z3Sxs6+nh7nvEp2KQWQGCAFUoZQqqJ8t+/PtfVgLgB44bkxti5NbDquGLixvmpt5Ne293A4K1RmNy9UVS2lA4ERFzQCQrZrX8yYaCj7/KcZbv88y03ceXuK6Vi2ueR8Y9vRzmhCaEoV8g2Fghol2IWvnjK7lqN0aRyrI9aHF07Y9Zcc4Y/2iGdOjTrnh3PPXipdNy/KyDKVUaBkEDS03KxcLlRbOK+DKZ/9eAgIuv7yqnXOd/Z0cISSV7QdMzsqS9MDaS4lbnMIZeuc13FKZaMxYOCTHw5sIeGjVyYBOBzMI2OmiKTp++bKWQdT/9BE03C8tnsPz/YaI1nJzM3vD7H7D/HtbzM7OCrDC7tOGSN5pl2VCRJoIX5DfdFFr6CKt4B+moWAb36dpRSn82Z+zUwghox2IqpUH3JibQZLT7f5o8SEgLY0DNB4lMjsKQ82VIqV+nE8zg0+knlnW8hkTsOcgLk0y6ECFarRuJYFcoveQ8efjgIrhXUDYw4rqPXh5H/oqNZofZbO3Q5YN62u1SfOqiCVmbCmvT/rdSOUNT2rO0N6CitI46JPezfMKFg1oB4wDe3BQc2JBmEgz3wN5RzRqab1nGhLTou9nlkjyh5FfZn6TauhVnrlAFtkNJYwT0BBr0K3en4retAcl9zG3yOQV0A9jmkzx+eu9g4sVCERPDuW54KF6nlRzTG+lkOx8bL5R4Ku/AtTMPjHM+h3JAAAAABJRU5ErkJggg==');
      background-size: 100% 100%;
      border: none;
    }

    &.semi-checked {
      width: 32px;
      height: 33px;
      background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA+9JREFUGBkFwTHRGFQUhcHvMq+gJwpwgA1iIgjCAtiIB5wQMkNF959l9z5/9Vn9OX2ibEdxEIUo21GEMrdioihEmSOIosyZVvBdvtyvX/09fWpuojJRiEI+HLXSnI7tdLYdB1G245hDOpuo0lap2fc3faIQnZWCKMpqHXa6KiPKHIVTTBxKpYOqqqwQxf30kJUOUiFqU4qK6tguxSESnWLOtNIcpaI2pcwp5pRnjmO7dSKKSo2o1IiDSohSEKGrohRqE4UUSrF6nDgKVZkoHGmOMikUoswpVuaQrlG6CUVRJoqy3UOETtXcBBGTjqQLmVMoxcp2lA6qQ9mOUqwoClEPN4lSiKIolYlClGKOmjRHUSPKHNJhlw5xkDnlmepSVHMQZcJxbKdQVoRKKaqplO3qqBAxp2O7deqgNwc1RxEVoqwkHWSOUKPUPlwXcykdE04Hh1RUlYl6zUUrlMpEIcWcYkWmUHNUF3OUoFQUstKhKGROeSt1IZWPnQ6iKCucQiGKUJRJxxyHIuYUc5RpVcXcQ5QVTocyZ1Ip6sytWFXhOM1xEAeZo5Stckbp4tYhz5wOZcJROnI6o6aV5ghFkCKZc6EoxVxCNadwxZkeRSEdHIWs/vry41VVVVdVXVVVVVVVVXVVVdX9/Pu/mtPRsd2Io3o6CFEU4kJVVVVVVVVVVVVVVVVVVVVVVAVtQlZI9XzsVlKUiVpK/fLHf1qNcFaKiaJwZFMVZW6CUpsoDk5lekpzOqZ1stNFpTaHKIIj1JQpnWKFTCrKRFmx4+Amj7JaMhet03Y6SKFTKEJRUWpSRTWnY8IRRMehSKu3j51OSqGTODiTjmKlMqeYWyFVmyiKnY6Joog0FTd5OpTtdAiFiC7bcWyns5Ki0pmU5nSIdRKhcFtUTaPUa07hSOYUyoeTdBClak5nYk6hIhWlmCjVprrabg4HvRUryZxijlK4pFFqdiQ7ThcrxMERisJZ1dlEUezsPApRhOKwm0pRqRWKIrbTQVbmEB3BbVGZKIt2VuiZKHOKOULM1ZmyQlSK0kFW5hQVhSOq5ogVxUHUozZVEBnlKswpqDlKMUfitiQUjuKMTMQKUew4Vg9V+XAuU6mRlWJF6UjmUhSnNIVyzC2ZI4iiEMd2Om+qOcXU3DomClEUMkcoCk2h5lQrVoSywlEEx6GeqTLKtWKiEJUyRwhlO4pTlVqhWiErRBEKkVHqNbdiLlIoitpURdhxTBwcO6uVlO04OEIhitoqRaV+mPtG6axjZTuEUubQnE24EaFWqG1nRVlwiMKZUihu/PND22/N98hkTgdRFMdcE0VSm5s050Mcwo4iVsiCG43om/z2P5BL6iIzKxw2AAAAAElFTkSuQmCC');
      background-size: 100% 100%;
      border: none;
    }
  }
}
</style>
