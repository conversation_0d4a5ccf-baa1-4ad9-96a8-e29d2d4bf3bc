<template>
  <VanbPop v-bind="$attrs" @close="emits('close')" @submit="submit" :visible="visible">
    <div class="self-popup-picker rounded-[8px]">
      <div
        :class="['item', 'flex', { 'item-active': active }]"
        v-for="{ name, value, active } in dict"
        @click="handleSelect({ name, value, active })"
        :key="value"
      >
        <div>{{ name }}</div>
        <img src="@/assets/img/common/gou_blue.png" />
      </div>
    </div>
  </VanbPop>
</template>
<script setup>
import VanbPop from '@/components/Van/VanbPop/index.vue'

const emits = defineEmits(['close', 'handleSelect', 'submit'])
const dict = ref([
  //多选
  { name: '智能装备产业', value: 'C1I1' },
  { name: '生物医药大健康产业', value: 'A1H1' },
  { name: '家具制造产业', value: 'F1A1' },
  { name: '建筑材料产业', value: 'F1R1' },
  { name: '金属加工产业', value: 'F1J1' },
  { name: '智能家居产业', value: 'F1Q1' },
  { name: '新能源与节能环保产业', value: 'D1F1' },
])
const props = defineProps({
  // 是否显示关闭按钮
  showClose: {
    type: Boolean,
    default: true,
  },

  // 当前选项值
  selectValue: {
    type: Array,
    default: [],
  },
  visible: {
    type: Boolean,
    default: false,
  },
})

// 回填数据
watch(
  () => props.visible,
  visible => {
    if (!visible) return
    dict.value = dict.value.map(item => {
      if (props.selectValue.includes(item.value)) {
        item.active = true
      } else {
        item.active = false
      }
      return item
    })
  },
  { immediate: true }
)
// 选中触发的方法
const handleSelect = ({ name, value, active }) => {
  dict.value = dict.value.map(item => {
    if (item.value === value) {
      item.active = !active
    }
    return item
  })
}
// 点击确定将选中的数据传递给父组件
const submit = () => {
  let arr = dict.value.filter(item => item.active)
  emits('close')
  emits('submit', arr.length ? arr.map(item => item.value) : [])
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  position: relative;
}
/*选择项样式*/
.self-popup-picker {
  margin-bottom: 68px;
  .item {
    width: 100%;
    text-align: center;
    height: 104px;
    line-height: 104px;
    font-size: 26px;
    color: #20263a;
    position: relative;
    text-align: left;
    padding: 0 24px;
    justify-content: space-between;
    align-items: center;
    img {
      width: 32px;
      height: 32px;
      opacity: 0;
    }

    &-active {
      background: #fff;
      font-family: 'PingFang SC';
      img {
        opacity: 1;
      }
      & > div {
        font-weight: 600 !important;
        color: #3e7bfa;
      }
    }
  }
}
</style>
