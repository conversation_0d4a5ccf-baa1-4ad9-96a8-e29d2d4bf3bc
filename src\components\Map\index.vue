<template>
  <div id="map-container" ref="map-container" class="container" />
</template>
<script>
/* eslint-disable no-undef */

export default {
  props: {
    defaultAddress: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      addressInfo: {
        position: '',
        address: '',
      },
      map: null,
      postionArray: [],
    }
  },
  mounted() {
    this.initMap()
  },
  methods: {
    initMap() {
      this.$nextTick(() => {
        this.map = new AMap.Map(this.$refs['map-container'], {
          zoom: 15,
          // center: this.postionArray,
        })

        this.map.on('complete', () => {
          this.setAddress()
        })
      })
    },
    setAddress() {
      if (this.defaultAddress) {
        this.geoCode(this.defaultAddress)
      }
      this.getLocation()
    },
    // 选址
    getLocation() {
      AMapUI.loadUI(['misc/PositionPicker', 'misc/PoiPicker'], (PositionPicker, PoiPicker) => {
        const positionPicker = new PositionPicker({
          mode: 'dragMap',
          map: this.map,
        })

        ;['success', 'fail'].forEach(item => {
          positionPicker.on(item, positionResult => {
            this.addressInfo = positionResult || {}
          })
        })
        positionPicker.start()
        this.map.panBy(0, 1)
      })
    },
    // 根据地址解析为经纬度，回填时定位
    geoCode(address) {
      const geocoder = new AMap.Geocoder({})
      geocoder.getLocation(address, (status, result) => {
        if (status === 'complete' && result.info === 'OK') {
          this.map.setCenter(result.geocodes[0].location)
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.container {
  height: 300px;
  width: 100%;
}
p {
  line-height: 30px;
  font-weight: bold;
}
</style>
