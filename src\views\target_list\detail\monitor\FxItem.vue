<template>
  <div class="card">
    <div class="flex justify-flex">
      <div class="flex-shrink-0 w-[96px] h-[96px] rounded-[8px] overflow-hidden mr-[20px]">
        <HImage :src="source.ent_logo" />
      </div>
      <div class="flex-1">
        <div class="t-name">
          <!-- <p>{{ active === 1 ? source.ENTNAME : source.dimension_name }}</p> -->
          <p>{{ type === 'dfx' ? source.entname || source.ENTNAME : source.dimension_name }}</p>
          <div class="flex-shrink-0">{{ source.created }}</div>
        </div>
        <div class="t-tag">
          <span v-if="source.risk_level_name" :class="`tag-${source.risk_level}`">{{ source.risk_level_name }}</span>
          <span v-if="source.risk_type_name" class="tags5">{{ source.risk_type_name }}</span>
          <span v-if="source.impact_name" :class="`tag-${source.impact}`">{{ source.impact_name }}</span>
          <template v-for="i in cls_list" :key="i">
            <span v-if="i" class="tags2">{{ i }}</span>
          </template>
        </div>
      </div>
    </div>
    <div class="t-con" v-if="active === 0">
      <div v-for="itm in source.detail" :key="itm.sort">
        <div class="t-c-tit">{{ itm.filed }}:</div>
        <div class="text-left content">{{ itm.value || '-' }}</div>
      </div>
    </div>
    <div class="t-con" v-else>
      {{ source.description }}
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  active: {
    type: Number,
    default: 0,
  },
  type: {
    //是否是日报
    type: String,
  },
})
const source = ref({})
const cls_list = ref([]) // 标签集合
watch(
  () => props.item,
  val => {
    const { cls_name } = val
    source.value = val
    if (cls_name) {
      cls_list.value = cls_name.split(',')
    }
  },
  {
    immediate: true,
  }
)
</script>

<style lang="scss" scoped>
.card {
  padding: 32px 24px 16px 24px;
  background: #fff;
  width: 100%;
  margin-bottom: 20px;
  .t-name {
    display: flex;
    justify-content: space-between;
    p {
      font-size: 32px;
      font-weight: 600;
    }
    div {
      font-size: 28px;
      color: #9b9eac;
      margin-bottom: 16px;
    }
  }
  .t-tag {
    span {
      padding: 0 10px;
      font-size: 24px;
      color: #4ab8ff;
      background: rgba(74, 184, 255, 0.1);
      margin-right: 12px;
      &.tags1 {
        color: #26c8a7;
        background-color: rgba(38, 200, 167, 0.1);
      }
      &.tags2 {
        color: #4ab8ff;
        background-color: rgba(7, 110, 228, 0.1);
      }
      &.tags3 {
        color: #fc7900;
        background-color: rgba(253, 121, 0, 0.1);
      }
      &.tags4 {
        color: #ffb93e;
        background-color: rgba(255, 185, 62, 0.1);
      }
      &.tags5 {
        color: #e72410;
        background-color: rgba(231, 36, 16, 0.1);
      }
      &.tags-1 {
        color: #e72410;
        background-color: rgba(231, 36, 16, 0.1);
      }
    }
  }
  .t-con {
    margin-top: 24px;
    padding-top: 24px;
    font-size: 28px;
    & > div {
      margin-bottom: 16px;
      display: flex;
    }
    .t-c-tit {
      color: #74798c;
      width: 140px;
    }
    .content {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .t-con::after {
    content: ' ';
    width: calc(100% + 48px);
    height: 1px;
    background: #eee;
    position: absolute;
    top: 0;
    left: -24px;
    transform: scaleY(0.5);
  }
}
// 标签样式
</style>
