<script setup>
import VanBPop from '../VanbPop/index.vue'

import { reactive, watch } from 'vue'
import { getDateTime } from '@/utils/utils'
let dayAry = [getDateTime().year + '', getDateTime().month + '', getDateTime().day + '']
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '最高年限',
  },
  date: {
    //'用于回显 2023-04-22'
    type: String,
  },
  dateType: {
    type: String,
    default: 'startDate',
  },
})
const emits = defineEmits(['close', 'submit'])
const data = reactive({
  selectedIdx: 0,
  currentDate: dayAry, //ps:里面为字符串 1996 01 03
  minDate: new Date(1990, 1, 1),
  maxDate: new Date(...dayAry),
  visible: false,
})

watch(
  () => [props.date, props.visible, props.dateType],
  ([val, bool, dateType]) => {
    data.visible = bool
    data.title = dateType == 'startDate' ? '最低年限' : dateType == 'endDate' ? '最高年限' : dateType
    if (!val) return
    let date = getDateTime(new Date(val))
    data.currentDate = [date.year + '', date.month + '', date.day + '']
  },
  {
    immediate: true,
  }
)

const formatter = (type, option) => {
  if (type === 'year') {
    option.text += '年'
  }
  if (type === 'month') {
    option.text += '月'
  }
  if (type === 'day') {
    option.text += '日'
  }
  return option
}
/**
 * 弹窗回调
 */
const submit = () => {
  data.visible = false
  emits('submit', data.currentDate.join('-'))
}
/**
 * 关闭弹窗
 */
const close = () => {
  data.visible = false
  emits('close')
}
</script>
<template>
  <VanBPop @submit="submit" @close="close" :visible="data.visible">
    <van-date-picker
      :title="data.title"
      v-model="data.currentDate"
      :min-date="data.minDate"
      :max-date="data.maxDate"
      :formatter="formatter"
      @change="item => (data.currentDate = item.selectedValues)"
    >
      <template #confirm></template>
      <template #cancel></template>
    </van-date-picker>
  </VanBPop>
</template>

<style lang="scss" scoped>
:deep(.van-picker) {
  .van-picker__toolbar {
    border-bottom: 1px solid #f7f7f7;
  }
  .van-picker-column__item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    color: var(--van-picker-option-text-color);
    &.van-picker-column__item--selected {
      color: rgba(231, 36, 16, 1);
    }
  }
  .van-picker__frame {
    position: absolute;
    top: 50%;
    right: 0;
    left: 0;
    z-index: 2;
    transform: translateY(-50%);
    pointer-events: none;
    background: rgba(42, 114, 255, 0.1);
  }
}
</style>
