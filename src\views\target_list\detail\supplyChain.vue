<template>
  <div class="mt-24 bg-white">
    <chainGragh :fullscreen="false" height="600" />

    <!-- <div
      class="fixed flex items-center justify-center mx-24 text-white w-702 bg-confirm rounded-8 bottom-40 h-80"
      @click="handleFullScreen"
    >
      横屏查看
    </div> -->
  </div>
</template>

<script setup>
import chainGragh from './gragh/index.vue'
import { pageChangeHandler } from '@/utils'
import { useRoute } from 'vue-router'

const route = useRoute()
const handleFullScreen = () => {
  pageChangeHandler('/supply-chain/fullscreen', route.query)
}
</script>
