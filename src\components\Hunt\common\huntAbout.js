import { clone } from '../../../utils/utils'
import { dateTagList, searchLeftLists, searchTermList } from './hconstant'
/*---------常量------------*/
// params最后会划分到下面
const params = {
  ent_name: '', //新，企业名称
  ent_address: '', //新，企业地址
  regionData: [], // 地区 默认写死,
  eleseic_data: [], //行業
  technology_types: [], //科技型企业
  listed_status: [], //上市状态
  super_dimension_patent_category: [], //专利内容
  expand_status: [], //疑似扩张
  foreign: [], //外资企业
  ent_scale: [], // 企业规模
  benefit_assess: [], // 效益评估
  // capital_event: '', // 	资本事件-这个目前没用到--放开后搜索模版哪里需要单独处理
  register_time: [], //	注册时间
  register_capital: [], // 注册资本
  ent_status: [], //企业状态
  shit_type: [], //实体类型
  enttype_data: [], //企業类型
  all_cert_data: [], //企业许可
  phone_data: [], // 联系方式
  tel_data: [], // 固定电话
  contactWays: [], //联系电话
  email_data: [], //新，联系邮箱
  super_dimension_social_num: [], //新，参保人数
  super_dimension_biding: [], //新，招投标
  super_dimension_job_info: [], //新，招聘
  tax_credit: [], //新， 纳税信用
  financing_info: [], //新，融资信息
  super_dimension_trademark: [], //新， 商标信息
  super_dimension_patent: [], //新， 专利信息
  super_dimension_android_app: [], //新， 安卓APP
  super_dimension_ios_app: [], //新， 苹果APP
  super_dimension_mini_app: [], //新， 小程序
  super_dimension_wx_extension: [], //新， 微信公众号
  super_dimension_weibo_extension: [], //新， 微博
  untrustworthy_info_data: [], //新， 失信信息
  judgment_doc_data: [], //新， 裁判文书
  adminstrative_penalties_data: [], //新， 行政处罚
  chattel_mortage_data: [], //新， 行政处罚
  abnormal_operation_data: [], //新， 行政处罚
  software_copyright_data: [], //新， 软件著作权
  work_copyright_data: [], //新， 作品著作权
  super_dimension_website: [], //新， 官网信息
  super_dimension_icp: [], //新， ICP备案
  chain_codes: [], //所属产业链
  chain_codes_data: [], //所属产业链
  leading_ent: [], //龙头企业
  types: [],
  levels: [],
  ckfw: [],
}
// 搜索--单选的情况
const searRadioConstant = {
  list: [
    'tel_data',
    'contactWays',
    'phone_data',
    'email_data',
    'super_dimension_biding',
    'tax_credit',
    'super_dimension_trademark',
    'super_dimension_android_app',
    'super_dimension_ios_app',
    'super_dimension_mini_app',
    'super_dimension_wx_extension',
    'untrustworthy_info_data',
    'judgment_doc_data',
    'adminstrative_penalties_data',
    'chattel_mortage_data',
    'abnormal_operation_data',
    'software_copyright_data',
    'work_copyright_data',
    'super_dimension_website',
    'super_dimension_icp',
    'super_dimension_weibo_extension',
    'super_dimension_patent',
    'leading_ent',
    'super_dimension_job_info',
  ],
  map: {
    tel_data: '固定电话',
    contactWays: '联系电话',
    phone_data: '手机号码',
    email_data: '联系邮箱',
    super_dimension_biding: '招投标',
    super_dimension_job_info: '招聘',
    tax_credit: '纳税信用',
    super_dimension_trademark: '商标信息',
    super_dimension_patent: '专利信息',
    super_dimension_android_app: '安卓APP',
    super_dimension_ios_app: '苹果APP',
    super_dimension_mini_app: '小程序',
    super_dimension_wx_extension: '微信公众号',
    super_dimension_weibo_extension: '微博',
    untrustworthy_info_data: '失信信息',
    judgment_doc_data: '裁判文书',
    adminstrative_penalties_data: '行政处罚',
    chattel_mortage_data: '动产抵押',
    leading_ent: '龙头企业',
    // eslint-disable-next-line no-dupe-keys
    chattel_mortage_data: '经营异常',
    software_copyright_data: '软件著作权',
    work_copyright_data: '作品著作权',
    super_dimension_website: '官网信息',
    super_dimension_icp: 'ICP备案',
    abnormal_operation_data: '经营异常',
  },
}
// 搜索--多选的情况
const searMultiSelectConstant = {
  list: [
    'capital_event',
    'shit_type',
    'ent_status',
    'technology_types',
    'financing_info',
    'listed_status',
    'super_dimension_patent_category',
    'expand_status',
    'chain_codes',
    'foreign',
    'types',
    'levels',
    'ckfw',
  ],
  map: {
    capital_event: '资本事件',
    shit_type: '实体类型',
    ent_status: '企业状态',
    technology_types: '科技型企业',
    financing_info: '融资信息',
    listed_status: '上市状态',
    super_dimension_patent_category: '专利内容',
    expand_status: '疑似扩张',
    foreign: '外资企业',
    types: '穿透方向',
    levels: '穿透层级',
    ckfw: '查看范围',
  },
}
// 搜索-- 多选 或 多选+自定义输入的情况    数据结构是 xx&xx
const searMultNotCusConstant = {
  list: ['ent_scale', 'benefit_assess'],
  map: {
    ent_scale: '企业规模',
    benefit_assess: '效益评估',
  },
}
const searCheckPopConstant = {
  //多选+自定义时间弹窗类型
  list: ['register_time'],
  map: {
    register_time: '注册时间',
  },
  tip: {
    register_time: {
      max: '最高年限',
      min: '最低年限',
      unit: '年',
    },
  },
}
const searMultIptConstant = {
  //多选+自定义时间弹窗类型
  list: ['register_capital', 'super_dimension_social_num'],
  map: {
    register_capital: '注册资本',
    super_dimension_social_num: '参保人数',
  },
  tip: {
    register_capital: {
      max: '最高资本',
      min: '最低资本',
      unit: '万元',
    },
    super_dimension_social_num: {
      max: '最高人数',
      min: '最低人数',
      unit: '人',
    },
  },
}
const searMultAllConstant = {
  list: [...searMultNotCusConstant['list'], ...searCheckPopConstant['list'], ...searMultIptConstant['list']],
}
// 搜索--弹窗的情况
const searMultPopConstant = {
  areas: {
    data: 'regionData',
    label: '所在地区',
  },
  trade_types: {
    data: 'eleseic_data',
    label: '所属行业',
  },
  ent_type: {
    data: 'enttype_data',
    label: '企业类型',
  },
  ent_cert: {
    data: 'all_cert_data',
    label: '企业许可',
  },
  chain_codes: {
    data: 'chain_codes_data',
    label: '所属产业链',
  },
}

// 搜索--输入框
const searInputConstant = {
  list: ['ent_name', 'ent_address'],
  map: {
    ent_name: '企业名称',
    ent_address: '企业地址',
  },
}
// a.1 处理上传布尔的情况 --不是数组要拿出来
const boolConstant = [
  'super_dimension_biding',
  'super_dimension_job_info',
  'super_dimension_trademark',
  'super_dimension_patent',
  'super_dimension_android_app',
  'super_dimension_ios_app',
  'super_dimension_mini_app',
  'super_dimension_wx_extension',
  'super_dimension_weibo_extension',
  'super_dimension_website',
  'super_dimension_icp',
  'leading_ent',
]
// a.2 处理上传对象-有父亲的情况
const parentType = ['super_dimension_']
/**
 * 生成渲染列表
 */
let getRenderList = function (left = searchLeftLists, allList = searchTermList) {
  const result = []
  left.forEach(item => {
    const { title, map, hideTitle, isBorder } = item
    const mapEntries = Object.entries(map).sort((a, b) => {
      return Object.keys(map).indexOf(a[0]) - Object.keys(map).indexOf(b[0])
    })
    const list = mapEntries.map(([key, value]) => ({
      key,
      value,
      active: false,
      isBorder: isBorder,
    }))

    const filteredList = allList
      // eslint-disable-next-line no-prototype-builtins
      .filter(i => map.hasOwnProperty(i.type))
      .map(items => ({
        isBorder: isBorder,
        ...items,

        tesTitle: item.isBorder && item.title === '所属产业链' ? '产业链环节' : item.title,
      }))
      .sort((a, b) => {
        const aIndex = Object.keys(map).indexOf(a.type)
        const bIndex = Object.keys(map).indexOf(b.type)
        return aIndex - bIndex
      })

    result.push({
      title,
      onlyText: true,
      isOpen: true,
      isActive: false,
      map: list,
      hideTitle,
      isBorder,
    })
    result.push(...filteredList)
  })
  return result
}

/*---------方法------------*/
let handleMultiple = (ary = []) => {
  if (!ary.length) return []
  let temParr = JSON.parse(JSON.stringify(ary))
  let checkedAry = temParr.filter(i => i.status == 'checked' && !i.ischildren)
  checkedAry = checkedAry.filter(item => !checkedAry.map(item => item.code).includes(item.parent))
  if (!checkedAry.length) return ary
  checkedAry.forEach(i => {
    temParr = temParr.filter(itm => i.code != itm.parent)
  })
  return temParr
}
/**
 * 过滤出需要的弹窗类型的数组 -- 地区为例
 * @param {*} params
 * @returns
 */
let handleData = function (params) {
  let obj = clone(params) //Object.assign
  obj['areas'] =
    params.regionData?.length > 0
      ? handleMultiple(params.regionData)
          .filter(i => i.status === 'checked')
          .map(i => i.code)
      : []
  obj['trade_types'] =
    params.eleseic_data?.length > 0
      ? handleMultiple(params.eleseic_data)
          .filter(i => i.status === 'checked')
          .map(i => i.code)
      : []
  obj['ent_type'] =
    params.enttype_data?.length > 0
      ? handleMultiple(params.enttype_data)
          .filter(i => i.status === 'checked')
          .map(i => i.code)
      : []
  obj['ent_cert'] =
    params.all_cert_data?.length > 0
      ? handleMultiple(params.all_cert_data)
          .filter(i => i.status === 'checked')
          .map(i => i.code)
      : []
  // 产业链比较特殊
  // obj['chain_codes'] =
  //   params.chain_codes_data?.length > 0
  //     ? handleMultiple(params.chain_codes_data)
  //         .filter(i => i.status === 'checked')
  //         .map(i => i.code)
  //     : []

  obj['chain_codes'] =
    params.chain_codes_data?.length > 0
      ? handleMultiple(params.chain_codes_data)
          .filter(i => i.active)
          .map(i => i.code)
      : []
  return JSON.stringify(obj)
}

// 处理高级搜索类似地区弹窗---名字哪里
let getNameFromPop = function (data) {
  if (data?.length > 0 && data?.[0]?.MultipleCelectionSingleSelection) {
    if (data?.[0]?.MultipleCelectionSingleSelection === 'isSingles') {
      // 产业链多选的时候
      return data.map(i => i.name).join('/')
    }
    //产业链单独处理--单选的时候
    return data[0].name
  }
  let arr = []
  data?.length > 0 &&
    data.forEach((i, idx, oldArr) => {
      if (i.status == 'checked') {
        if (i.parent && i.parent != '') {
          // 有父亲
          let parent = oldArr.filter(itm => i.parent == itm.code)[0]
          parent.status == 'checked' ? arr.push(parent) : arr.push(i)
        } else {
          // 没有父亲
          arr.push(i)
        }
      }
    })
  // 排重
  let newobj = {}
  arr = arr.reduce((preVal, curVal) => {
    newobj[curVal.code] ? '' : (newobj[curVal.code] = preVal.push(curVal))
    return preVal
  }, []) //.sort((a, b) => (a.level - b.level))
  return arr.map(item => item.name).join('/')
}
// 将'true','false'转换成==》true false 如果有前缀还要整合成对象形式
let handlestructure = function (data) {
  let params = Object.assign({}, data)
  boolConstant.forEach(item => {
    if (params[item]?.length) {
      params[item] = JSON.parse(params[item][0]) //将 "true"==>true
    } else {
      delete params[item]
    }
  })
  parentType.forEach(i => {
    Object.keys(params).forEach(itm => {
      if (itm.startsWith(i)) {
        let suffix = itm.slice(i.length),
          prefix = itm.slice(0, i.length - 1),
          obj = {}
        obj[suffix] = params[itm]
        params[prefix] = {
          ...params[prefix],
          ...obj,
        }
        delete params[itm]
      }
    })
  })
  return params
}
/**
 * 告诉外面当前是否是高亮
 * @param {*} paramsData
 * @returns Bool
 */
let getHeightStatus = function (paramsData) {
  //获取高亮状态
  let isHeight = false //是否有选中的 外面设置高亮要用
  Object.keys(paramsData).some(keys => {
    //这里如果遇到以前的数据可能会报错，所以以前的数据后端要清除没法兼容
    // 这种有自定义需要单独处理
    if (searCheckPopConstant['list'].includes(keys) || searMultIptConstant['list'].includes(keys)) {
      if (paramsData[keys].length > 0 && (paramsData[keys][0]?.start || paramsData[keys][0]?.end)) {
        isHeight = true
        return true
      } else if (searInputConstant['list'].includes(keys)) {
        if (paramsData[keys].trim().length > 0) {
          isHeight = true
          // console.log(1)
          return true
        }
      } else {
        isHeight = false
      }
    } else if (paramsData[keys].length > 0) {
      isHeight = true
      return true
    }
  })
  // console.log(isHeight)
  return isHeight
}
/**
 * 分割字符串
 * @param {*} str
 * @returns
 */
const divisionStr = str => {
  let sign = '$'
  let obj = {
    start: '',
    end: '',
  }
  if (str.indexOf(sign) > -1) {
    let arr = str.split(sign)
    obj.start = arr[0]
    obj.end = arr[1]
  }
  return obj
}
/**
 * 将除了输入框外的状态重置
 * @param {*} data
 * @param {*} type
 */
const setTagStatus = (data, type) => {
  let itemList = data.itemList,
    params = data.params
  for (let item of itemList) {
    // 清除对应类型标签的选中状态
    if (item.type === type) {
      item.list = item.list.map(tag => {
        tag.active = false
        return tag
      })
    }
  }
  params[type] = []
}

export {
  // 企业搜索常量
  dateTagList,
  getRenderList,
  params,
  searRadioConstant,
  searMultiSelectConstant,
  searMultAllConstant,
  searMultPopConstant,
  searInputConstant,
  searMultNotCusConstant,
  searCheckPopConstant,
  searMultIptConstant,
  searchTermList,
  // 方法
  divisionStr,
  setTagStatus,
  handleData,
  getNameFromPop,
  handlestructure,
  getHeightStatus,
  handleMultiple,
}
