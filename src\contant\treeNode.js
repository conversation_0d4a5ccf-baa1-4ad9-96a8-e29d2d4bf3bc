const treeNode = [
  {
    "belong_to": "",
    "parent_code": "",
    "chain_code": "A1",
    "level": "1",
    "name": "大健康产业",
    "tag": "1",
    "fig": "0",
    "bespoke": "",
    "children": [
      {
        "belong_to": "",
        "parent_code": "A1",
        "chain_code": "A1A1",
        "level": "2",
        "name": "生物医药产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "A1",
        "chain_code": "A1B1",
        "level": "2",
        "name": "医疗器械产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "1",
        "parent_code": "A1",
        "chain_code": "A1C1",
        "level": "2",
        "name": "大健康产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "A1",
        "chain_code": "A1D1",
        "level": "2",
        "name": "高端医疗器械产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "1",
        "parent_code": "A1",
        "chain_code": "A1E1",
        "level": "2",
        "name": "中药产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "1",
        "parent_code": "A1",
        "chain_code": "A1F1",
        "level": "2",
        "name": "化学药产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "A1",
        "chain_code": "A1G1",
        "level": "2",
        "name": "生命科学产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "A1",
        "chain_code": "A1H1",
        "level": "2",
        "name": "生物医药与大健康产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "A1",
        "chain_code": "A1I1",
        "level": "2",
        "name": "生物医药产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "A1",
        "chain_code": "A1J1",
        "level": "2",
        "name": "高性能医疗器械产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "A1",
        "chain_code": "A1K1",
        "level": "2",
        "name": "高端医疗器械产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "A1",
        "chain_code": "A1L1",
        "level": "2",
        "name": "高端医疗器械产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "A1",
        "chain_code": "A1M1",
        "level": "2",
        "name": "生物技术药及化学制剂产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      }
    ]
  },
  {
    "belong_to": "",
    "parent_code": "",
    "chain_code": "B1",
    "level": "1",
    "name": "新一代信息技术产业",
    "tag": "1",
    "fig": "0",
    "bespoke": "",
    "children": [
      {
        "belong_to": "",
        "parent_code": "B1",
        "chain_code": "B1A1",
        "level": "2",
        "name": "人工智能产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1A2",
        "level": "2",
        "name": "智能家电产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "B1",
        "chain_code": "B1B1",
        "level": "2",
        "name": "集成电路产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1B2",
        "level": "2",
        "name": "软件和信息服务产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "B1",
        "chain_code": "B1C1",
        "level": "2",
        "name": "软件和信息服务产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1C2",
        "level": "2",
        "name": "空天信息产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1D1",
        "level": "2",
        "name": "电子元器件产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1D2",
        "level": "2",
        "name": "半导体与集成电路产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "1",
        "parent_code": "B1",
        "chain_code": "B1E1",
        "level": "2",
        "name": "大数据产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1E2",
        "level": "2",
        "name": "基础软件及行业应用软件产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "1",
        "parent_code": "B1",
        "chain_code": "B1F1",
        "level": "2",
        "name": "电子元器件产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1F2",
        "level": "2",
        "name": "软件和信息服务产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "1",
        "parent_code": "B1",
        "chain_code": "B1G1",
        "level": "2",
        "name": "新型显示产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1G2",
        "level": "2",
        "name": "新一代电子信息制造业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "1",
        "parent_code": "B1",
        "chain_code": "B1H1",
        "level": "2",
        "name": "MEMS传感器产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1H2",
        "level": "2",
        "name": "新型显示产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "B1",
        "chain_code": "B1I1",
        "level": "2",
        "name": "软件和信息服务产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1I2",
        "level": "2",
        "name": "传感器及仪器仪表产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "B1",
        "chain_code": "B1J1",
        "level": "2",
        "name": "新一代电子信息制造业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "B1",
        "chain_code": "B1K1",
        "level": "2",
        "name": "功率半导体及集成电路产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1L1",
        "level": "2",
        "name": "网络与通信产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1M1",
        "level": "2",
        "name": "超高清视频显示产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1N1",
        "level": "2",
        "name": "智能终端产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1O1",
        "level": "2",
        "name": "半导体与集成电路产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1P1",
        "level": "2",
        "name": "传感器及仪器仪表产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1Q1",
        "level": "2",
        "name": "AI及机器人产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1R1",
        "level": "2",
        "name": "新型显示产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1S1",
        "level": "2",
        "name": "服务器产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1T1",
        "level": "2",
        "name": "AI大模型产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1U1",
        "level": "2",
        "name": "卫星互联网产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1V1",
        "level": "2",
        "name": "商业航天产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1W1",
        "level": "2",
        "name": "软件和信息服务产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1X1",
        "level": "2",
        "name": "集成电路产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1Y1",
        "level": "2",
        "name": "智能手机产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "B1",
        "chain_code": "B1Z1",
        "level": "2",
        "name": "笔电产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      }
    ]
  },
  {
    "belong_to": "",
    "parent_code": "",
    "chain_code": "C1",
    "level": "1",
    "name": "高端装备制造产业",
    "tag": "1",
    "fig": "0",
    "bespoke": "",
    "children": [
      {
        "belong_to": "",
        "parent_code": "C1",
        "chain_code": "C1A1",
        "level": "2",
        "name": "智能制造装备产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "C1",
        "chain_code": "C1B1",
        "level": "2",
        "name": "轨道交通产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "C1",
        "chain_code": "C1C1",
        "level": "2",
        "name": "海洋工程产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "C1",
        "chain_code": "C1D1",
        "level": "2",
        "name": "航空航天产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "1",
        "parent_code": "C1",
        "chain_code": "C1E1",
        "level": "2",
        "name": "工业机器人产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "C1",
        "chain_code": "C1F1",
        "level": "2",
        "name": "民用航空产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "C1",
        "chain_code": "C1G1",
        "level": "2",
        "name": "智能装备及智能制造产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "C1",
        "chain_code": "C1H1",
        "level": "2",
        "name": "装备制造产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "C1",
        "chain_code": "C1I1",
        "level": "2",
        "name": "智能装备产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "C1",
        "chain_code": "C1J1",
        "level": "2",
        "name": "高端装备与仪器产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "C1",
        "chain_code": "C1K1",
        "level": "2",
        "name": "低空经济与空天产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "C1",
        "chain_code": "C1L1",
        "level": "2",
        "name": "智能制造装备产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "C1",
        "chain_code": "C1M1",
        "level": "2",
        "name": "动力装备产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "C1",
        "chain_code": "C1N1",
        "level": "2",
        "name": "低空经济产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "C1",
        "chain_code": "C1O1",
        "level": "2",
        "name": "低空经济产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "C1",
        "chain_code": "C1P1",
        "level": "2",
        "name": "快速响应火箭产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "C1",
        "chain_code": "C1Q1",
        "level": "2",
        "name": "通导遥一体化产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "C1",
        "chain_code": "C1R1",
        "level": "2",
        "name": "新型卫星产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "C1",
        "chain_code": "C1S1",
        "level": "2",
        "name": "北斗应用产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "C1",
        "chain_code": "C1T1",
        "level": "2",
        "name": "天地一体通信系统产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      }
    ]
  },
  {
    "belong_to": "",
    "parent_code": "",
    "chain_code": "D1",
    "level": "1",
    "name": "新能源产业",
    "tag": "1",
    "fig": "0",
    "bespoke": "",
    "children": [
      {
        "belong_to": "",
        "parent_code": "D1",
        "chain_code": "D1A1",
        "level": "2",
        "name": "光伏发电产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "D1",
        "chain_code": "D1B1",
        "level": "2",
        "name": "锂电产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "D1",
        "chain_code": "D1C1",
        "level": "2",
        "name": "晶硅光伏产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "D1",
        "chain_code": "D1D1",
        "level": "2",
        "name": "新能源及新型储能产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "D1",
        "chain_code": "D1E1",
        "level": "2",
        "name": "光伏产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "D1",
        "chain_code": "D1F1",
        "level": "2",
        "name": "新能源与节能环保产业链",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "D1",
        "chain_code": "D1G1",
        "level": "2",
        "name": "新能源产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "D1",
        "chain_code": "D1H1",
        "level": "2",
        "name": "新能源及新型储能产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "D1",
        "chain_code": "D1I1",
        "level": "2",
        "name": "新能源产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "D1",
        "chain_code": "D1J1",
        "level": "2",
        "name": "新型储能产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      }
    ]
  },
  {
    "belong_to": "",
    "parent_code": "",
    "chain_code": "E1",
    "level": "1",
    "name": "新能源汽车产业",
    "tag": "1",
    "fig": "0",
    "bespoke": "",
    "children": [
      {
        "belong_to": "",
        "parent_code": "E1",
        "chain_code": "E1A1",
        "level": "2",
        "name": "新能源汽车产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "E1",
        "chain_code": "E1B1",
        "level": "2",
        "name": "汽车及零部件产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "E1",
        "chain_code": "E1C1",
        "level": "2",
        "name": "智能网联汽车产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "1",
        "parent_code": "E1",
        "chain_code": "E1D1",
        "level": "2",
        "name": "汽车及零部件产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "E1",
        "chain_code": "E1E1",
        "level": "2",
        "name": "智能网联新能源汽车产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "E1",
        "chain_code": "E1F1",
        "level": "2",
        "name": "智能网联新能源汽车产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "E1",
        "chain_code": "E1G1",
        "level": "2",
        "name": "智能网联新能源汽车产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "E1",
        "chain_code": "E1H1",
        "level": "2",
        "name": "智能网联汽车产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      }
    ]
  },
  {
    "belong_to": "",
    "parent_code": "",
    "chain_code": "F1",
    "level": "1",
    "name": "其它产业",
    "tag": "1",
    "fig": "0",
    "bespoke": "",
    "children": [
      {
        "parent_code": "F1",
        "chain_code": "F1A1",
        "level": "2",
        "name": "家具制造产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1A2",
        "level": "2",
        "name": "特色文旅",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1B1",
        "level": "2",
        "name": "食品加工制造产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1B2",
        "level": "2",
        "name": "食品及农产品加工产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "F1",
        "chain_code": "F1C1",
        "level": "2",
        "name": "农副产品加工产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "F1",
        "chain_code": "F1D1",
        "level": "2",
        "name": "食品加工制造产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "F1",
        "chain_code": "F1E1",
        "level": "2",
        "name": "轻工纺织产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "F1",
        "chain_code": "F1F1",
        "level": "2",
        "name": "煤及天然气化工产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "F1",
        "chain_code": "F1G1",
        "level": "2",
        "name": "绿色建材产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "F1",
        "chain_code": "F1H1",
        "level": "2",
        "name": "食品及农产品加工产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1I1",
        "level": "2",
        "name": "特色农业加工",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1J1",
        "level": "2",
        "name": "金属加工产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "F1",
        "chain_code": "F1K1",
        "level": "2",
        "name": "纺织服装产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "F1",
        "chain_code": "F1L1",
        "level": "2",
        "name": "高端摩托车产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "F1",
        "chain_code": "F1M1",
        "level": "2",
        "name": "生物制造产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "F1",
        "chain_code": "F1N1",
        "level": "2",
        "name": "特色农林产品精加工产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1O1",
        "level": "2",
        "name": "煤基精细化工产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "F1",
        "chain_code": "F1P1",
        "level": "2",
        "name": "天然气化工产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1Q1",
        "level": "2",
        "name": "智能家居产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1R1",
        "level": "2",
        "name": "建筑材料产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1S1",
        "level": "2",
        "name": "金融科技产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1T1",
        "level": "2",
        "name": "高端摩托车产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1U1",
        "level": "2",
        "name": "农机装备产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1V1",
        "level": "2",
        "name": "文化旅游产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1W1",
        "level": "2",
        "name": "特色食品产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1X1",
        "level": "2",
        "name": "磷化工产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1Y1",
        "level": "2",
        "name": "特色纺织品产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "F1",
        "chain_code": "F1Z1",
        "level": "2",
        "name": "通用机械产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      }
    ]
  },
  {
    "belong_to": "",
    "parent_code": "",
    "chain_code": "G1",
    "level": "1",
    "name": "新材料产业",
    "tag": "1",
    "fig": "0",
    "bespoke": "",
    "children": [
      {
        "belong_to": "",
        "parent_code": "G1",
        "chain_code": "G1A1",
        "level": "2",
        "name": "石墨烯产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "G1",
        "chain_code": "G1B1",
        "level": "2",
        "name": "膜材料产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "G1",
        "chain_code": "G1C1",
        "level": "2",
        "name": "先进材料产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "G1",
        "chain_code": "G1D1",
        "level": "2",
        "name": "纤维及复合材料产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "G1",
        "chain_code": "G1E1",
        "level": "2",
        "name": "高端合成材料产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "G1",
        "chain_code": "G1F1",
        "level": "2",
        "name": "前沿新材料产业",
        "tag": "1",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "G1",
        "chain_code": "G1G1",
        "level": "2",
        "name": "轻合金材料产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "G1",
        "chain_code": "G1H1",
        "level": "2",
        "name": "纤维及复合材料产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "G1",
        "chain_code": "G1I1",
        "level": "2",
        "name": "轻合金材料产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "G1",
        "chain_code": "G1J1",
        "level": "2",
        "name": "合成材料产业链-聚酯",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "G1",
        "chain_code": "G1K1",
        "level": "2",
        "name": "合成材料产业链-聚酰胺",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "G1",
        "chain_code": "G1L1",
        "level": "2",
        "name": "合成材料产业链-聚烯烃",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "G1",
        "chain_code": "G1M1",
        "level": "2",
        "name": "合成材料产业链-聚氨酯",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "G1",
        "chain_code": "G1N1",
        "level": "2",
        "name": "合成材料产业-聚甲基丙烯酸甲酯",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "G1",
        "chain_code": "G1O1",
        "level": "2",
        "name": "绿色包装产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "G1",
        "chain_code": "G1P1",
        "level": "2",
        "name": "前沿新材料产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      }
    ]
  },
  {
    "parent_code": "",
    "chain_code": "H1",
    "level": "1",
    "name": "数字与时尚产业",
    "tag": "0",
    "fig": "0",
    "bespoke": "",
    "children": [
      {
        "parent_code": "H1",
        "chain_code": "H1A1",
        "level": "2",
        "name": "数字创意产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "H1",
        "chain_code": "H1B1",
        "level": "2",
        "name": "现代时尚产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "H1",
        "chain_code": "H1C1",
        "level": "2",
        "name": "数字经济产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      }
    ]
  },
  {
    "parent_code": "",
    "chain_code": "I1",
    "level": "1",
    "name": "绿色低碳产业",
    "tag": "0",
    "fig": "0",
    "bespoke": "",
    "children": [
      {
        "parent_code": "I1",
        "chain_code": "I1A1",
        "level": "2",
        "name": "安全节能环保产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      }
    ]
  },
  {
    "parent_code": "",
    "chain_code": "J1",
    "level": "1",
    "name": "长沙产业大脑平台项目",
    "tag": "0",
    "fig": "0",
    "bespoke": "",
    "children": [
      {
        "parent_code": "J1",
        "chain_code": "?J1A1",
        "level": "2",
        "name": "人工智能及传感器（含数控机床）产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1A1",
        "level": "2",
        "name": "人工智能及传感器（含数控机床）产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "J1",
        "chain_code": "J1B1",
        "level": "2",
        "name": "新一代半导体及集成电路产业（集成电路）",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "J1",
        "chain_code": "J1C1",
        "level": "2",
        "name": "新一代半导体及集成电路产业（碳基）",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "J1",
        "chain_code": "J1D1",
        "level": "2",
        "name": "智慧物流产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "J1",
        "chain_code": "J1E1",
        "level": "2",
        "name": "新一代自主安全计算系统产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "belong_to": "",
        "parent_code": "J1",
        "chain_code": "J1F1",
        "level": "2",
        "name": "新型合金（含3D打印）产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1G1",
        "level": "2",
        "name": "大健康产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1H1",
        "level": "2",
        "name": "大数据产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1I1",
        "level": "2",
        "name": "先进储能材料产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1J1",
        "level": "2",
        "name": "节能环保产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1K1",
        "level": "2",
        "name": "新能源汽车（含燃油车）产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1L1",
        "level": "2",
        "name": "新型显示产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1M1",
        "level": "2",
        "name": "工程机械产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1N1",
        "level": "2",
        "name": "航空航天（含北斗）产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1O1",
        "level": "2",
        "name": "现代种业及食品产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1P1",
        "level": "2",
        "name": "智能建造产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      },
      {
        "parent_code": "J1",
        "chain_code": "J1Q1",
        "level": "2",
        "name": "智能网联汽车产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      }
    ]
  },
  {
    "parent_code": "",
    "chain_code": "K1",
    "level": "1",
    "name": "临时产业",
    "tag": "0",
    "fig": "0",
    "bespoke": "",
    "children": [
      {
        "parent_code": "K1",
        "chain_code": "K1A1",
        "level": "2",
        "name": "数字创意产业",
        "tag": "0",
        "fig": "0",
        "bespoke": "1",
        "children": []
      }
    ]
  }
]
