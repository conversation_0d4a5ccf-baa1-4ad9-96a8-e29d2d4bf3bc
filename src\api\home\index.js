import { Post, Get, Delete } from '@/request'
import { CHAIN_URL } from '../constants.js'

export const getSearchEntList = (params = {}) => {
  return Post(`${CHAIN_URL}/search/ent/list`, params)
}

/**
 * 获取产业 强链节点
 */
export const getStrongChainNodes = code => {
  return Get(`${CHAIN_URL}/supplementary/strong_chain/${code}`)
}

/**
 * 获取产业 补链节点
 */
export const getSupplementaryChainNodes = code => {
  return Get(`${CHAIN_URL}/supplementary/supplementary_chain/${code}`)
}

export const getChainCodePlus = (chainCode, areaCode) => {
  return Get(`${CHAIN_URL}/industry/chain/code/${chainCode}/plus/tag?area_code=${areaCode}`)
}

/**
 * 新增或编辑强链节点
 */
export const setStrongChain = (code, data) => {
  return Post(`${CHAIN_URL}/supplementary/strong_chain/${code}`, data)
}

export const setSupplementaryChain = (code, data) => {
  return Post(`${CHAIN_URL}/supplementary/supplementary_chain/${code}`, data)
}

export const getSupplementaryData = () => {
  return Post(`${CHAIN_URL}/supplementary/index_page`)
}

// 首页, 存量企业列表
export const getPointEntList = data => {
  return Post(`${CHAIN_URL}/simple/ent/list`, data)
}

// 单个添加重点存量企业
export const addPointEnt = data => {
  return Post(`${CHAIN_URL}/simple/ent/add`, data)
}

// 批量删除重点存量企业
export const removePointEntBatch = data => {
  return Post(`${CHAIN_URL}/simple/ent/remove/batch`, data)
}

// 上传存量企业
export const uploadPointEnt = data => {
  return Post(`${CHAIN_URL}/simple/ent/up`, data, {
    'Content-Type': '	multipart/form-data',
  })
}

export const getReserveList = (data) => {
  return Post(`${CHAIN_URL}/stocking/list`, data);
};
