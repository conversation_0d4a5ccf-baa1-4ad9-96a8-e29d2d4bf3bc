<template>
  <van-dropdown-menu style="width: 100%" active-color="#076EE">
    <van-dropdown-item
      v-for="(item, index) in options"
      :key="index"
      @opened="opened(item, index)"
      @closed="closed(item, index)"
      ref="dropRef"
      :title-class="searchParams?.[item.mark]?.length ? 'activeMenuItem' : ''"
    >
      <template #title>
        <span>{{ item.newName || item.name }}</span>
      </template>
      <template #default>
        <template v-if="index == 0">
          <MultiplecChoice
            :visible="item.visible"
            :mark="item.mark"
            :dataType="item.dataType"
            @submit="val => submitSub(val)"
            :oldData="item.oldData"
            @close="closeAllPop"
          />
        </template>
        <template v-if="index == 1">
          <!-- 全部行业 -->
          <MultiplecChoice
            :visible="item.visible"
            :mark="item.mark"
            :dataType="item.dataType"
            @submit="val => submitSub(val)"
            :oldData="item.oldData"
            @close="closeAllPop"
          />
        </template>
        <template v-if="index === 2">
          <div @click.stop>
            <div class="flex flex-wrap p-40 pb-0">
              <div
                v-for="(item, i) in registerCapitalOptions"
                class="w-[47%] bg-grey py-12 px-16 text-center text-primary text-24 mb-32 rounded-8"
                :class="{
                  'mr-38': i % 2 === 0,
                  'bg-confirm text-white': rangeValue === item.value,
                }"
                @click="
                  () => {
                    customRange.start = undefined
                    customRange.end = undefined

                    if (rangeValue === item.value) {
                      rangeValue = ''
                    } else {
                      rangeValue = item.value
                    }
                  }
                "
              >
                {{ item.name }}
              </div>
            </div>

            <div class="flex items-center px-40 pb-40">
              <div class="flex !bg-grey items-center h-56 rounded-8">
                <van-field
                  v-model="customRange.start"
                  class=""
                  :maxlength="10"
                  type="number"
                  @blur="
                    () => {
                      Number(customRange.start) > Number(customRange.end) && (customRange.start = undefined)
                    }
                  "
                  @change="rangeValue = ''"
                />
                <span class="text-primary">-</span>
                <van-field
                  v-model="customRange.end"
                  class=""
                  :maxlength="10"
                  type="number"
                  @blur="
                    () => {
                      Number(customRange.start) > Number(customRange.end) && (customRange.end = undefined)
                    }
                  "
                  @change="rangeValue = ''"
                />
              </div>
              <span class="flex ml-20 break-keep text-24 text-primary">万元</span>
            </div>

            <div class="flex p-24 footer h-128">
              <button class="close" @click="closeAllPop">取消</button>
              <button class="submit" @click="handleSearch()">确认</button>
            </div>
          </div>
        </template>
        <template v-if="index == 3">
          <div @click.stop>
            <div class="w-full overflow-y-auto overlay max-h-600">
              <div
                v-for="item in evaluateOptions"
                :key="item.value"
                class="flex items-center justify-between py-24 mx-24 border-b border-[#eee]"
                @click="handleChangeScore(item)"
              >
                <span
                  class="text-primary text-28"
                  :class="[item.label === activeEvaluate ? 'text-selected font-semibold' : '']"
                  >{{ item.label }}</span
                >
                <SvgIcon v-if="item.label === activeEvaluate" name="tick" class="w-32 h-32" />
              </div>
            </div>
          </div>
        </template>
      </template>
    </van-dropdown-item>
  </van-dropdown-menu>
</template>

<script setup>
import MultiplecChoice from '@/components/Hunt/component/MultiplecChoice/btmIndex.vue'
import { handleMultiple, params, getNameFromPop } from '@/components/Hunt/common/huntAbout'
import { defineEmits, defineProps, computed } from 'vue'

const emits = defineEmits(['submit'])
const props = defineProps({
  filterOptions: {
    type: Array,
    default: undefined,
  },
})
const options = computed(() => {
  return [
    {
      name: '全国',
      newName: '',
      mark: 'region_code',
      dataType: 'DistrictAry',
      oldData: [],
      visible: false,
      isHeight: false,
    },
    {
      name: '全部行业',
      newName: '',
      mark: 'nic_code',
      dataType: 'EleseicAry',
      oldData: [],
      visible: false,
      isHeight: false,
    },
    {
      name: '注册资本',
      mark: 'register_capital',
      visible: false,
      isHeight: false,
    },
    {
      name: '项目评价',
      mark: 'score',
      visible: false,
      isHeight: false,
    },
  ].filter(item => {
    if (!props.filterOptions) return true
    return props.filterOptions?.includes(item.name)
  })
})

const registerCapitalOptions = [
  { value: '0$100', name: '100万以下' },
  { value: '100$200', name: '100-200万' },
  { value: '500$1000', name: '500-1000万' },
  { value: '1000$5000', name: '1000-5000万' },
  { value: '5000$', name: '5000万以上' },
]

const HIGH_QUALITY = '高质量'
const MIDDLE_QUALITY = '中等质量'
const LOW_QUALITY = '低质量'

const evaluateOptions = [
  { label: '全部', value: undefined },
  { label: HIGH_QUALITY, value: [70] },
  { label: MIDDLE_QUALITY, value: [40, 70] },
  { label: LOW_QUALITY, value: [, 40] },
]
const activeEvaluate = ref(evaluateOptions[0].label)
const dropRef = ref(null)
const searchParams = ref({})

const rangeValue = ref('')
const customRange = reactive({
  start: undefined,
  end: undefined,
})

const closeAllPop = () => {
  dropRef.value.forEach(i => i.toggle(false))
}

const isCustom = ref(false)
const closed = (item, index) => {
  options.value[index].visible = false
  if (index === 2) {
    const oldValue = searchParams.value['register_capital']
    if (oldValue.length) {
      if (isCustom.value) {
        customRange.start = oldValue[0]
        customRange.end = oldValue[1]
        rangeValue.value = undefined
      } else {
        rangeValue.value = oldValue.join('$')
        customRange.start = undefined
        customRange.end = undefined
      }
    } else {
      customRange.start = undefined
      customRange.end = undefined
      rangeValue.value = ''
    }
  }
}

const opened = (item, index) => {
  options.value[index].visible = true
}

const submitSub = val => {
  if (val.checkedList) {
    const data = handleMultiple(val.checkedList)
      .filter(item => item.status === 'checked')
      .map(i => i.code)
    searchParams.value[val.mark] = data?.length === 1 ? data[0] : data
    const find = options.value.find(item => item.mark === val.mark)
    find.oldData = val.checkedList
    find.newName = getNameFromPop(val?.checkedList || [])
    emits('submit', searchParams.value)
  }

  closeAllPop()
}

const getregisterCapitalParam = (start, end) => {
  if (start && end) return [start, end]
  if (start && !end) return [start]
  // eslint-disable-next-line no-sparse-arrays
  if (!start && end) return [, end]
  return []
}

const handleSearch = () => {
  let value = {}
  if (rangeValue.value) {
    const values = rangeValue.value.split('$')
    value.start = values[0]
    value.end = values[1]
    isCustom.value = false
  } else {
    value = customRange
    isCustom.value = true
  }
  searchParams.value['register_capital'] = getregisterCapitalParam(value.start, value.end)
  emits('submit', searchParams.value)
  closeAllPop()
}

const handleChangeScore = item => {
  activeEvaluate.value = item.label
  searchParams.value['score'] = item.value
  closeAllPop()
  emits('submit', searchParams.value)
}
</script>

<style lang="scss" scoped>
.footer {
  width: 100%;

  button {
    width: 340px;
    height: 80px;
    line-height: 80px;
    border: none !important;
    padding: 0;
    font-size: 32px;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 400;
    text-align: CENTER;
    color: #74798c;
    margin: 0 12px 0px;
    background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
    border-radius: 8px;
    &.close {
      background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
      color: #74798c;
    }
    &.submit {
      background: linear-gradient(272deg, #076ee4 0%, #53a2ff 100%) !important;
      color: #fff;
    }
  }
}

:deep(.van-field) {
  background-color: transparent;
  padding: 0 16px;

  .van-field__control {
    background-color: inherit !important;
  }
}

:deep(.activeMenuItem) {
  color: #3e7bfa !important;
  span {
    color: #3e7bfa !important;
  }
}
:deep(.activeMenuItem:after) {
  border-color: transparent transparent currentColor currentColor;
}
:deep(.van-dropdown-item__content) {
  position: absolute;
  max-height: 800px !important;
}
</style>
