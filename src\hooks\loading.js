import { onBeforeMount, onMounted, onBeforeUnmount } from 'vue'
import { useSystemStore } from '@/store/modules/system'
const systemStore = useSystemStore()
export function useLoading() {
  let timer = null
  onBeforeMount(() => {
    systemStore.setLoading(true)
  })
  onMounted(() => {
    timer = setTimeout(() => {
      systemStore.setLoading(false)
    }, 700)
  })
  onBeforeUnmount(() => {
    clearTimeout(timer)
    timer = null
  })
}
