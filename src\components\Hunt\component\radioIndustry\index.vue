<script setup name="RadioA">
import { onBeforeMount, reactive, watch } from 'vue'
import { constant, getData } from '../MultiplecChoice/utils'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  // 上一次选中的数据，用于回填
  oldData: {
    type: String,
  },
  headText: {
    type: String,
    default: '全部',
  },
  childText: {
    type: String,
    default: ' ',
  },
  dataType: {
    type: String,
    default: constant.MDistrictAry,
  },
  fSure: {
    //不要全部 ，有确定-取消按钮的时候
    type: Boolean,
    default: false,
  },
  fSureAndHeadText: {
    //这个是在fsure上再需求华了一层
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['close', 'submit'])
const data = reactive({
  regionList: [], //  地区列表
  checkedList: [], // 存储选中项
  provinceList: [],
  cityList: [],
})
// 处理数据格式
const addLevel = list => {
  if (!list) return []
  let arr = list.map(item => {
    if (item?.children?.length) {
      item.children = item.children.map(i => {
        if (i?.children?.length) {
          i.children = i.children.map(itm => {
            return {
              ...itm,
              // level: 3,
              is_leaf: false,
            }
          })

          !props.fSure &&
            i.children.unshift({
              active: false,
              code: i.code,
              level: '3',
              name: props.childText + i.name,
              parent: '--',
              is_leaf: false,
              isFather: true,
            })
        }
        return {
          ...i,
          // level: 2,
          is_leaf: !!i?.children?.length,
        }
      })

      !props.fSure &&
        item.children.unshift({
          active: false,
          code: item.code,
          level: '3',
          name: props.childText + item.name,
          parent: '--',
          is_leaf: false,
          isFather: true,
        })
    }
    return {
      ...item,
      level: 1,
      is_leaf: !!item?.children?.length,
    }
  })
  return arr
}
// 初始化
const getRegion = async (code = '') => {
  let dataType = props.dataType
  let datas = await getData({ code, dataType, isAll: true })
  // 处理数据
  datas = addLevel(datas)
  let text = props.headText
  if (!props.fSure || props.fSureAndHeadText) {
    datas.unshift({
      active: false,
      code: '1000000',
      level: '1',
      name: text,
      parent: '--',
      is_leaf: true,
    })
  }

  data.regionList = datas
  gsetCityAry()
}
// 获取-设置 区的数组
const gsetAreaAry = cityList => {
  if (cityList.length <= 0) {
    data.cityList = []
    return
  }
  let arr = cityList.filter(item => item.active)
  if (arr.length <= 0) {
    data.cityList = []
    return []
  }
  arr = arr[0]?.children || []
  setTimeout(() => {
    data.cityList = arr
  }, 200)
  return arr
}
// 获取--设置 ：市的孩子数组
const gsetCityAry = () => {
  //每一次设置数据源的时候掉一次
  const { regionList } = data
  // debugger
  // console.log(regionList)
  let arr = regionList.filter(item => item.active)
  if (arr.length <= 0) {
    data.provinceList = []
    gsetAreaAry([])
    return []
  }
  arr = arr[0]?.children || []
  data.provinceList = arr
  gsetAreaAry(arr)
  return arr
}
const findchildren = (list, code) => {
  let items = list.filter(item => {
    if (item.code == code) {
      return item
    } else {
      if (item?.children?.length) {
        return findchildren(item.children, code)
      }
    }
  })
  return items?.length ? items?.[0] : false
}
//获取地区编码的层级关系
const getCityInfo = code => {
  // 这里回显做麻烦了 一开始就应该处理数据 往每个孩子里面push一个父亲加自己code的字符串。
  let arr = []
  let curItem = findchildren(data.regionList, code)
  curItem && arr.push(curItem)
  if (curItem?.children?.length && curItem?.code != code) {
    let towItem = findchildren(curItem.children, code)
    towItem && arr.push(towItem)
    if (towItem?.children?.length && towItem?.code != code) {
      let three = findchildren(towItem.children, code)
      three && arr.push(three)
    }
  }
  return arr
}
//填充数据
const setBackFill = (list, data) => {
  for (let region of list) {
    region.active = false
    for (let item of data) {
      if (region.code == item.code) {
        if (region.isFather) {
          region.active = false
        } else {
          region.active = true
        }
      }
    }
    if (region.children?.length) setBackFill(region.children, data)
  }
  return list
}
// 数据回填
const backFillRegion = () => {
  let { regionList, checkedList, provinceList, cityList } = data
  let oldData = props.oldData
  if (!oldData || oldData == '1000000') {
    // 22.11.15
    regionList.forEach(i => {
      if (i.active) {
        i.active = false
        i?.children?.length &&
          i.children.forEach(itm => {
            if (itm) {
              itm.active = false
              itm?.children?.length && itm.children.forEach(is => (is.active = false))
            }
          })
      }
      if (oldData == '1000000' && i.code == '1000000') {
        i.active = true
      } else {
        i.active = false
      }
    })
    provinceList = []
    cityList = []
    data.regionList = regionList
    data.provinceList = provinceList
    data.cityList = cityList
    props.isSure && submit()
    return
  }
  let arrList = getCityInfo(oldData)
  if (arrList?.length > 0) {
    regionList = setBackFill(regionList, arrList)
  }
  data.checkedList = checkedList
  data.regionList = regionList
  gsetCityAry()
  props.isSure && submit()
}
//设置选中项
const setCheckedList = (regionList, checkedList) => {
  for (let region of regionList) {
    if (region.active) checkedList.push(region)
    if (region.children?.length) setCheckedList(region.children, checkedList)
  }
}
const close = () => {
  emits('close', false)
}
// 将选中数据通过自定义事件传递出去
const submit = () => {
  let { regionList, checkedList } = data
  setCheckedList(regionList, (checkedList = []))
  emits('submit', checkedList[checkedList.length - 1])
}
// 单个点击
const getChildData = obj => {
  let { code, level, is_leaf } = obj // active：当前点击节点的状态
  let { regionList } = data
  regionList = regionList.length
    ? regionList.map(region => {
        if (level == '1') {
          region.active = false
          if (region.code === code) region.active = true
        } else {
          // 第二级
          region.children = !region?.children?.length
            ? []
            : region.children.map(city => {
                city.active = false
                if (city.code === code)
                  if (city.isFather) {
                    city.active = false
                  } else {
                    city.active = true
                  }
                if (city?.children?.length) {
                  city.children = city.children.map(item => {
                    item.active = false
                    return item
                  })
                }
                return city
              })
        }
        return region
      })
    : []
  // 特殊判断 --香港这个
  if (!is_leaf && level == '1' && code != '1000000') {
    let obj
    regionList.forEach(item => {
      if (item.code == code) {
        obj = item
        item.active = true
      }
    })
    emits('submit', [obj])
    data.regionList = regionList
    gsetCityAry()
    return
  }
  data.regionList = regionList
  gsetCityAry()
  if (code === '1000000') {
    emits('submit', [data.regionList[0]])
    return
  }

  if (level === '3' || !is_leaf) {
    submit()
    return
  }

  if (props.fSure) {
    let temObj = {}
    if (!obj?.children?.length) {
      emits('submit', temObj)
      return
    }
    let arr = obj?.children?.filter(i => i.active)
    if (level == '1') {
      // 处理第一层逻辑
      temObj =
        arr.find(item => item.children && item.children.length && item.children.some(child => child.active)) ||
        arr[0] ||
        obj
    } else if (level == '2') {
      // 处理第二层逻辑
      temObj = arr.find(item => item.active) || obj
    }
    // console.log('temObj', temObj)
    emits('submit', temObj)
  }
}
const getArea = obj => {
  const { code, level } = obj
  let { regionList } = data
  if (level != 3) {
    close()
    throw '数据错误'
  }
  regionList = regionList.map(region => {
    region.children = region?.children?.length
      ? region.children.map(city => {
          if (city.children) {
            city.children = city.children.map(area => {
              area.active = false
              if (area.code == code) {
                if (area.isFather) {
                  area.active = false
                } else {
                  area.active = true
                }
              }
              return area
            })
          }
          return city
        })
      : []
    return region
  })
  data.regionList = regionList
  submit()
}
onBeforeMount(() => {
  getRegion()
})
watch(
  () => [props.visible, props.oldData],
  ([bl, old]) => {
    if (!bl || !old) return
    // console.log('props.oldData', props.oldData)
    setTimeout(() => {
      backFillRegion()
    }, 200)
  },
  {
    immediate: true,
  }
)

defineExpose({ getRegion })
</script>

<template>
  <div class="region-wrap" @touchmove="_disabledPenetrate" @click="_disabledPenetrate" v-if="data.regionList.length">
    <!-- 省 -->
    <div class="list">
      <Scroll>
        <div
          class="item items"
          v-for="province in data.regionList"
          :key="province.code"
          :class="{ actived: province.active }"
          @click="getChildData(province)"
        >
          <span>{{ province.name }}</span>
        </div>
      </Scroll>
    </div>
    <!-- 市-->
    <div class="list city-wrap">
      <Scroll v-if="data.provinceList.length">
        <div
          class="item"
          v-for="city in data.provinceList"
          :key="city.code"
          :class="{ actived: city.active }"
          @click="getChildData(city)"
        >
          <span>{{ city.name }}</span>
        </div>
      </Scroll>
    </div>
    <!-- 区  -->
    <div class="list area-wrap">
      <Scroll v-if="data.cityList.length">
        <div
          class="item"
          :class="{ actived: area.active }"
          v-for="(area, index) in data.cityList"
          @click="getArea(area)"
          :key="index"
        >
          <span>{{ area.name }}</span>
        </div>
      </Scroll>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.region-wrap {
  width: 100%;
  display: flex;
  color: #20263a;
  font-size: 27px;
  position: relative;

  &::before {
    content: ' ';
    width: 100%;
    height: 2px;
    background: #eeeeee;
    position: absolute;
    top: 0;
    transform: scaleY(0.5);
  }

  .province-wrap {
    height: 600px;
    width: 200px;
    background: #f7f7f7;
    /* border: 1px solid pink; */
  }

  .city-wrap,
  .area-wrap {
    width: 280px;
    height: 600px;
    /* border-right: 1px solid #eeeeee; */
    position: relative;

    &::before {
      content: ' ';
      height: 200%;
      width: 1px;
      background: #eeeeee;
      position: absolute;
      top: -50%;
      transform: scaleX(0.5);
    }
  }
  .list {
    height: 600px;
    overflow: hidden;
  }

  /* 每个item */
  .list .item {
    width: 100%;
    display: flex;
    align-items: center;
    /* justify-content: space-between; */
    /* padding: 20px 92px 20px 104px; */
    padding: 24px 24px 24px 20px;
    font-size: 28px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    color: #20263a;

    &.items {
      padding: 24px 24px 24px 20px;
      text-align: left;
    }
  }
}
.actived {
  /* background: #e7f1fd; */
  span {
    // font-weight: 600;
    color: #076ee4 !important;
  }
}
</style>
