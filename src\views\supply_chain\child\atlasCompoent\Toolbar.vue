<template>
  <div class="toolbar">
    <slot></slot>
    <!-- @click.stop="item.func(item)" -->
    <div
      :class="['item', { active: item.active }, 'cursor']"
      v-for="item in data.toolbarItems"
      :key="item.name"
      @touchstart.stop="onStart(item)"
      @touchend.stop="onEnd(item)"
      @touchcancel.stop="onEnd(item)"
    >
      <svg-icon :name="item.active ? 'a-' + item.icon : item.icon" class="ico"></svg-icon>
      <span class="text">{{ item.text }}</span>
    </div>
  </div>
</template>
<script setup>
import { reactive, watch, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import { backHandler } from '@/utils'
const emit = defineEmits(['refresh'])
const Router = useRouter()
const listAry = [
  // {
  //   text: '下载',
  //   icon: 'download',
  //   func: download,
  // },
  {
    text: '刷新',
    icon: 'refresh',
    func: () => {
      emit('refresh')
    },
  },
]
// eslint-disable-next-line no-undef
const props = defineProps({
  // 额外工具项
  extraItems: {
    type: Array,
    default: () => [],
  },
  // 需要下载的节点class
  downloadClass: {
    type: String,
  },
  // 下载生成的图片名称
  downloadName: {
    type: String,
  },
  // 下载需要忽略的节点class集合
  ignoreClasses: {
    type: Array,
    default: () => [],
  },
})
// 下载---这里在小程序里面有问题 思路就是判断宿主环境，然后微信断需要将数据传给微信，然后叫微信下  https://blog.csdn.net/qq_37622750/article/details/120996043 后面来改
// eslint-disable-next-line no-unused-vars
const download = () => {
  if (!window.html2canvas) return
  const { downloadClass, downloadName, ignoreClasses } = props
  const dom = document.querySelector(`.${downloadClass}`)
  window
    .html2canvas(dom, {
      // 隐藏筛选的节点
      ignoreElements: element => {
        if (ignoreClasses.includes(element.className)) return true
      },
    })
    .then(canvas => {
      let dataURL = canvas.toDataURL('image/png')
      let a = document.createElement('a') // 生成一个a元素
      let event = new MouseEvent('click') // 创建一个单击事件
      a.download = downloadName // 设置图片名称
      a.href = dataURL // 将生成的URL设置为a.href属性
      a.dispatchEvent(event) // 触发a的单击事件
    })
}
const data = reactive({
  toolbarItems: [],
})
const onStart = item => {
  data.toolbarItems.forEach(i => {
    i.active = false
    if (i.icon == item.icon) {
      i.active = true
    }
  })
  // console.log('点击刷新', item)
  if (item?.func) {
    item.func(item)
    setTimeout(() => {
      item.active = false
    }, 100)
  }
}
const onEnd = () => {
  data.toolbarItems.forEach(i => {
    i.active = false
  })
}

watch(
  () => props.extraItems,
  async val => {
    // if (!val.length) return;
    let arr = []
    arr = [...val, ...listAry]
    arr.push({
      text: '退出',
      icon: 'tback',
      func: async () => {
        backHandler()
        console.log('点击退出')
      },
    })
    // }
    data.toolbarItems = arr
  },
  {
    immediate: true,
  }
)
</script>
<style lang="scss" scoped>
.toolbar {
  position: fixed;
  bottom: 50px; /* 12px * 1.2 = 14.4px */
  right: 24px; /* 7.5px * 1.2 = 9px */
  background-color: transparent;
  .item {
    width: 100px; /* 67.5px * 1.2 = 81px */
    height: 50px; /* 31.5px * 1.2 = 37.8px */
    background: #fefefe;
    box-shadow: 0px 0px 9.6px 0px rgba(32, 38, 58, 0.15); /* 8px * 1.2 = 9.6px */
    border-radius: 7.2px; /* 6px * 1.2 = 7.2px */
    opacity: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #3d3d47;
    position: relative;
    cursor: pointer;
    margin-top: 15.6px; /* 13px * 1.2 = 15.6px */
    padding: 0 12.6px; /* 10.5px * 1.2 = 12.6px */
    font-size: 20px;
    .ico {
      width: 24px; /* 20px * 1.2 = 24px */
      height: 24px; /* 20px * 1.2 = 24px */
      margin-bottom: 2.4px; /* 2px * 1.2 = 2.4px */
      transform: rotate(-90deg);
    }
    .text {
      font-size: 20px; /* 12px * 1.2 = 14.4px */
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #076ee4;
    }
    &.active {
      background-color: #076ee4;
      .text {
        color: #fff;
      }
    }
  }
}
</style>
