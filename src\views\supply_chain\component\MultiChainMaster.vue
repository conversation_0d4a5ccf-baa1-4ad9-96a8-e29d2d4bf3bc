<template>
  <div class="w-[100vw]">
    <!--  -->
    <div class="bg-white p-24">
      <div class="f-y-center justify-between">
        <div class="text-#20263A text-32 font-600">目标企业</div>
        <img
          src="@/assets/image/edit.png"
          alt=""
          class="w-36 h-36"
          @click="onClick('del')"
          v-show="!data.isDelShow && multiList.length"
        />
      </div>
      <div class="text-24 text-[#FD9331] mt-12">可最多添加10家企业，查询企业工业供应链上下游企业</div>
    </div>
    <!-- 内容部分 -->
    <div class="w-full px-24" ref="contRef">
      <van-checkbox-group v-model="data.checked" shape="square">
        <div v-for="(item, index) in data.list" :key="item.id">
          <div class="mt-32 text-[#74798C] text-28">目标企业{{ index + 1 }}</div>
          <div class="w-full h-88 f-y-center mt-12">
            <div class="f-y-center flex-1 bg-white h-full px-24">
              <img src="@/assets/image/cicrl.png" class="w-20 h-20 mr-10 flex-shrink-0" />
              <div class="sle flex-1 text-#20263A font-600 text-28 max-w-[530px]">{{ item.ent_name }}</div>
            </div>
            <div class="flex-shrink-0 ml-16 w-96 h-88 f-all-center bg-white" v-if="data.isDelShow">
              <!-- <img src="@/assets/image/delete.png" class="w-36 h-36" /> -->
              <van-checkbox :name="item.ent_id" class="!text-[24px]"></van-checkbox>
            </div>
          </div>
        </div>
      </van-checkbox-group>
      <div v-if="multiList.length < 1">
        <div class="mt-32 text-[#74798C] text-28">目标企业1</div>
        <div class="w-full h-88 f-y-center mt-12">
          <div class="f-y-center flex-1 bg-white h-full px-24">
            <img src="@/assets/image/cicrl.png" class="w-20 h-20 mr-10 flex-shrink-0" />
            <div class="sle flex-1 text-[#74798C] text-28 max-w-[530px]" @click="onClick('add')">新增目标企业</div>
          </div>
        </div>
      </div>
      <div v-if="multiList.length < 2">
        <div class="mt-32 text-[#74798C] text-28">目标企业2</div>
        <div class="w-full h-88 f-y-center mt-12">
          <div class="f-y-center flex-1 bg-white h-full px-24">
            <img src="@/assets/image/cicrl.png" class="w-20 h-20 mr-10 flex-shrink-0" />
            <div class="sle flex-1 text-[#74798C] text-28 max-w-[530px]" @click="onClick('add')">新增目标企业</div>
          </div>
        </div>
      </div>
      <!-- <div class="noData w-full h-full" v-else>
        <img src="@/assets/image/null.png" alt="" class="w-230 h-230" />
        <div class="text-32 text-#20263A mt-32">暂无数据</div>
        <div class="text-[#74798C] text-28 mt-8">请添加目标企业查看供应链</div>
      </div> -->
    </div>
    <!--  -->
    <div class="btn" ref="btnRef">
      <div class="bg-confirm mr-11 text-white" @click="onClick(data.isDelShow ? 'cancel' : 'add')">
        {{ data.isDelShow ? '取消' : '新增目标企业' }}
      </div>
      <div
        class="ml-11 bg-confirm text-white"
        :class="[isGreaterThanTwo || data.isDelShow ? 'opacity-100' : 'opacity-50']"
        @click="onClick(data.isDelShow ? 'delete' : 'goDetail')"
      >
        {{ data.isDelShow ? '删除' : '查看供应链' }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { useMultiList } from '../hook/multiListHook.js'
import { pageChangeHandler } from '@/utils'
const { removeItems, isGreaterThanTwo, multiList, isTenItem } = useMultiList()
const router = useRouter()
const route = useRoute()
const contRef = ref(null)
const btnRef = ref(null)
const { proxy } = getCurrentInstance()

const data = reactive({
  ent_id: null,
  list: [],
  checked: [],
  isDelShow: false,
})
const { height: winHeight } = useWindowSize()

const handH = () => {
  const { top: gylHeight } = useElementBounding(contRef)
  const { height: btnHeight } = useElementBounding(btnRef)

  // 计算剩余高度
  const remainingHeight = winHeight.value - gylHeight.value - btnHeight.value - 20

  // 设置局部滚动区域高度
  contRef.value.style.height = `${remainingHeight}px`

  // 移除滚动条
  contRef.value.style.overflowY = 'auto'
}
const onClick = type => {
  switch (type) {
    case 'add': // 新增目标企业
      if (isTenItem.value) {
        proxy.$showToast('最多添加10个企业!')
      } else {
        pageChangeHandler('/supply-search', { name: '新增目标企业' })
      }
      break
    case 'del': // 删除目标企业
      data.isDelShow = true
      break
    case 'cancel': // 取消
      data.isDelShow = false
      data.checked = []
      break
    case 'delete': // 删除目标企业
      removeItems(data.checked)
      data.isDelShow = false
      break
    case 'goDetail': // 查看供应链
      if (!isGreaterThanTwo.value) return
      pageChangeHandler('/supply-multiDetail', {
        ent_id: encodeURIComponent(multiList.value.map(i => i.ent_id).join(',')),
        isMulti: true,
        title: encodeURIComponent(multiList.value.map(i => i.ent_name).join(',')),
      })

      break
    default:
      break
  }
}
onMounted(() => {
  const { ent_id } = route.query
  data.ent_id = ent_id
  data.list = multiList.value
  nextTick(() => {
    handH()
  })
})
</script>

<style lang="scss" scoped>
.btn {
  position: fixed;
  bottom: 0;
  display: flex;
  width: 100%;
  padding: 24px 24px calc(env(safe-area-inset-bottom) + 40px);
  flex-shrink: 0;
  background: white;
  height: auto;
  border-radius: 0 !important;

  div {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;
    width: 340px;
    font-size: 32px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #20263a;
    border-radius: 8px;
    overflow: hidden;
  }
}
</style>
