<template>
  <div class="w-full h-full flex flex-col bg-[#f7f7f7]">
    <div class="flex-shrink-0 bg-white relative">
      <!--  -->
      <div class="h-[28px] flex justify-center items-end">
        <div class="h-[8px] w-[200px] bg-[#dedede]"></div>
      </div>
      <div
        class="h-[88px] flex items-center text-[28px] text-#74798C pl-[24px]"
        :class="[isPage ? 'justify-start' : 'justify-center']"
      >
        附近找到<u class="text-#3E7BFA px-[8px] font-semibold">{{ total }}</u
        >家相关园区
      </div>
      <!--  -->
      <div class="h-[1px] bg-#eee w-full"></div>
      <!-- 盖一层用来判断拖拽  id就是外面用来判断的-->
      <div
        @click="emits('clickItem')"
        class="absolute w-full h-full z-1 left-0 top-0 bg-transparent"
        id="tuoZhaui"
      ></div>
    </div>
    <!-- 列表 -->
    <div class="h-full overflow-y-scroll scroll-smooth" style="scrollbar-width: none; -ms-overflow-style: none">
      <PullDownList
        ref="pullListRef"
        apiUrl="/hdzhzs/business_admin/v0.1/maps/park/zi_ding_yi"
        :queryParams="queryParams"
        :requestAuto="false"
        requestType="page_index"
      >
        <template #data="{ sourceData }">
          <!--根据传来的距离 动态计算出距离 -->
          <div v-for="item in sourceData" :key="item.id">
            <div
              class="flex items-center justify-between px-[24px] py-[24px] bg-white border-b-[1px] border-[#eee]"
              @click="clickItem(item)"
            >
              <div class="w-[670px]">
                <div class="text-#20263a text-[32px] mb-[16px] sle">{{ item.pname }}</div>
                <div class="text-#74798C text-[28px] sle">入园企业：{{ item.ent_num || 0 }}家</div>
              </div>
<!--              <img src="@/assets/img/form/arrow-right.png" class="w-[32px] h-[32px]" />-->
            </div>
          </div>
        </template>
      </PullDownList>
    </div>
  </div>
</template>

<script setup>
import PullDownList from '@/components/PullDownList/index.vue'
import EntCard from '@/components/EntCard/index.vue'
import { openReport } from '@/utils'
import { pageChangeHandler } from '@/utils'

const emits = defineEmits(['clickItem'])
const props = defineProps({
  isPage: {
    type: Boolean,
    default: true,
  },
  params: {
    type: Object,
    default: () => ({}),
  },
  isHidden: {
    type: Boolean,
    default: true,
  },
})

const router = useRouter()
const route = useRoute()
const queryParams = ref({})
const pullListRef = ref(null)
const total = ref(0)
const clickItem = item => {
  const { mid, pname } = item
  const { lat, lon } = queryParams.value.grids[0]
  pageChangeHandler('/excavate/list', { mid, lat, lon, name: pname })
}
watch(
  () => props.params,
  val => {
    if (!Object.keys(val).length) return
    queryParams.value = val
    nextTick(() => {
      pullListRef.value?.onRefreshFn()
    })
  },
  {
    deep: true,
    immediate: true,
  }
)
watch(
  () => pullListRef.value?.total,
  val => {
    total.value = val || 0
  }
)
onMounted(() => {
  // 获取当前位置
})
</script>

<style lang="scss" scoped>
:deep(.noData) {
  justify-content: flex-start;
  background: white !important;
  height: 100% !important;
  padding-top: 60px;
}
</style>
