<template>
  <div class="bg-#F7F7F7">
    <div class="w-full border-b border-solid border-[#eee] bg-white justify-between px-24">
      <VanbTab :list="['列表展示', '图谱展示']" :before-change="beforeChange" />
    </div>
  </div>
  <div><ListOfBusinesses /></div>
</template>

<script setup name="SupplyMultiDetail">
import VanbTab from '@/components/Van/VanbTab/index.vue'
import ListOfBusinesses from '@/views/advance_search/ListOfBusinesses.vue'
import { pageChangeHandler } from '@/utils'

const router = useRouter()
const route = useRoute()
const data = reactive({
  ent_id: null,
})
const beforeChange = idx => {
  if (idx === 1) {
    // 跳转图谱
    pageChangeHandler('/supply-atlas', { ent_id: data.ent_id, title: data.title, isSingle: false })
  }
  return false
}
onMounted(() => {
  const { ent_id, title } = route.query
  data.ent_id = ent_id
  data.title = title
})
</script>

<style lang="scss" scoped></style>
