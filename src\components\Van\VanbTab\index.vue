<template>
  <div>
    <van-tabs v-bind="$attrs" :class="['self-tab', { 'self-tab-blue': theme }]" @click-tab="onClickTab">
      <van-tab :title="item" :name="index" :key="index" v-for="(item, index) in list"> </van-tab>
    </van-tabs>
  </div>
</template>
<script setup>
import { watch } from 'vue'

const emits = defineEmits(['handleClick'])
const props = defineProps({
  list: {
    type: Array,
    default: () => [],
  },
  // 颜色主题
  theme: {
    type: String,
    default: 'true',
  },
})

function onClickTab({ name }) {
  emits('handleClick', name)
}
</script>
<style></style>
