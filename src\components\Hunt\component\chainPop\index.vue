<script setup>
import VanBPop from '../../../Van/VanbPop/index.vue'
import { chainNewAll, getFixChian } from '@/api/advanceSearch/index.js'

import { onBeforeMount, reactive, getCurrentInstance, watch } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  oldData: {
    type: Array,
  },
  mark: {
    type: String,
  },
  special: {
    type: Boolean,
    default: false, // 这个项目都要特殊处理
  },
})
const emits = defineEmits(['close', 'submit'])
const { proxy } = getCurrentInstance()

const data = reactive({
  visible: false,
  activeChildList: [], // 处于活动状态的子级列表
  sourceData: [],
})
/**
 * 获取数据
 */
const getAllChainData = async () => {
  //这个项目产业 是特殊需求
  let allChainData = localStorage.getItem('fixChainPop')

  // let allChainData = localStorage.getItem(props.special ? 'fixChainPop' : 'allChainDatas')
  if (!allChainData) {
    // let [, res] = await proxy.$to(props.special ? 'getFixChian' : 'chainAll')
    let res = await getFixChian()
    // console.log('ddd', res)

    res.unshift({
      MultipleCelectionSingleSelection: true,
      belong_to: '',
      chain_code: 'ALL',
      children: [],
      code: 'ALL',
      level: '1',
      name: '全部',
    })
    allChainData = res.map(chain => {
      const name = chain?.name || chain?.chain_name
      chain.code = chain.chain_code
      chain.MultipleCelectionSingleSelection = true
      chain.name = name
      chain.children = chain.children.map(child => {
        child.code = child.chain_code
        child.MultipleCelectionSingleSelection = true
        child.name = child.name || child.chain_name
        return child
      })
      chain.children.unshift({
        name: name == '全部' ? name : '全部' + name, //全部,
        code: chain.code,
        parent_code: chain.code,
        MultipleCelectionSingleSelection: true,
      })
      return chain
    })
    // localStorage.setItem(props.special ? 'fixChainPop' : 'allChainDatas', JSON.stringify(allChainData))
    localStorage.setItem('fixChainPop', JSON.stringify(allChainData))
  } else {
    allChainData = JSON.parse(allChainData)
  }
  data.sourceData = allChainData
}
/**
 * 选中切换
 */
const handleChange = (select, parentind) => {
  let { sourceData, activeChildList } = data
  const updateActiveStatus = (list, code) =>
    list.map(item => {
      item.active = item.code === code
      return item
    })

  if (parentind?.toString()) {
    sourceData = updateActiveStatus(sourceData, select.code)
    activeChildList = JSON.parse(JSON.stringify(sourceData.find(item => item.active).children))
    activeChildList[0].active = true
    data.sourceData = sourceData
    data.activeChildList = activeChildList
  } else {
    data.activeChildList = updateActiveStatus(activeChildList, select.code)
  }
}
/**
 *设置默认选中
 */
const setDefaultSelect = (code, paramsData) => {
  for (let item of paramsData) {
    if (item.code === code) {
      const { sourceData } = data
      item.active = true
      const parentInd = sourceData.findIndex(val => val.code === item.parent_code).toString()

      if (parentInd !== '-1') {
        sourceData[parentInd].active = true
        data.sourceData = sourceData
        data.activeChildList = JSON.parse(JSON.stringify(paramsData))
      } else {
        const tempArr = JSON.parse(JSON.stringify(item.children))
        tempArr[0].active = true
        data.sourceData = paramsData
        data.activeChildList = tempArr
      }
    } else {
      item.active = false
      item.children && setDefaultSelect(code, item.children)
    }
  }
}
/**
 * 重置所有状态
 */
const resetAllStates = (source, callback) => {
  if (!source || !source.length) return
  source.forEach(item => {
    item.active = false
    if (item.children) {
      item.children.forEach(itm => (itm.active = false))
    }
  })
  callback && callback()
}
/**
 * 弹窗回调
 */
const submit = () => {
  data.visible = false
  let arr = data.activeChildList.filter(item => item.active)
  arr = arr.filter(i => i.code != 'ALL')
  emits('submit', {
    checkedList: arr,
    mark: props.mark,
  })
}
/**
 * 关闭弹窗
 */
const close = () => {
  data.visible = false
  emits('close', {
    checkedList: data.oldData,
    mark: data.mark,
    visible: false,
  })
}
const echoCheck = (arr = []) => {
  const arrCode = arr.map(i => i.code)
  isSingleData.list.forEach(i => {
    i.active = false
    i.active = arrCode.includes(i.code)
  })
}
watch(
  () => props.visible,
  val => {
    data.visible = val
    if (!val) return

    if (!props.special) {
      // 处理多选的情况
      echoCheck(props.oldData || [])
      return
    }
    let { sourceData } = data
    resetAllStates(sourceData, () => {
      setDefaultSelect(props.oldData[0]?.code || 'ALL', sourceData)
    })
  },
  {
    immediate: true,
  }
)
//生命周期
onBeforeMount(() => {
  if (props.special) {
    getAllChainData()
  }
})

// ---------------------------------- 多选逻辑
const isSingleData = reactive({
  isNoData: false,
  list: [],
})
const getSingleData = async () => {
  const res = await chainNewAll()
  console.log('222', res)

  if (!res.length) {
    // 缺省页面
    isSingleData.isNoData = true
    return
  }
  isSingleData.isNoData = false
  isSingleData.list = res
    .map(item => item.children)
    .flat()
    .map(item => ({
      name: item.name,
      code: item.chain_code,
      active: false,
      MultipleCelectionSingleSelection: 'isSingles',
      level: '1',
      children: [],
    }))
}
const handleCheck = item => {
  // 当前最多选5个
  const len = isSingleData.list.filter(i => i.active).length
  if (len >= 5 && !item.active) {
    proxy.$showToast('最多只能选择5个产业链!')
    return
  }

  isSingleData.list.forEach(i => {
    if (i.code === item.code) {
      i.active = !i.active
    }
  })
}

const submitSingle = () => {
  const checkedList = isSingleData.list.filter(i => i.active)
  emits('submit', {
    checkedList: toRaw(checkedList),
    mark: props.mark,
  })
}
onBeforeMount(() => {
  if (!props.special) {
    getSingleData()
  }
})
</script>
<template>
  <!-- 单选 -->
  <VanBPop @submit="submit" @close="close" :visible="data.visible" v-if="special">
    <div class="chain-wrap">
      <!-- 父级列表 -->
      <div class="parent">
        <div
          v-for="(item, index) in data.sourceData"
          :key="index"
          @click="handleChange(item, index)"
          :class="{ item: true, active: item.active }"
        >
          <div class="name text-ellipsis">{{ item.name }}</div>
        </div>
      </div>
      <!-- 子级列表 -->
      <div class="children">
        <div
          v-for="(child, childIndex) in data.activeChildList"
          :key="childIndex"
          @click="handleChange(child)"
          :class="{ item: true, 'text-ellipsis': true, active: child.active }"
        >
          {{ child.name }}
        </div>
      </div>
    </div>
  </VanBPop>
  <!-- 针对这个项目企业多选 -->
  <VanBPop @close="close" :visible="data.visible" v-if="!special" @submit="submitSingle">
    <div class="parent !w-full" v-if="!isSingleData.isNoData">
      <div
        v-for="(item, index) in isSingleData.list"
        :key="index"
        @click="handleCheck(item)"
        class="flex items-center justify-between px-24"
        :class="{ item: true, active: item.active }"
      >
        <div class="name text-ellipsis !w-400">{{ item.name }}</div>
        <img src="@/assets/image/gou.png" class="w-[20px] h-[20px] ml-[10px]" v-if="item.active" />
      </div>
    </div>
    <div v-else>
      <div class="noData w-full h-full">
        <img src="@/assets/image/null.png" alt="" class="w-230 h-230" />
        <div class="text-32 text-secondary mt-32">暂无数据</div>
      </div>
    </div>
  </VanBPop>
</template>

<style lang="scss" scoped>
.chain-wrap {
  background-color: #fff;
  color: #20263a;
  font-size: 26px;
  position: relative;
  overflow-x: hidden;
}

.parent {
  width: 308px;
  height: 756px;
  background-color: rgba(247, 247, 247, 0.6);
  overflow-y: scroll;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}

.item {
  height: 84px;
  line-height: 84px;
  padding-left: 24px;
  font-size: 26px;
  color: #20263a;
  font-family: PingFang SC-Regular, PingFang SC;
}

.item.active {
  background-color: #fff;
  color: #2a72ff;
  font-weight: 600;
  font-family: PingFang SC-Semibold, PingFang SC;
}

.item .name {
  width: 220px;
}

.text-ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.children {
  height: 756px;
  width: 100%;
  position: absolute;
  left: 308px;
  top: 0;
  overflow-y: scroll;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}
</style>
