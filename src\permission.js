import router from './router'
import { useSettingStore } from './stores/setting'
import { getToken } from './utils/auth'

const whiteList = ['/', '/login']
router.beforeEach((to, from, next) => {
  const settingStore = useSettingStore()

  if (getToken()) {
    to.meta.title && settingStore.setTitle(to.meta.title)
    next()
  } else {
    if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      next('/')
    }
  }
})
