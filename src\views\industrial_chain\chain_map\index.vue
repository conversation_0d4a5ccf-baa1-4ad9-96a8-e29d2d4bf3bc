<template>
  <div class="relative h-full bg-[#F7F7F7] overflow-hidden flex flex-col">
    <div ref="headRef" class="head">
      <!-- 左边产业链下拉框 -->
      <div class="dropdown-container">
        <div
          class="dropdown-trigger"
          @click="toggleChainDropdown"
          :class="{ 'dropdown-actives': data.chainDropdownVisible }"
        >
          <span :class="[data.chainDropdownVisible ? 'text-[#07a6f0]' : '']">{{
            selectedChain.name || '请选择产业链'
          }}</span>
          <svg-icon
            name="arrow-fill"
            class="ml-8 transition-all duration-300 w-20 h-20 text-[#A0A5BA]"
            :class="[data.chainDropdownVisible ? 'rotate-180 text-[#07a6f0]' : '']"
          />
        </div>
      </div>
      <!-- 右边地区下拉框 -->
      <div class="btn_group">
        <div class="region-tab" :class="{ active: data.regionMode === 'nearby' }" @click="switchRegionMode('nearby')">
          周边
        </div>
        <div
          class="region-tab f-all-center"
          :class="{ active: data.regionMode === 'national' }"
          @click="switchRegionMode('national')"
        >
          <span>{{ selectedRegion.name || '全国' }}</span>
          <svg-icon
            v-if="data.regionMode === 'national'"
            name="arrow_d_w"
            class="ml-8 transition-all duration-300 w-20 h-20"
            :class="[data.regionDropdownVisible ? 'rotate-180' : '']"
            @click.stop="toggleRegionDropdown"
          />
        </div>
      </div>
    </div>
    <!-- 下面超出就局部滚动 -->
    <div>
      <!-- 地图 -->
      <div class="w-full h-534">
        <ChainMap></ChainMap>
      </div>
      <!-- 推荐企业名单取前十个 -->
      <div>
        <!-- <EntCard></EntCard> 渲染列表用这个 -->
      </div>
    </div>

    <!-- 产业链下拉框 -->
    <ChainSelection
      :visible="data.chainDropdownVisible"
      :defaultCode="data.selectedChainData.length > 0 ? data.selectedChainData[0].chain_code : ''"
      :headHeight="headHeight"
      @close="handleChainDropdownClose"
      @submit="handleChainSubmit"
    />

    <!-- 地区下拉框 -->
    <RegionSelection
      :visible="data.regionDropdownVisible"
      :defaultCode="data.selectedRegionData.length > 0 ? data.selectedRegionData[0].code : 'All'"
      :headHeight="headHeight"
      @close="handleRegionDropdownClose"
      @submit="handleRegionSubmit"
    />
  </div>
</template>

<script setup>
import ChainMap from './ChainMap.vue'
import ChainSelection from './ChainSelection.vue'
import RegionSelection from './RegionSelection.vue'
import EntCard from '@/components/EntCard/index.vue'
import { hotCount, industryListTen } from '@/api/iChain/index'
import { useDropdowns } from './useDropdowns.js'

const route = useRoute()
const headRef = ref(null)

// 响应式数据
const mapData = reactive({
  heatMapData: [], // 热力图数据
  areaCode: { code: '1000000', level: 1 }, // 地图区域代码
})

const entListData = reactive({
  loading: false,
  list: [], // 企业列表
  total: 0,
})

const headHeight = computed(() => {
  return headRef.value?.offsetHeight || 88
})

// 业务逻辑：获取地图热力数据
const getMapHot = async selectionData => {
  console.log('地图数据更新:', selectionData)

  // 构建请求参数
  const params = {
    areas: [],
    chain_codes: [],
  }

  // 处理产业链数据
  if (selectionData.chainData && selectionData.chainData.length > 0) {
    params.chain_codes = selectionData.chainData.map(item => item.chain_code)
  }

  // 处理地区数据
  if (selectionData.mode === 'national' && selectionData.regionData && selectionData.regionData.length > 0) {
    // 全国模式：使用选中的地区
    params.areas = selectionData.regionData.map(item => item.code).filter(code => code !== 'All')

    // 如果选择的是全国，则不传areas参数或传空数组
    if (selectionData.regionData[0]?.code === 'All') {
      params.areas = []
    }
  } else if (selectionData.mode === 'nearby') {
    // 周边模式：使用当前位置或默认地区
    // 这里可以根据实际需求设置默认地区
    params.areas = ['440300'] // 示例：深圳市
  }

  try {
    // 调用热力图接口
    if (params.chain_codes.length > 0) {
      const response = await hotCount(params)
      if (response && response.data && response.data.datalist) {
        // 转换数据格式为地图组件需要的格式
        mapData.heatMapData = response.data.datalist.map(item => ({
          name: item.region_name,
          value: item.count,
          region_code: item.region_code,
          region_full_title: item.region_full_title,
        }))

        // 更新地图区域代码
        if (params.areas.length > 0) {
          mapData.areaCode = {
            code: params.areas[0],
            level: getAreaLevel(params.areas[0]),
          }
        } else {
          mapData.areaCode = { code: '1000000', level: 1 } // 全国
        }
      }
    }
  } catch (error) {
    console.error('获取地图热力数据失败:', error)
  }

  // 同时获取企业列表数据
  await getEntList(selectionData)
}

// 获取企业列表数据
const getEntList = async selectionData => {
  // 构建请求参数
  const params = {
    areas: [],
    chain_codes: [],
    insight: true,
    level: 2,
    page_index: 1,
    page_size: 10,
    sort: { name: 'REGCAP', order: 'DESC' },
  }

  // 处理产业链数据
  if (selectionData.chainData && selectionData.chainData.length > 0) {
    params.chain_codes = selectionData.chainData.map(item => item.chain_code)
  }

  // 处理地区数据
  if (selectionData.mode === 'national' && selectionData.regionData && selectionData.regionData.length > 0) {
    params.areas = selectionData.regionData.map(item => item.code).filter(code => code !== 'All')
    if (selectionData.regionData[0]?.code === 'All') {
      params.areas = []
    }
  } else if (selectionData.mode === 'nearby') {
    params.areas = ['440300'] // 示例：深圳市
  }

  try {
    entListData.loading = true
    if (params.chain_codes.length > 0) {
      const response = await industryListTen(params)
      if (response && response.data) {
        entListData.list = response.data.items || []
        entListData.total = response.data.count || 0
      }
    } else {
      entListData.list = []
      entListData.total = 0
    }
  } catch (error) {
    console.error('获取企业列表失败:', error)
    entListData.list = []
    entListData.total = 0
  } finally {
    entListData.loading = false
  }
}

// 根据地区代码获取级别
const getAreaLevel = code => {
  if (!code) return 1
  const codeStr = String(code)
  if (codeStr.endsWith('0000')) return 1 // 省级
  if (codeStr.endsWith('00')) return 2 // 市级
  return 3 // 区级
}

// 使用下拉框 hook
const {
  data,
  selectedChain,
  selectedRegion,
  toggleChainDropdown,
  handleChainDropdownClose,
  handleChainSubmit,
  switchRegionMode,
  toggleRegionDropdown,
  handleRegionDropdownClose,
  handleRegionSubmit,
  initializeData,
} = useDropdowns(getMapHot)

onMounted(() => {
  // 初始化下拉框数据
  initializeData(route.query)
})
//  0,2,4,6
//  0（不按地区分组）
//  2（按地区（省份）分组）
//  4 （按地区（市）分组）
//  6（按地区（区）分组）
</script>

<style lang="scss" scoped>
.head {
  @apply relative f-all-center justify-between h-88 bg-white px-24 border-b border-solid border-[#eee];

  .dropdown-container {
    position: relative;
    height: 40px;

    .dropdown-trigger {
      @apply f-all-center cursor-pointer;
      min-width: 200px;
      font-weight: 400;
      font-size: 28px;
      color: #20263a;
      .svg-left {
        color: red !important;
      }
    }
  }
  .btn_group {
    @apply flex;
    height: 56px;
    background: #f4f4f4;
    border-radius: 8px;
    overflow: hidden;
    font-weight: 400;
    font-size: 24px;
    color: #404040;
    .region-tab {
      min-width: 124px;
      height: 56px;
      padding: 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
        color: #fff;
      }

      &:hover:not(.active) {
        background: #e8e8e8;
      }
    }
  }
}
</style>
