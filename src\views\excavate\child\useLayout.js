import { setPx } from '@/utils/height'

// 拖拽布局+高度相关
const useLayout = () => {
  const filterRef = ref(null) //筛选Ref
  const isHidden = ref(true)
  // 高度相关
  const listMaxH = ref(0) // 列表+园区列表情况 最大高度
  const isShowFour = ref(true) // 拖拽在一定位置  四个入口需要被隐藏
  // 拖拽相关
  const movableViewRef = ref(null)
  let startY = 0
  let initialTop = 0
  const topRange = reactive([300, 400]) // 拖动范围，单位为像素
  const newTop = ref(0)

  const handleTouchStart = event => {
    startY = event.touches[0].clientY
    initialTop = movableViewRef.value.offsetTop
  }

  const handleTouchMove = event => {
    if (event.target.id !== 'tuoZhaui') return

    const deltaY = event.touches[0].clientY - startY
    newTop.value = initialTop + deltaY
    // 这里要分两种情况 一种是有showFour 一种是没有showFour
    if (isShowFour.value) {
      if (newTop.value < topRange[0]) {
        newTop.value = topRange[0] // 限制向上拖动范围
      } else if (newTop.value > topRange[1]) {
        newTop.value = topRange[1] // 限制向下拖动范围
      }
    } else {
      if (newTop.value < setPx(100)) {
        newTop.value = setPx(100) // 限制向上拖动范围
      } else if (newTop.value > topRange[1]) {
        newTop.value = topRange[1] // 限制向下拖动范围
      }
    }
    // console.log(newTop.value)
    movableViewRef.value.style.top = newTop.value + 'px'
  }

  const handleTouchEnd = () => {
    // 拖拽结束
    const Num = 70
    // console.log(newTop.value > 0 && newTop.value < topRange[0])
    setTimeout(() => {
      if (newTop.value > topRange[0] + Num || (newTop.value > 0 && newTop.value < topRange[0])) {
        movableViewRef.value.style.transition = 'top 0.5s ease-in-out'
        movableViewRef.value.style.top = topRange[1] + 'px'
        isShowFour.value = true
        isHidden.value = true
      }
      if (newTop.value < topRange[0] + Num && newTop.value >= topRange[0]) {
        movableViewRef.value.style.transition = 'top 0.5s ease-in-out'
        isShowFour.value = false
        movableViewRef.value.style.top = setPx(100) + 'px'
        isHidden.value = false
      }
    }, 500)
  }

  const clickItem = () => {
    isShowFour.value = !isShowFour.value
    isHidden.value = !isHidden.value
    if (isShowFour.value) {
      movableViewRef.value.style.transition = 'top 0.5s ease-in-out'
      movableViewRef.value.style.top = topRange[1] - 10 + 'px'
      isShowFour.value = true
      isHidden.value = true
    } else {
      movableViewRef.value.style.transition = 'top 0.5s ease-in-out'
      isShowFour.value = false
      movableViewRef.value.style.top = setPx(100) + 'px'
      isHidden.value = false
    }
  }
  const backTopRangeH = () => {
    nextTick(() => {
      movableViewRef.value.style.height = listMaxH.value + 'px'
      movableViewRef.value.style.transition = 'top 0.5s ease-in-out'
      movableViewRef.value.style.top = topRange[1] + 'px'
      isShowFour.value = true
      isHidden.value = true
    })
  }
  nextTick(() => {
    const { height } = useWindowSize()
    const fExcavate = document.getElementById('fExcavate')
    const rect = fExcavate.getBoundingClientRect()
    const topDistance = rect.top
    movableViewRef.value.style.height = height.value - topDistance - 34 + 'px'
  })

  return {
    clickItem,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    movableViewRef,
    isShowFour,
    filterRef,
    isHidden,
    backTopRangeH,
  }
}

export { useLayout }
