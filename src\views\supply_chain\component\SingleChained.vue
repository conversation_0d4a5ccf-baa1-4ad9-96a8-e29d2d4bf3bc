<template>
  <div>
    <!-- 搜索静态 -->
    <div class="relative">
      <Hipt placeholder="输入目标供应链企业名称" style="padding: 10px" disabled />
      <div class="absolute top-10 right-10 left-10 bottom-10" @click.native="onIpt"></div>
    </div>
    <!-- tab -->
    <div class="bg-white"><VanbTab :list="list" v-model:active="curIdx" /></div>
    <!-- 列表 -->
    <div class="">
      <div v-show="curIdx === 0">
        <ListCont :source="data.tjlData" />
      </div>
      <div v-show="curIdx === 1">
        <ListCont :source="data.sslData" />
      </div>
      <div v-show="curIdx === 2">
        <ListCont :source="data.gczData" />
      </div>
    </div>
  </div>
</template>

<script setup>
import Hipt from '@/components/Hipt/index.vue'
import VanbTab from '@/components/Van/VanbTab/index.vue'
import { getRecommended } from '@/api/supplyChain/index.js'
import ListCont from './ListCont.vue'
import { pageChangeHandler } from '@/utils'

const { proxy } = getCurrentInstance()
const router = useRouter()
const list = reactive(['推荐链主企业', '上市链主企业', '高成长性企业'])
const data = reactive({
  tjlData: [],
  sslData: [],
  gczData: [],
})
const curIdx = ref(0)
const onIpt = () => {
  if (!window.sessionStorage.getItem('preset')) {
    proxy.$showToast('请前往设置招商预设！')
    return
  }
  // 跳转搜索页面
  pageChangeHandler('/supply-search')
}
const getList = async () => {
  proxy.$loading('加载中...')
  const params = {
    $count: true,
    $filter: 'recommended_type eq 推荐链主企业',
    $limit: 5,
    $offset: 0,
  }
  const [res1, res2, res3] = await Promise.all([
    getRecommended(params),
    getRecommended({
      ...params,
      $filter: 'recommended_type eq 上市链主企业',
    }),
    getRecommended({
      ...params,
      $filter: 'recommended_type eq 高成长性企业',
    }),
  ])
  proxy.$close()
  data.tjlData = res1.items
  data.sslData = res2.items
  data.gczData = res3.items
}
onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped></style>
