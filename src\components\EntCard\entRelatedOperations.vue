<!-- eslint-disable vue/no-use-v-if-with-v-for -->
<template>
  <div class="ent-related-operations">
    <div class="operations">
      <button @click="handleGetContacts(data)">
        <SvgIcon name="phone" />
        联系方式
      </button>
      <button v-if="data.official_website || data.website" @click="handleClickOfficialWebsite(data)">
        <SvgIcon name="official-website" />
        官网
      </button>
      <button v-if="entAddress && !nearby" @click="handleClickLocation(data)">
        <SvgIcon name="location" />
        地址
      </button>

      <button v-if="entAddress && nearby" @click="handleClickNearby(data)">
        <SvgIcon name="enterprise" />
        附近企业
      </button>
    </div>

    <VanPopup v-model:show="show" position="bottom" @opened="opened" @closed="closed" teleport="#app">
      <div class="overlay-content">
        <VanTabs v-model:active="active">
          <VanTab name="0" :title="`全部 (${contactsList.length})`"></VanTab>
          <VanTab name="1" :title="`手机 (${contactsList.filter(_ => _.type === '1').length})`"></VanTab>
          <VanTab name="2" :title="`固话 (${contactsList.filter(_ => _.type === '2').length})`"></VanTab>
          <VanTab name="3" :title="`邮箱 (${contactsList.filter(_ => _.type === '3').length})`"></VanTab>
        </VanTabs>
        <div class="contacts-container">
          <div
            v-if="currentData?.length"
            v-for="(item, index) in currentData"
            :key="item.contact_data + item.ascription + index"
            class="contact-item"
          >
            <div>
              <p class="name sle">{{ item.name || '匿名' }}</p>
              <span>归属: {{ item.ascription || '-' }}</span>
            </div>

            <div>
              <p class="number">
                <SvgIcon :name="CATACT_TYPES_MAP[item.type]" class="" />
                {{ item.contact_data }}
              </p>
              <span>{{ item.resources }}</span>
            </div>
          </div>

          <div v-else class="empty">
            <img src="@/assets/image/null.png" alt="" />
            <p>暂无联系方式</p>
          </div>
        </div>
      </div>
    </VanPopup>

    <VanDialog
      teleport="#app"
      v-model:show="locationDialogShow"
      :showTitle="false"
      :showConfirmButton="false"
      class="location-dialog"
    >
      <div class="location-dialog-body" @touchmove.prevent.stop>
        <p class="title">地址</p>
        <div class="map">
          <Map :default-address="entAddress"></Map>
        </div>

        <span class="address">{{ entAddress }}</span>

        <div class="btn" @click="copyAddress(entAddress)">复制地址</div>
        <!-- <div class="btn navigation" @click="navigation">导航</div> -->
        <div class="btn" @click="locationDialogShow = false">取消</div>
      </div>
    </VanDialog>

    <!-- <van-popup v-model:show="navigationShow" position="bottom">
      <div class="btn" @click="handleToGaode">高德地图</div>
      <div class="btn" @click="handleToBaidu">百度地图</div>
      <div class="btn" @click="handleToTencent">腾讯地图</div>
    </van-popup> -->
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, getCurrentInstance, computed, defineExpose } from 'vue'
import { getEntContacts } from '@/api/advanceSearch/index'
import Map from '@/components/Map/index.vue'
import { isIOS } from '@/utils/device'
// import dd from 'gdt-jsapi'
import useClipboard from 'vue-clipboard3'
const { toClipboard } = useClipboard()
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  nearby: {
    type: Boolean,
    default: false,
  },
  showContacts: {
    type: Boolean,
    default: undefined,
  }
})

const { proxy } = getCurrentInstance()

const appName = ''

const show = ref(false)
const active = ref('0')
const contactsList = ref([])
const locationDialogShow = ref(false)
const navigationShow = ref(false)

const emits = defineEmits(['visibleChange', 'nearby'])

const currentData = computed(() => {
  return contactsList.value.filter(contact => {
    contact.name = contact.name.trim()
    if (active.value === '0') return true
    return String(active.value) === contact.type
  })
})

// 地址取值
const entAddress = computed(() => {
  return props.data.register_address || props.data.address || props.data.dom
})

const CATACT_TYPES_MAP = {
  1: 'mobile-phone',
  2: 'landline',
  3: 'email',
}

const preCollect = ref(undefined)
const handleGetContacts = data => {
  console.log(data);
  if (!data.ent_id) return proxy.$showToast('未找到联系方式')
  show.value = true

  // if (preCollect.value === data.collect) return
  // preCollect.value = data.collect
  // if (contactsList.value.length) return
  proxy.$loading('加载中...')
  getEntContacts({
    ent_id: data.ent_id,
    collect: props.showContacts ? true : data.collect,
  }).then(res => {
    contactsList.value = res || []
    proxy.$close()
  })
}

// const handleClickOfficialWebsite = item => {
//   const startWith = /^(http|https):\/\/\S*/i
//   // const websitePrefix = /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~\/])+$/
//   if (item.official_website || item.website) {
//     let url = item.official_website || item.website
//     if (!startWith.test(url)) url = 'http://' + url
//     window.open(url, '_self')
//   }
// }

const handleClickOfficialWebsite = async item => {
  const startWith = /^(http|https):\/\/\S*/i;
  let url = item.official_website ?? item.website;
  if (!startWith.test(url)) url = 'http://' + url;
  // 全局环境判断  0：浏览器 1：微信h5 2：微信小程序 3：安卓app
  const ua = window?.navigator?.userAgent?.toLowerCase()
  if (ua?.indexOf('micromessenger') !== -1) {
    // wx.miniProgram.getEnv(async res => { // 异步耗时接口
    //   // if (res.miniprogram) {
    //   //  // 微信小程序环境
    //   // } else {
    //   //   // 微信h5环境
    //   // }
    // })
    await navigator.clipboard.writeText(url);
    proxy.$showToast('由于小程序框架限制，已复制链接地址，请在浏览器粘贴打开');
  }else {
    // 其他（浏览器）环境
    if(url) window.open(url, '_self');
  }
}

const handleClickLocation = () => {
  locationDialogShow.value = true
}

const handleClickNearby = data => {
  emits('nearby', data)
}

const opened = () => {
  emits('visibleChange', true)
}

const closed = () => {
  emits('visibleChange', false)
}

const copyAddress = address => {
  toClipboard(address).then(() => {
    proxy.$showToast('复制成功')
  })
}

/**
 *
 * android @doc https://lbs.amap.com/api/amap-mobile/guide/android/marker
 * ios @doc https://lbs.amap.com/api/amap-mobile/guide/ios/marker
 */
// eslint-disable-next-line no-unused-vars
const handleToGaode = () => {
  let prefix = 'androidamap://' // 默认为安卓设备
  if (isIOS) {
    prefix = 'iosamap://'
  }

  const location = props.data.location

  const link = `${prefix}viewMap?sourceApplication=${appName}&lat=${location.lat}&lon=${location.lon}&poiname=${entAddress.value}&dev=0`
  // window.location.href = link

  // eslint-disable-next-line no-undef
  openLink({
    url: link,
  })
    .then(() => {})
    .catch(() => {
      proxy.$showToast('未检测到高德地图')
    })
  navigationShow.value = false
}

/**
 * android @doc https://lbs.baidu.com/index.php?title=uri/api/android
 * ios @doc https://lbs.baidu.com/index.php?title=uri/api/ios
 */
// eslint-disable-next-line no-unused-vars
const handleToBaidu = () => {
  let prefix = 'bdapp://'
  if (isIOS) prefix = 'baidumap://'
  const location = props.data.location

  const link = `${prefix}map//marker?location=${location.lat},${location.lon}&title=${''}&content=${
    entAddress.value
  }&src=${appName}`

  // window.location.href = link

  navigationShow.value = false

  // eslint-disable-next-line no-undef
  openLink({
    url: link,
  })
    .then(() => {})
    .catch(() => {
      proxy.$showToast('未检测到百度地图')
    })
}

/**
 * @doc https://lbs.qq.com/webApi/uriV1/uriGuide/uriMobileMarker
 */
// eslint-disable-next-line no-unused-vars
const handleToTencent = () => {
  const prefix = 'qqmap://map/marker'
  const location = props.data.location

  const link = `${prefix}?marker=coord:${location.lat},${location.lon};title:${entAddress.value};addr:${entAddress.value}&referer=${appName}`
  // window.location.href = link
  navigationShow.value = false

  // eslint-disable-next-line no-undef
  openLink({
    url: link,
  })
    .then(() => {})
    .catch(() => {
      proxy.$showToast('未检测到腾讯地图')
    })
}

defineExpose({
  openLocationDialog: handleClickLocation,
})
</script>
<style lang="scss">
.location-dialog {
  border-radius: 8px !important;
  width: 600px !important;

  .location-dialog-body {
    background: white !important;
    p {
      text-align: center;
      margin-bottom: 24px;
      color: #20263a;
      font-weight: 500;
      font-size: 34px;
    }

    .address {
      color: #74798c;
      display: block;
      font-size: 32px;
      padding: 0 48px;
      line-height: 44px;
      text-align: center;
      margin: 32px 0;
    }
  }
}
</style>
<style lang="scss" scoped>
.ent-related-operations {
  .operations {
    display: flex;
  }
}

button {
  // background: rgba(7, 110, 228, 0.06);
  border: 1px solid #dedede !important;
  border-radius: 8px;
  padding: 8px 12px;
  margin-right: 16px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24px;
  color: #20263a;
  display: flex;
  border: none;
  &:last-of-type {
    margin-right: 0;
  }

  svg {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }
}

.overlay-content {
  background: #f7f7f7;
  height: 100%;
  overflow: hidden;

  .van-tabs__content {
    max-height: 808px;
    overflow-y: auto;
    background: #f7f7f7;
  }

  :deep(.van-tabs) {
    height: 88px;
    .van-tab__text {
      color: #74798c;
    }
    .van-tab--active {
      .van-tab__text {
        color: #3e7bfa;
      }
    }

    .van-tabs__line {
      background: #3e7bfa;
      width: 40px;
    }
  }
}

.contacts-container {
  height: fit-content;
  padding-top: 24px;
  background: #f7f7f7;
  height: 806px;
  overflow-y: auto;

  .contact-item {
    background: #fff;
    padding: 32px;
    margin-bottom: 20px;
    & > div {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;
      &:last-of-type {
        margin-bottom: 0;
      }

      .name {
        width: 345px;
        color: #20263a;
        font-size: 30px;
        font-weight: 600;
        line-height: 35px;
      }

      .number {
        font-size: 24px;
        color: #20263a;
        display: flex;
        align-items: center;

        svg {
          width: 32px;
          height: 32px;
          margin-right: 8px;
        }
      }

      span {
        color: #74798c;
        font-size: 24px;
        line-height: 28px;
      }
    }
  }
}

.btn {
  width: 100%;
  text-align: center;
  padding: 32px 0;
  font-size: 28px;
  color: #076ee4;
  &.navigation {
    color: #3e7bfa;
    font-weight: 500;
  }
}

.empty {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  padding-top: 100px;
  img {
    width: 406px;
    height: 240px;
  }
  p {
    font-size: 28px;
    color: #74798c;
    margin-top: 24px;
  }
}
</style>
