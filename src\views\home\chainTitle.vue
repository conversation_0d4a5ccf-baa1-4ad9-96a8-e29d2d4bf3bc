<template>
  <van-popover
    v-model:show="showPopover"
    overlay-class="home-selector-overlay-class"
    :show-arrow="false"
    :overlay="true"
    class="w-full home-chain-selector"
  >
    <template #reference>
      <div
        class="flex items-center justify-between h-96 p-24 !z-1000000 relative border-b border-solid border-[#eee] bg-white w-[750px]"
      >
        <div class="flex items-center">
          <SvgIcon name="chain" class="mr-16" />
          <span class="font-semibold text-32">{{ currentIndustry.label }}</span>
        </div>
        <SvgIcon
          name="arrow-down"
          :style="{ transform: `rotate(${showPopover ? 180 : 0}deg)`, transition: 'all 0.3s' }"
        />
      </div>
    </template>

    <div class="w-full overflow-y-auto overlay max-h-600">
      <div
        v-for="item in industriesList"
        :key="item.value"
        class="flex items-center justify-between p-24"
        @click="handleClick(item)"
      >
        <span class="text-primary text-28" :class="[item.value === currentIndustry.value ? 'text-selected' : '']">{{
          item.label
        }}</span>
        <SvgIcon v-if="item.value === currentIndustry.value" name="tick" class="w-32 h-32" />
      </div>
    </div>
  </van-popover>
</template>

<script setup>
import { getSupplementaryData } from '@/api/home/<USER>'
import { defineEmits, onMounted } from 'vue'
import { useAppHeaderHeight } from '@/hooks/useAppHeaderHeight'
import { filterChainMenuLists } from '@/contant/dict.js'
import { useUserStore } from '@/store/modules/user'

const userStore = useUserStore()
const emits = defineEmits(['change'])
const headerHeight = useAppHeaderHeight()
const route = useRoute()

const showPopover = ref(false)
const industriesList = ref([])
const currentIndustry = computed(() => userStore.chain)
// 产业链接口

// getSupplementaryData().then(res => {
//   res &&
//     res.forEach(item => {
//       item.children_nodes.forEach(child => {
//         if (child.available) {
//           industriesList.value.push({
//             value: child.code,
//             label: child.name,
//           })
//         }
//       })
//     })

//   currentIndustry.value = industriesList.value?.[0]
//   emits('change', currentIndustry.value)
// })

onMounted(() => {
  const code = route.query?.code || window.localStorage.getItem('m_s_c')

  // 产业链写死
  industriesList.value = filterChainMenuLists
  currentIndustry.value = filterChainMenuLists[0]

  if (code) {
    // 如果是跳转进来的
    currentIndustry.value = industriesList.value.find(item => item.value === code)
  }
  emits('change', currentIndustry.value)
})

const handleClick = item => {
  // currentIndustry.value = item
  userStore.setChain(item)
  emits('change', item)
  showPopover.value = false
}
</script>
<style lang="scss">
.home-chain-selector {
  // transform: translateY(-6px) !important;
  .van-popover__content {
    border-radius: 0 0 16px 16px;
  }

  .van-popover__wrapper {
    width: 100%;
  }
}

.home-selector-overlay-class {
  top: v-bind(headerHeight) + 96 + 'px' !important;
}
</style>
