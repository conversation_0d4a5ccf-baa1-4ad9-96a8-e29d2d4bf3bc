<template>
  <div class="h-full bg-[#F7F7F7] flex flex-col">
    <TwoNav v-model:curIdx="curIdx" />
    <div class="flex-1 flex">
      <div v-show="curIdx === 'a'" class="flex-1">
        <!-- 单链主招商 -->
        <SingleChained />
      </div>
      <div v-if="curIdx === 'b'">
        <!-- 多链主招商 -->
        <MultiChainMaster />
      </div>
    </div>
  </div>
</template>

<script setup>
import TwoNav from './component/TwoNav.vue'
import SingleChained from './component/SingleChained.vue'
import MultiChainMaster from './component/MultiChainMaster.vue'
import { industrialChainPreset } from '@/api/supplyChain/index.js'

const curIdx = ref('a')

onBeforeMount(async () => {
  window.sessionStorage.getItem('isMulti') === 'true' ? (curIdx.value = 'b') : (curIdx.value = 'a')
  window.sessionStorage.removeItem('isMulti')
  const res = await industrialChainPreset()
  if (res) {
    // 说明有预设
    window.sessionStorage.setItem('preset', true)
  } else {
    window.sessionStorage.removeItem('preset')
  }
})
</script>

<style lang="scss" scoped></style>
