<template>
  <div class="h-auto" @touchmove.prevent.stop>
    <!-- 四个入口 -->
    <div v-if="isShow" class="flex items-center px-[60px] justify-between py-[40px] w-full">
      <div
        v-for="({ name, img, a_img, active }, index) in fourList"
        :key="name"
        @click="onTabFour(name, index)"
        class="flex flex-col items-center"
      >
        <img :src="active ? a_img : img" class="w-[80px] h-[80px] mb-[12px]"  alt=""/>
        <div class="text-[28px] text-#74798C">{{ name }}</div>
      </div>
    </div>
    <div v-if="isShow" class="w-[702px] h-[1px] bg-#eee"></div>

    <!--位置+距离+更多筛选-->
    <div class="relative">
      <van-dropdown-menu active-color="#3E7BFA" :z-index="170" ref="dropRef">
        <van-dropdown-item
          :title="data.area_name"
          title-class="activeMenuItem"
          @open="handleClick('areaOpen')"
          @close="handleClick('areaClose')"
        >
          <template #default>
            <div>
              <RadioIndustryS
                :visible="filterData.visibleArea"
                :oldData="data.area_value"
                @submit="data => handleClick('getArea', data)"
                dataType="DistrictAry"
                fSure
              />
              <div class="flex justify-between px-24 py-10 bg-white">
                <div class="w-[340px] mr-22 cancel-btn" @click="handleClick('areaClose')">取消</div>
                <div class="w-[340px] confirm-btn" @click="handleClick('areaSure')">确定</div>
              </div>
            </div>
          </template>
        </van-dropdown-item>
        <van-dropdown-item :title="data.sliderVal + 'km'" :title-class="data.sliderVal !== 0 ? 'activeMenuItem' : ''">
          <template #default>
            <div class="pb-[24px]">
              <div class="w-full h-[1px] bg-#eee"></div>
              <SliderMap
                v-model:sliderVal="data.sliderVal"
                @change="val => handleClick('sliderVal', val)"
                :address="data.address"
              />
            </div>
          </template>
        </van-dropdown-item>
        <!-- 更多筛选 -->
        <van-dropdown-item
          title="更多筛选"
          :title-class="filterData.hunH ? 'activeMenuItem' : ''"
          @open="handleClick('huntOpen')"
          v-if="filterData.isShowHunt"
        >
          <template #default>
            <div>
              <div>
                <Hunt
                  :asideList="data.asideOptions"
                  height="300px"
                  ref="huntRef"
                  :fixChainPop="true"
                  @submit="val => handleClick('hunt', val)"
                ></Hunt>
              </div>
              <div class="flex justify-between px-24 py-10 bg-white">
                <div class="w-[340px] mr-22 cancel-btn" @click="handleClick('huntClose')">取消</div>
                <div class="w-[340px] confirm-btn" @click="handleClick('huntSure')">确定</div>
              </div>
            </div>
          </template>
        </van-dropdown-item>
      </van-dropdown-menu>
    </div>

    <!-- 地区确定弹窗 -->
    <RevokeL
      className="h_sure_pop"
      :visible="filterData.areaPop"
      @close="filterData.areaPop = false"
      @submit="handleClick('areaPopSure')"
      sureText="确定"
    >
      <div class="pop">
        <div class="tit">提示</div>
        <div class="cont">是否针对 {{ filterData.tempArea.name }} 进行全域企业分析?</div>
      </div>
    </RevokeL>
    <!-- 行业 -->
    <MultipleChoice
      :visible="filterData.industry_visible"
      :oldData="filterData.tempindustry"
      @close="filterData.industry_visible = false"
      @submit="val => handleClick('industrySure', val)"
      dataType="EleseicAry"
      mark="EleseicAry"
    />
    <MultipleChanyeChoice
      :visible="filterData.chanye_visible"
      :oldData="filterData.chain_codes"
      @close="filterData.chanye_visible = false"
      @submit="val => handleClick('chanyeSure', val)"
    />
  </div>
</template>

<script setup name="Excavate">
import cy_s from '@/assets/image/yzwj/cy_s.png'
import hy_s from '@/assets/image/yzwj/hy_s.png'
import lt_s from '@/assets/image/yzwj/lt_s.png'
import yq_s from '@/assets/image/yzwj/yq_s.png'
import cy_a from '@/assets/image/yzwj/cy_a.png'
import hy_a from '@/assets/image/yzwj/hy_a.png'
import lt_a from '@/assets/image/yzwj/lt_a.png'
import yq_a from '@/assets/image/yzwj/yq_a.png'

import RadioIndustryS from '@/components/Hunt/component/radioIndustry/index.vue'
import MultipleChoice from '@/components/Hunt/component/MultiplecChoice/index.vue'
import MultipleChanyeChoice from '@/components/Hunt/component/MultipleChanyeChoice/index.vue'
import RevokeL from '@/components/Van/VanbRevokeL/index.vue'
import SliderMap from './SliderMap.vue'
import { useFilter } from '../hook/useFilter.js'
import Hunt from '@/components/Hunt/index.vue'
import { asideOptionsOne, asideOptionsTwo, asideOptionsThree, asideOptionsChanye } from './filterOptions.js'

const props = defineProps({
  isShowFour: {
    type: Boolean, //是否显示四个入口  -- 这里会发生变化的
    default: true,
  },
})
const emits = defineEmits(['update:modelValue'])

const route = useRoute()
const router = useRouter()

const fourList = ref([
  { name: '行业', img: hy_s, active: false, a_img: hy_a, type: 'industry' },
  { name: '龙头', img: lt_s, active: false, a_img: lt_a, type: 'leading_ent' },
  { name: '园区', img: cy_s, active: false, a_img: cy_a, type: 'park' },
  { name: '产业', img: yq_s, active: false, a_img: yq_a, type: 'chanye' },
])
const isShow = computed(() => props.isShowFour)

// 下拉框弹窗Ref
const dropRef = ref(null)

const data = reactive({
  // 地址范围
  sliderVal: 1,
  grids: [{ lon: '', lat: '' }], // 为数字
  address: '',
  // 位置筛选框
  // 深圳440300
  area_value: '500000', //
  area_name: '重庆市', // 默认
  // 行业 筛选框
  industry_value: [], //默认这个 不选择
  // 产业链  ----从首页获取
  // chain_codes: [],
  // 是否龙头
  leading_ent: false,
  // 更多筛选参数
  asideOptions: asideOptionsOne,
  // 更多筛选数据
  huntData: {},
})
let requestData = ref({}) // 最终请求的数据+是否是园区

// 最终请求逻辑 -过滤筛选条件 - 过滤请求数据 - 处理高亮情况
const getData = ({ type = '', ...rest }) => {
  console.log('getData', type, rest)
  //默认行业,龙头
  let query = {
    grids: data.grids,
    radius: data.sliderVal * 1000 + '',
    shape: 'CIRCLE',
    area_code: data.area_value, //测试 加上这些条件没数据了
    area_name: data.area_name,
    chain_codes: rest?.chain_codes,
    // super_dimension: {}, // 更多筛选里面的字段--空这里也过滤了
    type: type,
    trade_types: data.industry_value,
    // leading_ent: data.leading_ent,
  }
  //清空高级筛选 -  如果当前 type为industry || leading_ent 并且requestData.value 的type为industry 就不清空
  if (['industry', 'leading_ent', 'park', 'chanye'].includes(type)) {
    if (!(['industry', 'leading_ent'].includes(type) && requestData.value?.type === 'industry')) {
      filterData.sureHunt = {}
      filterData.hunH = false
    }
  }
  // 不是更多筛选,地区，距离 一进来数据为{}
  if (!['huntSure', 'area', 'sliderVal'].includes(type)) {
    requestData.value = {}
  } else {
    // 如果开始进来没有点四个tab默认行业和龙头 -- 这三个筛选条件直接加
    if (!Object.keys(requestData.value)?.length) {
      requestData.value = { ...query }
    }
  }
  if (requestData.value?.isArea) {
    delete requestData.value?.isArea
  }
  filterData.isShowHunt = true
  switch (type) {
    case 'industry':
      fourList.value[2].active = false
      fourList.value[3].active = false
      fourList.value[0].active = false
      data.park = false
      if (data.industry_value?.length) {
        fourList.value[0].active = true
      }
      Object.assign(requestData.value, query)
      data.asideOptions = asideOptionsOne
      break
    case 'area':
      // 地区和协会互斥
      if (requestData.value.type === 'release') {
        fourList.value[3].active = false
        delete requestData.value.ent_name
        // release.value.type = 'industry'
      }
      Object.assign(requestData.value, { area_code: data.area_value, area_name: data.area_name, isArea: true })
      break
    case 'leading_ent':
      data.leading_ent = !fourList.value[1].active
      fourList.value[1].active = !fourList.value[1].active
      fourList.value[2].active = false
      // 保留产业选择
      // fourList.value[3].active = false
      if (data.leading_ent) {
        // 这种写法 为false就不传
        query['leading_ent'] = data.leading_ent
      }
      Object.assign(requestData.value, query)
      data.asideOptions = asideOptionsOne
      break
    case 'park':
      const park = fourList.value[2].active
      fourList.value[0].active = false
      fourList.value[1].active = false
      fourList.value[3].active = false
      if (!park) {
        filterData.isShowHunt = false // 没得更多筛选
        fourList.value[2].active = !park
        // ps pc这里一开始area_code, area_name没带进去 选择地区后点园区就有了
        const { grids, radius, shape, area_code, area_name } = query
        Object.assign(requestData.value, {
          grids,
          radius,
          shape,
          address: data.address,
          area_code,
          area_name,
          type: 'park',
        })
        break
      } else {
        fourList.value[2].active = !park
        // 取消园区：显示更多筛选  - 默认发请求 -pc 这里取消没有发请求 卧槽
        Object.assign(requestData.value, query)
        data.asideOptions = asideOptionsOne
      }
      break
    case 'sliderVal':
      Object.assign(requestData.value, { radius: data.sliderVal * 1000 + '' })
      break
    case 'chanye':
      fourList.value[0].active = false
      fourList.value[1].active = false
      fourList.value[2].active = false
      if(rest?.chain_codes?.length) {
        fourList.value[3].active = true
      }
      if(query?.trade_types?.length) {
        query.trade_types = []
      }
      // 不保留是否龙头
      // query['leading_ent'] = fourList.value[1].active
      // 切换产业时保留筛选
      const huntSureSources = data.huntData
      if (Object.keys(huntSureSources).length) {
        Object.assign(requestData.value, { ...huntSureSources })
      }
      Object.assign(requestData.value, query)
      data.asideOptions = asideOptionsChanye
      break
    case 'huntSure':
      // 处理特殊情况 产业链环节
      const sources = data.huntData
      if (sources?.chain_codes?.length) {
        delete sources['chain_codes_data']
      } else {
        // 筛选确认时 保留选择的产业信息
        // delete requestData.value['chain_codes']
      }
      if (Object.keys(sources).length) {
        Object.assign(requestData.value, { ...sources })
      } else {
        // 高级筛选为空的情况
        const keyAry = data.asideOptions.map(i => i.keys)
        requestData.value = Object.keys(requestData.value)
          .filter(key => {
            return !keyAry.includes(key)
          })
          .reduce((acc, key) => {
            acc[key] = requestData.value[key]
            return acc
          }, {})
      }

      break
    default:
      break
  }

  // 过滤调空字符串，空数组，空对象
  const params = Object.keys(requestData.value).reduce((acc, key) => {
    const value = requestData.value[key]
    if (
      !(Array.isArray(value) && value.length === 0) &&
      !(typeof value === 'object' && Object.keys(value).length === 0) &&
      value !== ''
    ) {
      acc[key] = value
    }
    return acc
  }, {})
  // 将参数传出去
  emits('update:modelValue', params)
}
const { handleClick, filterData, huntRef, onTabFour } = useFilter({ data, dropRef, getData })

// 一开始拿到传进来的地区和地区编码
onBeforeMount(() => {
  // 园区就杭州有数据 330100 杭州市
  const { area_name = '', area_code = '', chain_codes = '' } = route.query
  if (area_name && area_code) {
    data.area_name = area_name // 后面如果要完全对应地区 就需要通过code去本地找名字
    data.area_value = area_code
  }
  // if (chain_codes) {
  //   data.chain_codes = [chain_codes]
  // }
})
</script>

<style lang="scss" scoped>
:deep(.van-dropdown-menu__bar) {
  height: 88px;
  overflow: hidden;
  box-shadow: none;
  // border-bottom: 1px solid #eee;
  .van-dropdown-menu__item {
    justify-content: flex-start;
    padding-left: 62px;
  }
}
:deep(.activeMenuItem) {
  color: #3e7bfa !important;
  span {
    color: #3e7bfa !important;
  }
}
:deep(.activeMenuItem:after) {
  border-color: transparent transparent currentColor currentColor;
}
</style>
