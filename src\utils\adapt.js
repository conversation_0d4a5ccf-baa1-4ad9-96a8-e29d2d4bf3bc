!(function (n, e) {
  function setViewHeight() {
    let windowVH = e.innerHeight / 100
    n.documentElement.style.setProperty('--vh', windowVH + 'px')
  }
  // 判断当前环境是否为微信内置浏览器
  const isWechatBrowser = /MicroMessenger/i.test(navigator.userAgent)
  const i = 'orientationchange' in window ? 'orientationchange' : 'resize'
  if (isWechatBrowser) {
    // 非微信浏览器 执行特定逻辑  因为safari上下遮盖 这里需要计算可视高度  苹果微信不用
  } else {
    // 非微信浏览器
    n.addEventListener('DOMContentLoaded', setViewHeight)
    e.addEventListener(i, setViewHeight)
  }
})(document, window)
