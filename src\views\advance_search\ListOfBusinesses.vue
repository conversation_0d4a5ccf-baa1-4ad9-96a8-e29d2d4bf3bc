<template>
  <div class="home-search-container">
    <div class="search-input" style="height: 64px">
      <SearchInput
        v-model="ent_name"
        placeholder="请输入目标供应链企业名称"
        @clear="handleClear"
        @enter="addSearchHistory"
        @change="IptChange"
        :isRightIcon="false"
      />
    </div>
    <div class="search-list">
      <LeadingEnterprises
        :showTotal="false"
        :open-now="false"
        :highlightQuery="ent_name"
        :ent_name="ent_name"
        @clickItem="handleClickEntItem"
        showTotal
        :filterkEY="['ent_name']"
      />
    </div>
  </div>
</template>

<script setup name="ListOfBusinesses">
import SearchInput from '@/components/SearchInput/index.vue'
import LeadingEnterprises from './ListOfBuschildren.vue'

const router = useRouter()
const isShowList = ref(false)
const ent_name = ref('')

const handleClear = () => {
  ent_name.value = ''
  isShowList.value = false
}

const IptChange = val => {
  if (val) {
    isShowList.value = true
  } else {
    handleClear()
  }
}
</script>

<style lang="scss" scoped>
.home-search-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .search-input {
    padding: 20px 24px;
    background-color: #fff;
    flex-shrink: 0;
    height: 112px !important;
  }
  &-list {
    flex: 1;
  }

  background-color: #f7f7f7;
  // padding: 30px;
  height: 100%;
}
:deep(.dropdown) {
  border-top: none;
}
</style>
