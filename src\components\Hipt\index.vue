<template>
  <div ref="inputRef" class="ipt" :style="style">
    <Field
      :id="data.uuid"
      ref="iptRef"
      v-model="data.iptVal"
      :placeholder="placeholder"
      clearable
      :formatter="blur"
      format-trigger="onBlur"
      enterkeyhint="search"
      @focus="focus"
      :disabled="disabled"
    >
      <template #left-icon>
        <div class="input-ico">
          <SvgIcon name="Search" class="search" />
        </div>
      </template>
    </Field>
  </div>
</template>

<script setup>
import { nextTick, onBeforeMount, onUnmounted, reactive, ref } from 'vue'
import { Field } from 'vant'
import { v4 as uuidv4 } from 'uuid'

const iptRef = ref(null)

defineProps({
  placeholder: {
    type: String,
    default: '',
  },
  style: {
    type: [String, Object],
    default: 'padding: 14px 12px;height: 72px;',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:height', 'change'])
const inputRef = ref(null)
const data = reactive({
  iptVal: '',
  oldIptVal: '',
  offsetHeight: 0,
  uuid: 'ipt' + uuidv4(),
})
const focus = () => {}
const blur = val => {
  // 失去焦点发请求
  if (val.trim() != data.oldIptVal) {
    emit('change', val.trim())
  }
  data.oldIptVal = val.trim()
  return val.trim()
}
nextTick(() => {
  data.offsetHeight = inputRef.value.offsetHeight
  // 外面通过v-model拿到高度
  emit('update:height', data.offsetHeight)
})
const enter = event => {
  if (event.keyCode == 13) {
    iptRef.value?.blur()
  }
}
onBeforeMount(() => {
  nextTick(() => {
    let ipt = document.getElementById(data.uuid)
    ipt.addEventListener('keydown', enter)
  })
})
onUnmounted(() => {
  let ipt = document.getElementById(data.uuid)
  ipt?.removeEventListener('keydown', enter)
})
</script>

<style scoped lang="scss">
.ipt {
  width: 100%;
  // height: 128px;
  // padding: 28px 24px;
  background: #fff;
  display: flex;
  align-items: center;
  // height: 144px;
  box-sizing: border-box;
  .search {
    width: 36px;
    height: 36px;
  }
  :deep(.van-field) {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    background: #f7f7f7;
    font-size: 28px;
    font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 400;
    outline: none;
    color: #282d30;
    caret-color: rgba(7, 110, 228, 1) !important;
    // padding-left: 16rpx !important;
    padding: 16rpx 0 16rpx 28rpx !important;
    .van-field__body {
      height: 40px;
      line-height: 40px;
      input {
        padding-left: 16px;
      }
      input::placeholder {
        font-size: 28px;
        color: #9b9eac;
      }
    }
  }
}
</style>
