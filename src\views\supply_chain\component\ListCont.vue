<template>
  <div class="p-24">
    <div
      v-for="item in source"
      :key="item.id"
      @click="goDetail(item)"
      class="f-y-center justify-between px-24 bg-white h-96 mb-24 rounded-12"
    >
      <div class="f-y-center flex-1">
        <img src="@/assets/image/companys.png" class="w-64 h-64 mr-16" />
        <div class="text-#20263A text-28 sle">{{ item.enterprise_name }}</div>
      </div>
      <img src="@/assets/image/arrow_right.png" class="h-24 w-24 flex-shrink-0" />
    </div>
  </div>
</template>

<script setup>
import { pageChangeHandler } from '@/utils'
// const router = useRouter()
defineProps({
  source: {
    type: Boolean,
  },
})

const { proxy } = getCurrentInstance()

const goDetail = item => {
  if (!window.sessionStorage.getItem('preset')) {
    proxy.$showToast('请前往设置招商预设！')
    return
  }
  pageChangeHandler('/single-detail', { ent_id: item.enterprise_id, title: item.enterprise_name })
}
</script>

<style scoped></style>
