<template>
  <div class="card" >
    <div class="title">{{ title }}</div>
    <div class="box_wrap">
      <div
        class="box"
        v-for="item in list"
        :key="item.chain_code"
         :style="getBoxBackgroundStyle(item.chain_code)"
      >
        <div class="box_cont" @click="handleToChainNode(item)">
          <img :src="cardBackgroundStyle" alt="11"></img>
          <div>
            <HighlightText
              :text="item.name"
              :keyword="searchKeyword"
              highlight-class="search-highlight"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import HighlightText from './HighlightText.vue'
const router = useRouter()
const props = defineProps({
  title: {
    type: String,
    default: '大健康产业',
  },
  titCode:{
    type:String,
    default: 'A1',
  },
  source: {
    type: Array,
    default: () => [
      {
        belong_to: '',
        bespoke: '',
        chain_code: 'A1A1',
        children: [],
        fig: '0',
        level: '2',
        name: '生物医药产业',
        parent_code: 'A1',
        tag: '1',
      },

    ],
  },
  searchKeyword: {
    type: String,
    default: ''
  }
})

const list = computed(() => {
  return props.source
})

// 获取card背景样式
const cardBackgroundStyle = computed(() => {
  try {
    const imageUrl = new URL(`/src/assets/image/cyl/${props.titCode}.png`, import.meta.url).href
    return imageUrl
  } catch (error) {
    console.warn(`Card background image not found: ${props.titCode}.png`)
    return ''
  }
})

// 获取box背景样式
const getBoxBackgroundStyle = (chainCode) => {
  try {
    const imageUrl = new URL(`/src/assets/image/cyl/${chainCode}.png`, import.meta.url).href
    return {
      backgroundImage: `url(${imageUrl})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }
  } catch (error) {
    console.warn(`Box background image not found: ${chainCode}.png`)
    return {}
  }
}
// 处理跳转 
const handleToChainNode = (item) => {
  router.push({
    path: '/industrial_chain_node',
    query: { code: item.chain_code, name: item.name }
  })
}

</script>

<style lang="scss" scoped>
.card {
  @apply mb-20 bg-white;
  padding: 24px;
  .title {
    font-weight: 600;
    font-size: 32px;
    color: #404040;
    padding : 0 24px 24px;
  }
  .box_wrap {
    @apply flex flex-wrap;
    gap: 22px;
    .box {
      width: 340px;
      height: 120px;
      border-radius: 8px;
      overflow: hidden;
      padding: 24px 20px;
      .box_cont {
        display: flex;
        align-items: flex-start;
        
        img {
          width: 32px;
          height: 32px;
          margin-right: 12px;
        }
        div {
          width: 208px;
          height: 80px;
          font-family: PingFang SC, PingFang SC;
          font-weight: 600;
          font-size: 28px;
          color: #404040;

          :deep(.search-highlight) {
            color: #076ee4 !important;
          }
        }
      }  
    }
  }
}
</style>
