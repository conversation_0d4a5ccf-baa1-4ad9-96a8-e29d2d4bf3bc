<template>
  <span v-if="query && switcher" class="text-[#20263A] text-28">
    <!-- 此代码格式不能随意变动, 额外的换行会导致文本之间出现空格 -->
    {{ text.split(query)[0]
    }}<span v-if="text.includes(query)" :class="hightlightClass" :style="hightlightStyle">{{ query }}</span
    >{{ text.split(query)[1] }}
  </span>
  <span v-else class="text-[#20263A] text-28">{{ text }}</span>
</template>

<script>
/**
 * 高亮文本
 * 通过以query来拆分字符串然后拼接实现.
 */
export default {
  props: {
    query: {
      type: String,
      default: undefined,
    },
    hightlightClass: {
      type: String,
      default: 'light',
    },
    hightlightStyle: {
      type: Object,
      default: () => ({}),
    },
    switcher: {
      type: Boolean,
      default: true,
    },
    text: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      //
    }
  },
}
</script>

<style lang="scss" scoped>
.light {
  color: #e72410;
}
</style>
