<template>
  <!-- 只有龙头企业筛选是全部产业 其余列表筛选为行业 -->
  <div class="leading-page">
    <van-sticky>
      <div class="dropdown" ref="zhanWeiRef">
        <DropDown @submit="filterSubmit" :oldData="oldData" isHunt :aside-list="asideOptions" isChain="eleseic_data"
          :filters="['所属地区', '所属行业', '更多筛选']" />
      </div>
      <div class="flex justify-between p-24 text-28 text-secondary">
        <span>
          共找到
          <span class="text-selected">{{ total }}</span>
          家
          <span>{{ text }}</span>
          企业
        </span>

        <van-popover v-model:show="showPopover" placement="bottom-end" @select="onOrderSelect">
          <template #reference>
            <span class="flex items-center text-28 text-tertiary">
              {{ orderby.text }}
              <svg-icon name="arrow-down" class="ml-8 transition-all duration-300"
                :class="[showPopover ? 'rotate-180' : '']" />
            </span>
          </template>

          <div class="w-full bg-white">
            <div v-for="item in actions" :key="item.text" class="p-24"
              :class="[orderby.value === item.value && 'text-selected']" @click="onOrderSelect(item)">
              {{ item.text }}
            </div>
          </div>
        </van-popover>
      </div>
    </van-sticky>

    <!-- <div class="null" v-else></div> -->
    <div :style="{ height: listHeight + 'px' }" class="list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model:loading="loading" @load="loadmore" offset="1" :immediate-check="false" :finished="finished"
          :finished-text="entList.length ? '没有更多了' : undefined">
          <EntCard :entList="entList" :query="highlightQuery" :openNow="openNow"
            @clickItem="item => emits('clickItem', item)" special type="noThreeAddCollect" />
        </van-list>
      </van-pull-refresh>
      <div class="noData" v-if="noData">
        <img src="@/assets/image/null.png" alt="" />
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup name="LeadingEnterprises">
import DropDown from '@/components/Hunt/component/dropDown/index.vue'
import EntCard from '@/components/EntCard/index.vue'
import { handlestructure } from '@/components/Hunt/common/huntAbout'
// import { getChainList } from '@/api/advanceSearch/index.js'
import { asideOptions } from './options.js'
import { getChainList } from '@/api/chains/index'

const { proxy } = getCurrentInstance()
const props = defineProps({
  showTotal: {
    type: Boolean,
    default: true,
  },
  //企业数量文本展示
  text: {
    type: String,
    default: undefined,
  },
  searchParams: {
    //多选弹窗那种要把原始数据带上 不然回填不上
    type: Object,
    default: () => { },
  },
  openNow: {
    type: Boolean,
    default: true,
  },
  filters: {
    //排除更多筛选 当前模块筛选项目
    type: Array,
    default: () => ['所在地区', '所属行业', '更多筛选'],
  },
  filterkEY: {
    // 排除高亮
    type: Array,
    default: () => ['areas', 'trade_types'],
  },
  // 关键字 高亮公司名称
  highlightQuery: {
    type: String,
    default: undefined,
  },
  special: {
    //用于做一些个性化需求
    type: String,
    default: '',
  },
  ent_name: {
    type: String,
    default: '',
  },
})

const route = useRoute()
const emits = defineEmits(['clickItem'])

let params = reactive({ page_index: 1, page_size: 10 })
const total = ref(0)
const entList = ref([])
const loading = ref(false) //是否处于加载状态
const finished = ref(false) //是否已加载完成
const refreshing = ref(false) //下拉刷新加载
const noData = ref(false)
const refreshParams = ref({})
const zhanWeiRef = ref(null) //拿到列表高度
const listHeight = ref(0)
const showPopover = ref(false)
const oldData = ref({})

const actions = [
  { text: '默认排序', value: '' },
  { text: '成立日期从晚到早', value: 'ESDATE$DESC' },
  { text: '成立日期从早到晚', value: 'ESDATE$ASC' },
  { text: '注册资本从高到低', value: 'REGCAP$DESC' },
  { text: '注册资本从低到高', value: 'REGCAP$ASC' },
]

const orderby = ref(actions[0])

const onRefresh = () => {
  params.page_index = 1
  getList()
}

const loadmore = function () {
  if (total.value <= entList.value.length) {
    finished.value = true
    return
  }
  params.page_index += 1
  getList()
}

const getList = function (payload = {}) {
  if (params.page_index === 1) {
    entList.value = []
  }

  proxy.$loading('加载中')
  loading.value = true
  noData.value = false
  // if (refreshing.value) {
  //   entList.value = []
  //   refreshing.value = false
  // }
  getChainList({
    chain_codes: [route.query.code],
    insight: false,
    level: route.query.level,
    ...payload,
    ...params,
    ...refreshParams.value,
    sort: orderby.value?.value
      ? {
        name: orderby.value.value?.split('$')[0],
        order: orderby.value.value?.split('$')[1],
      }
      : undefined,
  })
    .then(res => {
      if (params.page_index === 1) {
        entList.value = res.items || []
      } else {
        entList.value.push(...res.items)
      }
      total.value = res.count || 0
      noData.value = !res.count
      proxy.$close()
    })
    .finally(() => {
      loading.value = false
      refreshing.value = false
      proxy.$close()
    })
}

const onOrderSelect = item => {
  orderby.value = item
  showPopover.value = false

  params.page_index = 1
  getList()
}

// 弹窗回调
const filterSubmit = (obj, str) => {
  entList.value = []
  total.value = 0
  finished.value = false
  const params = handlestructure(obj.params)

  const result = {}
  Object.keys(params).forEach(key => {
    if (Array.isArray(params[key])) {
      if (params[key]?.length) {
        result[key] = params[key]
      }
    } else {
      result[key] = params[key]
    }
  })
  // oldData.value = Object.keys(result).reduce((obj, key) => {
  //   if (!props.filterkEY.includes(key)) {
  //     obj[key] = result[key]
  //   }
  //   return obj
  // }, {})
  refreshParams.value = result
  getList()
}
onMounted(() => {
  getList()

  nextTick(() => {
    // 计算列表滚动高度
    const { height: h } = useWindowSize()
    const { top, height } = useElementBounding(zhanWeiRef, { immediate: true, windowResize: true }) //第一次拿不到
    listHeight.value = h.value - top.value - height.value - (props.showTotal ? 48 : 10)
  })
})
</script>

<style lang="scss" scoped>
.leading-page {
  height: 100%;
  background: #f7f7f7;

  .dropdown {
    // border-top: 2px solid #eee;
    display: flex;
    height: var(--van-dropdown-menu-height);
  }

  .list {
    // height: calc(100vh - 280px);

    overflow: auto;
    -ms-overflow-style: none;

    /* IE 10+ */
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
}

.noData {
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 230px;
    height: 230px;
    transform: translateY(-180px);
  }

  div {
    color: #74798c;
    margin-top: 24px;
    transform: translateY(-180px);
  }
}

.null {
  height: 20px;
}
</style>
