<template>
  <div class="bg-grey">
    <div class="mb-24 min-h-196">
      <img :src="entBg" class="absolute top-0 w-full h-full" />

      <div class="px-24 pt-32 pb-12">
        <div class="flex">
          <img :src="currentProject.logo" class="mr-20 w-96 h-96" />
          <div>
            <p class="mb-16 font-semibold text-32 text-primary min-h-44">{{ currentProject.ent_name }}</p>
            <div class="flex items-center h-36">
              <van-popover v-model:show="showOldName" placement="bottom">
                <template #reference>
                  <span class="flex items-center text-24 text-secondary break-keep">
                    曾用名: 
                    {{ oldNames[0] }}
                    <SvgIcon
                      v-if="oldNames.length > 1"
                      name="arrow-down"
                      class="w-24 h-24 ml-4 transition-all duration-300 mr-22"
                      :class="[showOldName ? 'rotate-180' : '']"
                    />
                  </span>
                </template>

                <div class="bg-white">
                  <div v-for="item in oldNames" :key="item.text" class="p-24">
                    {{ item }}
                  </div>
                </div>
              </van-popover>
            </div>
            <div class="flex items-center">
              <ColorfulTag :data="tags?.slice(0, TAGS_SHOW_COUNT)" />
              <span
                v-if="tags.length >= TAGS_SHOW_COUNT"
                class="flex items-center self-end text-24 break-keep text-selected"
                @click="handleMoreTag"
              >
                更多
                <SvgIcon name="arrow-blue" class="w-24 h-24" />
              </span>
            </div>

            <van-dialog v-model:show="show">
              <ColorfulTag :data="tags" />
            </van-dialog>
          </div>
        </div>
      </div>
    </div>

    <div class="flex items-center pb-12 bg-white">
      <div class="flex flex-col items-center justify-center h-110 w-[33%] px-24 py-12">
        <label class="mb-12 text-secondary text-24">法定代表人</label>
        <span class="text-26 text-primary break-keep">{{ businessInfo.legal_person }}</span>
      </div>
      <div class="w-1 h-48 bg-[#eee]"></div>
      <div class="flex flex-col items-center justify-center h-110 w-[33%] px-24 py-12">
        <label class="mb-12 text-secondary text-24">注册资本</label>
        <span v-if="businessInfo.register_capital" class="text-26 text-primary break-keep">{{ businessInfo.register_capital }}万</span>
      </div>
      <div class="w-1 h-48 bg-[#eee]"></div>
      <div class="flex flex-col items-center justify-center h-110 w-[33%] px-24 py-12">
        <label class="mb-12 text-secondary text-24">成立日期</label>
        <span class="text-26 text-primary">{{ businessInfo.register_date }}</span>
      </div>
    </div>

    <div class="flex items-center justify-between p-24 bg-white border-t border-b border-[#eee]">
      <span class="flex items-center text-selected text-24">
        <svg-icon v-if="currentProject.register_address" name="location" class="mr-8" />
        {{ currentProject.register_address }}
      </span>
      <div class="confirm-btn w-104 h-52" @click="handleNavigation">导航</div>
    </div>

    <div class="flex items-center justify-end px-24 mb-24 bg-white h-92">
      <EntRelatedOperations ref="entRelateRef" nearby show-contacts :data="currentProject" @nearby="handleNearby" />
    </div>

    <van-sticky class="z-999999" :offset-top="headerHeight + 'px'">
      <van-tabs v-model:active="active" :swipe-threshold="3">
        <van-tab title="基本信息" />
        <van-tab title="项目质量评估" />
        <van-tab title="供应链关系" />
        <van-tab title="企业关系链" />
        <van-tab title="企业情报监控" />
      </van-tabs>
    </van-sticky>

    <Baseinfo v-show="Number(active) === 0" :data="{ ...businessInfo, region_id: currentProject.region_id }" />
    <ProjectQuality v-if="Number(active) === 1" />
    <SupplyChain ref="supplyChainRef" v-if="Number(active) === 2" />
    <RelationChain v-if="Number(active) === 3" />
    <Monitor v-show="Number(active) === 4" />
  </div>
</template>

<script setup name="TargetDetail">
import entBg from '@/assets/image/ent-bg.png'
import { useRoute, useRouter } from 'vue-router'
import { onMounted, watch, nextTick, computed, onActivated } from 'vue'
import Baseinfo from './baseInfo.vue'
import ProjectQuality from './projectQuality.vue'
import { getEntBusinessInfo, getEntDetail } from '@/api/qualityReport'
import ColorfulTag from './ColorfulTag.vue'
import EntRelatedOperations from '@/components/EntCard/entRelatedOperations.vue'
import SupplyChain from './supplyChain.vue'
import RelationChain from './relationChain.vue'
import Monitor from './monitor/index.vue'
import { pageChangeHandler } from '@/utils'
import { useAppHeaderHeight } from '@/hooks/useAppHeaderHeight'

const route = useRoute()
const router = useRouter()
const headerHeight = useAppHeaderHeight()

const TAGS_SHOW_COUNT = 3
const active = ref(0)
const businessInfo = ref({})
const currentProject = ref({})
const oldNames = ref([])
const showOldName = ref(false)
const supplyChainRef = ref(null)
const entRelateRef = ref(null)
const show = ref(false)

const getOldNames = old_name => {
  let result = []
  const oldName = old_name
  if (typeof oldName === 'string' && oldName) {
    result = oldName.split(', ')
  }
  oldNames.value = result
}

const checkList = [
  { label: '乡贤企业', value: 'county_ent' },
  { label: '本地产业契合', value: 'fit_industry' },
  { label: '与本地有关联企业', value: 'number' },
  { label: '昨日风险变动', value: 'risk_change' },
]

function formatTag(tag) {
  return checkList.find(itm => itm.value === tag)?.label || ''
}

const tags = computed(() => {
  const tags = currentProject.value.tags?.map(item => ({ name: item })) || []
  const chains = currentProject.chains?.map(item => ({ name: item.tag, note: item.details.join(', ') })) || []
  const specialTags =
    currentProject.specialTags?.map(item => {
      const name = formatTag(item)
      return { name: name }
    }) || []

  return [...tags, ...chains, ...specialTags]
})

onActivated(() => {
  if (route.query?.tab !== undefined) {
    active.value = Number(route.query.tab)
  }
})

onMounted(() => {
  getEntBusinessInfo(route.query.ent_id).then(res => {
    businessInfo.value = res || {}
    getOldNames(res.old_name)
  })
  getEntDetail(route.query.ent_id).then(res => {
    currentProject.value = {
      specialTags: res.tags,
      ...res,
      ...res.ent_vo,
      monitorLoading: false,
    }
  })
})

const handleNearby = data => {
  pageChangeHandler('/excavate', { area_name: data.register_address, area_code: data.area_code })
}

const handleNavigation = () => {
  entRelateRef.value.openLocationDialog()
}

const handleMoreTag = () => {
  show.value = true
}
</script>

<style lang="scss" scoped>
:deep(.van-tab--active) {
  .van-tab__text {
    color: #2a72ff;
  }
}

:deep(.van-tabs__line) {
  background: linear-gradient(90deg, #2a72ff 0%, #3994ff 71%);
  width: 40px;
  height: 6px;
}

:deep(.van-dialog__header) {
  display: none;
}
</style>
