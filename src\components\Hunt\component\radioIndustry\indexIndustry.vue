<script setup name="RadioI">
import { onBeforeMount, reactive, watch, getCurrentInstance } from 'vue'
import { constant, getData } from '../MultiplecChoice/utils'
const { proxy } = getCurrentInstance()
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  // 上一次选中的数据，用于回填
  oldData: {
    type: String,
  },
  headText: {
    type: String,
    default: '全部',
  },
  childText: {
    type: String,
    default: ' ',
  },
  dataType: {
    type: String,
    default: constant.DistrictAry,
  },
})
const emits = defineEmits(['close', 'submit'])
const data = reactive({
  regionList: [], //  地区列表
  checkedList: [], // 存储选中项
  provinceList: [],
  cityList: [],
})
// 处理数据格式
const addLevel = list => {
  if (!list) return []
  let arr = list.map(item => {
    if (item?.children?.length) {
      item.children = item.children.map(i => {
        if (i?.children?.length) {
          i.children = i.children.map(itm => {
            return {
              ...itm,
              // level: 3,
              is_leaf: false,
            }
          })
          i.children.unshift({
            active: false,
            chain_code: i.chain_code,
            level: '3',
            name: props.childText + i.name,
            parent: '--',
            is_leaf: false,
            isFather: true,
          })
        }
        return {
          ...i,
          // level: 2,
          is_leaf: !!i?.children?.length,
        }
      })
      item.children.unshift({
        active: false,
        chain_code: item.chain_code,
        level: '3',
        name: props.childText + item.name,
        parent: '--',
        is_leaf: false,
        isFather: true,
      })
    }
    return {
      ...item,
      level: 1,
      is_leaf: !!item?.children?.length,
    }
  })
  return arr
}
// 初始化
// eslint-disable-next-line no-unused-vars
const getRegion = async (chain_code = '') => {
  let [_err, datas] = await proxy.$to('getAtlas', {
    code: 'E1E1',
    area_code: '500000',
  })
  // 处理数据
  datas = addLevel(datas)
  let text = props.headText
  datas.unshift({
    active: false,
    chain_code: 'ALL',
    level: '1',
    name: text,
    parent: '--',
    is_leaf: true,
  })
  data.regionList = datas
  gsetCityAry()
}
// 获取-设置 区的数组
const gsetAreaAry = cityList => {
  if (cityList.length <= 0) {
    data.cityList = []
    return
  }
  let arr = cityList.filter(item => item.active)
  if (arr.length <= 0) {
    data.cityList = []
    return []
  }
  arr = arr[0]?.children || []
  setTimeout(() => {
    data.cityList = arr
  }, 200)
  return arr
}
// 获取--设置 ：市的孩子数组
const gsetCityAry = () => {
  //每一次设置数据源的时候掉一次
  const { regionList } = data
  // debugger
  // console.log(regionList)
  let arr = regionList.filter(item => item.active)
  if (arr.length <= 0) {
    data.provinceList = []
    gsetAreaAry([])
    return []
  }
  arr = arr[0]?.children || []
  data.provinceList = arr
  gsetAreaAry(arr)
  return arr
}
const findchildren = (list, chain_code) => {
  let items = list.filter(item => {
    if (item.chain_code == chain_code) {
      return item
    } else {
      if (item?.children?.length) {
        return findchildren(item.children, chain_code)
      }
    }
  })
  return items?.length ? items?.[0] : false
}
//获取地区编码的层级关系
const getCityInfo = chain_code => {
  // 这里回显做麻烦了 一开始就应该处理数据 往每个孩子里面push一个父亲加自己chain_code的字符串。
  let arr = []
  let curItem = findchildren(data.regionList, chain_code)
  curItem && arr.push(curItem)
  if (curItem?.children?.length && curItem?.chain_code != chain_code) {
    let towItem = findchildren(curItem.children, chain_code)
    towItem && arr.push(towItem)
    if (towItem?.children?.length && towItem?.chain_code != chain_code) {
      let three = findchildren(towItem.children, chain_code)
      three && arr.push(three)
    }
  }
  return arr
}
//填充数据
const setBackFill = (list, data) => {
  for (let region of list) {
    region.active = false
    for (let item of data) {
      if (region.chain_code == item.chain_code) {
        if (region.isFather) {
          region.active = false
        } else {
          region.active = true
        }
      }
    }
    if (region.children?.length) setBackFill(region.children, data)
  }
  return list
}
// 数据回填
const backFillRegion = () => {
  let { regionList, checkedList, provinceList, cityList } = data
  let oldData = props.oldData
  if (!oldData || oldData == 'ALL') {
    // 22.11.15
    regionList.forEach(i => {
      if (i.active) {
        i.active = false
        i?.children?.length &&
          i.children.forEach(itm => {
            if (itm) {
              itm.active = false
              itm?.children?.length && itm.children.forEach(is => (is.active = false))
            }
          })
      }
      if (oldData == 'ALL' && i.chain_code == 'ALL') {
        i.active = true
      } else {
        i.active = false
      }
    })
    provinceList = []
    cityList = []
    data.regionList = regionList
    data.provinceList = provinceList
    data.cityList = cityList
    return
  }
  let arrList = getCityInfo(oldData)
  if (arrList?.length > 0) {
    regionList = setBackFill(regionList, arrList)
  }
  data.checkedList = checkedList
  data.regionList = regionList
  gsetCityAry()
}
//设置选中项
const setCheckedList = (regionList, checkedList) => {
  for (let region of regionList) {
    if (region.active) checkedList.push(region)
    if (region.children?.length) setCheckedList(region.children, checkedList)
  }
}
const close = () => {
  emits('close', false)
}
// 将选中数据通过自定义事件传递出去
const submit = () => {
  let { regionList, checkedList } = data
  setCheckedList(regionList, (checkedList = []))
  emits('submit', checkedList[checkedList.length - 1])
  emits('submitAry', checkedList)
}
// 单个点击
const getChildData = obj => {
  let { chain_code, level, is_leaf } = obj // active：当前点击节点的状态
  let { regionList } = data
  regionList = regionList.length
    ? regionList.map(region => {
        if (level == '1') {
          region.active = false
          if (region.chain_code === chain_code) region.active = true
        } else {
          // 第二级
          region.children = !region?.children?.length
            ? []
            : region.children.map(city => {
                city.active = false
                if (city.chain_code === chain_code)
                  if (city.isFather) {
                    city.active = false
                  } else {
                    city.active = true
                  }
                if (city?.children?.length) {
                  city.children = city.children.map(item => {
                    item.active = false
                    return item
                  })
                }
                return city
              })
        }
        return region
      })
    : []
  // 特殊判断 --香港这个
  if (!is_leaf && level == '1' && chain_code != 'ALL') {
    var obj
    regionList.forEach(item => {
      if (item.chain_code == chain_code) {
        obj = item
        item.active = true
      }
    })
    emits('submit', [obj])
    data.regionList = regionList
    gsetCityAry()
    return
  }
  data.regionList = regionList
  gsetCityAry()
  if (chain_code === 'ALL') {
    emits('submit', [data.regionList[0]])
    return
  }
  if (level === '3' || !is_leaf) {
    submit()
  }
}
const getArea = obj => {
  const { chain_code, level } = obj
  let { regionList } = data
  if (level != 3) {
    close()
    throw '数据错误'
    return
  }
  regionList = regionList.map(region => {
    region.children = region?.children?.length
      ? region.children.map(city => {
          if (city.children) {
            city.children = city.children.map(area => {
              area.active = false
              if (area.chain_code == chain_code) {
                if (area.isFather) {
                  area.active = false
                } else {
                  area.active = true
                }
              }
              return area
            })
          }
          return city
        })
      : []
    return region
  })
  data.regionList = regionList
  submit()
}
onBeforeMount(() => {
  getRegion()
})
watch(
  () => [props.visible, props.oldData],
  ([bl, old]) => {
    if (!bl || !old) return
    setTimeout(() => {
      backFillRegion()
    }, 1000)
  },
  {
    immediate: true,
  }
)
</script>

<template>
  <div class="region-wrap" @touchmove="_disabledPenetrate" @click="_disabledPenetrate" v-if="data.regionList.length">
    <!-- 省 -->
    <div class="list">
      <Scroll>
        <div
          class="item items"
          v-for="(province, index) in data.regionList"
          :key="province.chain_code"
          :class="{ actived: province.active }"
          @click="getChildData(province)"
        >
          <span>{{ province.name }}</span>
        </div>
      </Scroll>
    </div>
    <!-- 市-->
    <div class="list city-wrap">
      <Scroll v-if="data.provinceList.length">
        <div
          class="item"
          v-for="(city, index) in data.provinceList"
          :key="city.chain_code"
          :class="{ actived: city.active }"
          @click="getChildData(city)"
        >
          <span>{{ city.name }}</span>
        </div>
      </Scroll>
    </div>
    <!-- 区  -->
    <div class="list area-wrap">
      <Scroll v-if="data.cityList.length">
        <div
          class="item"
          :class="{ actived: area.active }"
          v-for="(area, index) in data.cityList"
          @click="getArea(area)"
        >
          <span>{{ area.name }}</span>
        </div>
      </Scroll>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.region-wrap {
  width: 100%;
  display: flex;
  color: #20263a;
  font-size: 27px;
  position: relative;

  &::before {
    content: ' ';
    width: 100%;
    height: 2px;
    background: #eeeeee;
    position: absolute;
    top: 0;
    transform: scaleY(0.5);
  }

  .province-wrap {
    height: 600px;
    width: 200px;
    background: #f7f7f7;
    /* border: 1px solid pink; */
  }

  .city-wrap,
  .area-wrap {
    width: 280px;
    height: 600px;
    /* border-right: 1px solid #eeeeee; */
    position: relative;

    &::before {
      content: ' ';
      height: 200%;
      width: 1px;
      background: #eeeeee;
      position: absolute;
      top: -50%;
      transform: scaleX(0.5);
    }
  }
  .list {
    height: 600px;
    overflow: hidden;
  }

  /* 每个item */
  .list .item {
    width: 100%;
    display: flex;
    align-items: center;
    /* justify-content: space-between; */
    /* padding: 20px 92px 20px 104px; */
    padding: 24px 24px 24px 20px;
    font-size: 28px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    text-align: left;
    color: #20263a;

    &.items {
      padding: 24px 24px 24px 20px;
      text-align: left;
    }
  }
}
.actived {
  /* background: #e7f1fd; */
  font-weight: 600;
  color: #076ee4 !important;
}
</style>
