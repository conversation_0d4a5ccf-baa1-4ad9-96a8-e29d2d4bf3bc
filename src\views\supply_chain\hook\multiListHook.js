import { Session } from '@/utils/storage'
import { getCurrentInstance } from 'vue'
const useMultiList = () => {
  const multiList = ref([])
  const isTenItem = computed(() => multiList.value.length >= 10)
  // 添加
  const addItem = item => {
    const exists = multiList.value.some(existingItem => existingItem.ent_id === item.ent_id)
    if (!exists) {
      multiList.value.push(item)
      Session.set('multiList', multiList.value)
      return true
    }
    return false
  }
  // 移除
  const removeItems = ids => {
    ids.forEach(ent_id => {
      const index = multiList.value.findIndex(item => item.ent_id === ent_id)
      if (index !== -1) {
        multiList.value.splice(index, 1)
      }
    })
    Session.set('multiList', multiList.value)
  }
  // 获取
  const getList = () => {
    const list = Session.get('multiList')
    if (list) {
      multiList.value = list
    }

    return multiList.value
  }
  // 是否大于两个
  const isGreaterThanTwo = computed(() => {
    return multiList.value.length >= 2
  })

  onMounted(() => {
    getList()
  })
  return {
    addItem,
    removeItems,
    getList,
    isGreaterThanTwo,
    multiList,
    isTenItem,
  }
}

export { useMultiList }
