@import "./variables.scss";
:root {
    --vh: 1vh;
}
html,
body {
    font-family: PingFang SC;
    height: 100%;
    width: 750px;
    font-family: $app-font-family;
    //禁止文字选中
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer */
    user-select: none; /* Standard syntax */
    // height: calc(var(--vh) * 100 - 1px);
}
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    position: relative;
    font-weight: normal;
}

.app-container {
    background-color: $app-background-color;
    background-color: #fff;

    .app-header {
        z-index: 9999;
    }
    //有导航没有footer的情况
    .app-body-navBar {
        height: calc(
            var(--vh) * 100 - var(--van-nav-bar-height) - env(safe-area-inset-bottom) - env(safe-area-inset-top)
        );
        @include hide-scrollbar;
    }
    // 没有导航有footer的情况
    .app-only-hasfooter {
        height: calc(var(--vh) * 100 - env(safe-area-inset-bottom) - env(safe-area-inset-top));
        .h_container {
            display: flex;
            flex-direction: column;

            .h_computed_content {
                height: calc(var(--vh) * 100 - $footerHeight - $inset-bottom - env(safe-area-inset-top));
                @include hide-scrollbar;
            }

            .h_footers {
                height: $footerHeight;
                padding-bottom: $p-b; //这个 //这个先加上 后面样式有问题再去掉
                flex-shrink: 0;
                box-shadow: 0 -2px 2px rgba(0, 0, 0, 0.05);
            }
        }
    }
    // 有导航有footer的情况  footer类名是写在页面里面的
    .app-body-hasfooterNav {
        height: calc(
            var(--vh) * 100 - var(--van-nav-bar-height) - env(safe-area-inset-bottom) - env(safe-area-inset-top)
        );
        .h_container {
            display: flex;
            flex-direction: column;

            .h_computed_content {
                height: calc(
                    var(--vh) * 100 - var(--van-nav-bar-height) - $footerHeight - $p-b - env(safe-area-inset-top)
                );
                @include hide-scrollbar;
            }

            .h_footers {
                height: $footerHeight;
                padding-bottom: $p-b; //这个 //这个先加上 后面样式有问题再去掉
                flex-shrink: 0;
                box-shadow: 0 -2px 2px rgba(0, 0, 0, 0.05);
            }
        }
    }
    // 没有导航没有footer的情况
    .app-body-noNavBarNoFooter {
        height: calc(var(--vh) * 100 - 1px - env(safe-area-inset-bottom) - env(safe-area-inset-top));
    }
}

.global-toast {
    background: rgba(0, 0, 0, 0.5) !important;
}

.van-overlay {
    background: rgba(0, 0, 0, 0.45) !important;
    position: fixed;
    top: 10 !important;
    left: 0;
    width: 100%;
    height: 100%;
}
.van-toast {
    background: rgba(0, 0, 0, 0.5) !important;
}
// 字体
@font-face {
    /*给字体命名*/
    font-family: "PingFang SC";
    /*引入字体文件*/
    src: url("./PingFang-SC.ttf");
    font-weight: normal;
    font-style: normal;
}

div {
    font-weight: 400;
    font-family: "PingFang SC";
}

u {
    text-decoration: none;
}

.global-toast {
    background: rgba(0, 0, 0, 0.5) !important;
}

.van-overlay {
    background: rgba(0, 0, 0, 0.45) !important;
    position: fixed;
    top: 10 !important;
    left: 0;
    width: 100%;
    height: 100%;
}

.van-toast {
    background: rgba(0, 0, 0, 0.5) !important;
}

/* 文字单行省略号 */
.sle {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 文字多行省略号 */
.mle {
    display: -webkit-box;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    word-break: break-all;
}

/* 文字多了自動換行 */
.break-word {
    word-break: break-all;
    word-wrap: break-word;
}

/* 输入框样式自定义 */
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
    color: #9b9eac;
    font-size: 28px;
}
.van-cell:after {
    border-bottom: 0.5px solid #e8e9ec !important;
}

.custom-input {
    font-size: 28px;
    color: #20263a;
    height: 96px;
    line-height: 48px;
    display: flex;
    align-items: center;
    padding: 28px 0 !important;

    &::after {
        right: 0 !important;
        left: 0 !important;
    }

    .van-radio {
        height: 40px;
        margin-left: 56px;

        .van-radio__icon {
            height: 40px;

            .img-icon {
                transform: translateY(3px);
            }
        }

        .van-radio__label {
            height: 20px;
            transform: translateY(-9px);
            font-size: 28px;
            color: #595c69;
            margin-left: 16px;
        }
    }

    .van-cell__title.van-field__label,
    .van-cell__value.van-field__value {
        height: 40px;
        line-height: 40px;
    }

    .van-cell__left-icon,
    .van-cell__right-icon {
        height: 40px;
        font-size: 24px;
        color: #9b9eac;
        margin-left: 16px;
        width: 20px;
        height: 20px;
        &::before {
            position: absolute;
            top: 0px;
            font-size: 0;
            width: 20px;
            height: 20px;
            display: inline-block;
            //background-image: url("@/assets/img/form/arrow-right.png");
            //background-size: 20px 20px;
        }
    }
    .van-field__label--required:before {
        top: 11%;
        color: #cd4343;
        font-weight: 400;
        font-size: 36px;
    }
    .van-field__error-message {
        font-size: 24px;
        color: #fe2118;
        text-align: right;
        line-height: 30px;
    }
    .van-field__body {
        height: 40px;
        line-height: 40px;

        .van-field__control {
            height: 40px;
            line-height: 40px;
        }

        .van-field__control--left,
        .van-field__control--right {
            color: #595c69;
            font-size: 28px;

            &::before {
                height: 40px;
                top: -5px;
            }
        }

        .van-icon-clear {
            font-size: 36px;
            color: #808080 !important;
            margin-left: 0 !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
            margin-right: 0 !important;
            margin-top: 10px !important;
        }
    }

    .custom-input-label {
        display: flex;
        label {
            display: flex;
            align-items: center;
            height: 40px;
            font-size: 28px;
            color: #20263a;
        }
    }
}
.self-hide-line {
    &::after {
        border: none !important;
    }
}

/*输入框换行*/
.custom-input-wrap {
    height: auto !important;
    line-height: unset !important;
    align-items: flex-start;

    .custom-input-label {
        height: 90px;
        margin-bottom: 20px;
    }

    .van-cell__value.van-field__value,
    .van-field__body,
    .van-field__control {
        height: auto !important;
        line-height: unset !important;
    }

    .van-cell__value.van-field__value {
        width: 100%;
    }
}

/*textarea样式设置*/
.custom-input-textarea {
    .van-cell__value.van-field__value {
        width: 100%;
    }

    .van-field__body {
        height: auto !important;
        line-height: 40px !important;
    }

    .van-field__control {
        color: #595c69;
        height: auto !important;
        line-height: 40px !important;
    }

    .van-field__control--left {
        font-size: 28px;
    }
}

/*弹框样式设置*/
.self-dialog {
    max-width: 702px !important;
    width: 702px !important;
    border-radius: 0 !important;
    background: transparent !important;
}

.self-pop-center {
    max-height: 100vh !important;
    max-width: calc(100% - 48px) !important;
    box-shadow: none !important;
    bottom: inherit !important;
    background: transparent !important;
}

.self-pop-center-toast {
    width: fit-content !important;
}
.self-pop-center-toast-prompt {
    .toast-wrapper {
        padding: 32px 24px !important;
    }
}
.self-pop-center-tip {
    max-width: calc(100% - 192px) !important;
    .toast-wrapper {
        padding: 0 !important;
    }
}

/*弹框样式popup*/
.self-popup {
    border-top-right-radius: 12px !important;
    border-top-left-radius: 12px !important;

    .popwrap {
        height: 100%;
    }
}

/*时间选择器样式*/
.self-time-picker {
    .van-picker__columns {
        .van-picker-column {
            &:first-child {
                .van-picker-column__item--selected {
                    // background: #F4F5F5;
                    // border-radius: 8px 0 0 8px;
                    border-width: 0;
                    position: relative;

                    // &::after {
                    //     content: ":";
                    //     position: absolute;
                    //     top: 20%;
                    //     right: 0;
                    //     width: 10px;
                    //     height: 48px;
                    //     line-height: 48px;
                    //     font-weight: 600;
                    //     font-size: 32px;
                    //     color: #cd4343;
                    // }

                    .van-ellipsis {
                        font-weight: 600;
                        font-size: 32px;
                        color: #cd4343;
                    }
                }
            }

            &:nth-of-type(2) {
                .van-picker-column__item--selected {
                    // background: #F4F5F5;
                    // border-radius: 0 8px 8px 0;
                    .van-ellipsis {
                        font-weight: 600;
                        font-size: 32px;
                        color: #cd4343;
                    }
                }
            }
        }
    }

    .van-picker__frame {
        position: fixed;

        &::after {
            border: none;
        }
    }
}

/*tab样式*/
.self-tab {
    height: 96px;

    .van-tabs__wrap {
        height: 96px !important;
    }

    .van-tabs__nav {
        background: transparent;
        height: 96px;
        padding-bottom: 0;

        .van-tab {
            height: 96px;
            padding: 26px 0;

            .van-tab__text {
                color: #595c69;
                font-weight: 400;
                font-size: 32px;
            }
        }

        .van-tab--active {
            .van-tab__text {
                color: #cd4343;
                font-weight: 600;
                font-size: 32px;
                line-height: 44px;
            }
        }

        .van-tabs__line {
            width: 80px;
            height: 6px;
            background: #cd4343;
            border-radius: 0px;
            bottom: 0;
        }
    }
}

.self-tab-blue {
    height: 88px;

    .van-tabs__wrap {
        height: 88px !important;
    }

    .van-tabs__nav {
        background: transparent;
        height: 88px;
        padding-bottom: 0;

        .van-tab {
            height: 88px;
            padding: 24px 0;

            .van-tab__text {
                color: #74798c;
                font-weight: 400;
                font-size: 28px;
            }
        }

        .van-tab--active {
            .van-tab__text {
                color: #2a72ff;
                font-weight: 600;
                font-size: 28px;
                line-height: 40px;
            }
        }

        .van-tabs__line {
            width: 40px;
            height: 6px;
            // background: #2a72ff;
            background: linear-gradient(90deg, #2a72ff 0%, #3994ff 71%);
            border-radius: 0px;
            bottom: 0px;
        }
    }
}

/* 公共按钮样式自定义 */
.submit-button {
    height: 84px;
    box-sizing: border-box;
    text-align: center;
    line-height: 84px;
    background: linear-gradient(90deg, #cd4343 0%, #e34646 100%);
    border-radius: 8px;
    font-size: 32px;
    color: #ffffff;
}
.red {
    border: 1px solid red;
}
.pink {
    border: 1px solid pink;
}
.blue {
    border: 1px solid blue;
}

// 列表缺省页样式
.noData {
    min-height: 60vh;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    img {
        width: 230px;
        height: 230px;
    }
    div {
        font-size: 32px;
        color: #74798c;
        margin-top: 48px;
    }
}

.toast-wrapper {
    .aaapop1 {
        margin-bottom: 56px;
        text-align: center;
        font-family:
            PingFang SC,
            PingFang SC;
        font-weight: 400;
        font-size: 32px;
        color: rgba(32, 38, 58, 1);
        line-height: 38px;
        u {
            color: rgba(205, 67, 67, 1);
        }
    }
}
[v-cloak] {
    display: none;
}
// 确认弹窗样式
.h_sure_pop {
    background: #fff;
    .toast-wrapper {
        padding: 48px 0 0 !important;
        .footer {
            font-size: 32px;
            border-top: 1px solid #eeeeee;
            position: relative;
            &::before {
                content: "";
                width: 1px;
                background: #eee;
                height: 100%;
                position: absolute;
                left: 50%;
            }
            .button {
                background: transparent !important;
            }
            .submit-button {
                background: transparent !important;
                color: #076ee4;
            }
        }
    }
    .pop {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 8px;
        .tit {
            font-size: 32px;
            color: #20263a;
        }
        .cont {
            font-size: 28px;
            color: #74798c;
            line-height: 46px;
            margin-top: 40px;
            margin-bottom: 40px;
        }
    }
}
// 调试微信的时候加的 目前还没调试出来
.van-safe-area-bottom {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}
