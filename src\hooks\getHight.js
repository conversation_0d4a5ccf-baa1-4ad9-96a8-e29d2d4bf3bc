import { useRect } from '@vant/use'
import { ref, nextTick } from 'vue'
/**
 *
 * @param {元素上一个ref} bortherRef
 * @param {当前ref} selfRef
 * @returns  剩余高度  用于滚动高度计算
 */
export function useDynamicHeight(bortherRef, selfRef) {
  let rect,
    h = ref(null),
    nextRect
  const setDynamicHeight = nextBorRef => {
    nextTick(() => {
      rect = useRect(bortherRef)
      h.value = window.innerHeight - rect.height - rect.top
      if (nextBorRef) {
        //底部占用的高度
        nextRect = useRect(nextBorRef)
        h.value = h.value - nextRect.height
      }
      selfRef.value.style.height = h.value + 'px'
    })
    return { resH: h, nextRect: rect }
  }

  return {
    setDynamicHeight,
  }
}
