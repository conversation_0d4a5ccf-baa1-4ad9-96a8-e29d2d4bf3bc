import hasPermi from './permission/hasPermi'
import hasRole from './permission/hasRole'
import has from './permission/has'
import { fixedInput, clickOutside } from './other/directive'
import debounce from './other/debounce.js'
import throttle from './other/throttle.js'

const directivesList = {
  debounce,
  throttle,
  hasPermi,
  hasRole,
  has,
  fixedInput,
  clickOutside,
}

const setDirectives = {
  install(app) {
    Object.keys(directivesList).forEach(key => {
      app.directive(key, directivesList[key])
    })
  },
}

export default setDirectives
