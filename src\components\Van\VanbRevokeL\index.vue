<template>
  <VanbToast v-bind="$attrs" @close="emits('close')">
    <div class="tips-wrapper">
      <slot
        ><p class="text">{{ text }}</p></slot
      >
      <div :class="[`footer flex justify-between footer-${theme}`]">
        <div class="button cancel" @click="emits('close')">取消</div>
        <div class="submit-button button submit" @click="clickSub">{{ sureText }}</div>
      </div>
    </div>
  </VanbToast>
</template>
<script setup>
import { watch } from 'vue'
import VanbToast from '@/components/Van/VanbToast/index.vue'
defineProps({
  text: {
    type: String,
    default: '确定要删除浏览历史？',
  },
  sureText: {
    type: String,
    default: '删除',
  },
  // 颜色主题
  theme: {
    type: String,
    default: '',
  },
})
const emits = defineEmits(['close', 'submit'])

function clickSub() {
  emits('submit')
}
</script>

<style scoped lang="scss">
.tips-wrapper {
  .text {
    width: 100%;
    // height: 44px;
    line-height: 44px;
    margin-bottom: 56px;
    text-align: center;
    font-weight: 400;
    font-size: 32px;
    color: #20263a;
  }
  .footer {
    width: 100%;
    height: 96px;
    .button {
      width: 48.5%;
      height: 76px;
      line-height: 76px;
      text-align: center;
      font-size: 32px;
      cursor: pointer;
    }
    .cancel {
      color: #20263a;
      border-radius: 8px;
      background: linear-gradient(84deg, #dddddd 0%, #e3e3e3 100%);
      cursor: pointer;
    }
  }
  .footer-blue {
    .submit {
      background: #1e92f0;
    }
  }
}
</style>
