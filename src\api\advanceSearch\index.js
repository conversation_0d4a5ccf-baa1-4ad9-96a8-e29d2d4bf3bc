import { Post, Get, Delete } from '@/request'
import { commonChainMenuLists } from '../../contant/dict'
import { hydrogenUrl, CHAIN_URL, BUSINESS_URL } from "@/api/constants";

// 搜索企业名字
export const getNameList = name => {
  return Get(`${CHAIN_URL}/industry/ent/name?ent_name=${encodeURI(name)}`)
}
// 搜索企业地名
const getMapSearch = data => {
  return Post(`${BUSINESS_URL}/maps/ent/list`, data)
}
// 全量行业树
const eleseicPlus = () => {
  return Get(
    `https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/elseic_plus.json`,
    {},
    {
      'Content-Type': 'application/json',
    }
  )
}
const mdistrictPlus = () => {
  return Post(`${CHAIN_URL}/industry/address/district_plus`, [])
}
// 全部企业许可
const getAllCert = data => {
  return Post(`${CHAIN_URL}/search/all_cert`, data)
}
// 全部企业类型
const allEntType = data => {
  return Post(`${CHAIN_URL}/search/ent_type`, data)
}
const districtPlus = () => {
  return Get(
    `https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/district_plus.json`,
    {},
    {
      'Content-Type': 'application/json',
    }
  )
}
// 新版所有产业链树
const chainAll = () => {
  return Get(`${CHAIN_URL}/merchants/industry/treeNode`)
}
// 列搜产业链
export const chainNewAll = () => {
  // const source = commonChainMenuLists.map(item => {
  //   return {
  //     name: item.label,
  //     chain_code: item.value,
  //   }
  // })
  // return new Promise((resolve, reject) => {
  //   resolve(source)
  // })
  return Get(`${CHAIN_URL}/merchants/industry/treeNode/new`)
}
// 获取固定的产业链数 特殊需求
export const getFixChian = () => {
  // return Post(`${hydrogenUrl}/industryAnalysis/treeNode`, ['A1E1', 'A1B1', 'A1A1', 'A1C1', 'A1G1'])
  const specialChainList = [
    { name: '生物医药产业', chain_code: 'A1A1' },
    { name: '医疗器械产业', chain_code: 'A1B1' },
    { name: '大健康产业', chain_code: 'A1C1' },
    { name: '中药产业', chain_code: 'A1E1' },
    { name: '生命科学产业', chain_code: 'A1G1' },
  ]

  function getChildren(chain_code) {
    return Get(`${CHAIN_URL}/industry/chain/code/${chain_code}/plus/tag?area_code=440306`)
  }

  // 返回一个 Promise，在 Promise 内部处理所有异步操作
  return Promise.all(
    specialChainList.map(async (item) => {
      const children = await getChildren(item.chain_code)
      return {
        ...item,
        children
      }
    })
  ).then(result => {
    console.log('result', result)
    return result
  })
}

// 获取模板
const getTemplate = () => {
  return Get(`${CHAIN_URL}/my_template`)
}

//保存模版
export const saveTemplate = data => {
  return Post(`${CHAIN_URL}/add_template`, data)
}
// 搜索模版
const searTemplate = () => {
  return Get(`${CHAIN_URL}/my_template`)
}
// 删除模版
const delTemplate = id => {
  return Delete(`${CHAIN_URL}/remove_template/${id}`)
}
// 获取高级搜索列表
export const getLeadingEntList = data => {
  return Post(`${CHAIN_URL}/industry/ent/portrait/list`, data)
}
// 联系方式
export const getEntContacts = data => {
  // return Get(`${CHAIN_URL}/industry/ent/contact/${data.ent_id}?types=1,2,3`)
  return Get(`${CHAIN_URL}/industry/ent/contact/${data.ent_id}?onceStr=${!data.collect}`)
}
// 收藏接口
export function batch(data) {
  return Post(`${CHAIN_URL}/collect/common/collectEnterprise/batch`, data)
}
// 取消收藏接口
export function removeBatch(data) {
  return Post(`${CHAIN_URL}/collect/common/removeEnterprise/batch`, data)
}
// 跳转h5
export const getH5Address = id => {
  return Get(`${CHAIN_URL}/search/company/report/${id}`)
}

export default {
  getNameList,
  getMapSearch,
  eleseicPlus,
  getAllCert,
  allEntType,
  districtPlus,
  chainAll,
  saveTemplate,
  searTemplate,
  getTemplate,
  delTemplate,
  mdistrictPlus,
  getLeadingEntList,
  getFixChian,
  chainNewAll,
}
