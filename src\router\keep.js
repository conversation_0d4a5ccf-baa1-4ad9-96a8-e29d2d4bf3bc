import { ref, getCurrentInstance, reactive, onDeactivated, onActivated } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'

export const excludes = ref([])
export function removeKeepAliveCache(instance) {
  if (!instance?.type?.name) return
  if (excludes.value.includes(instance.type.name)) return
  excludes.value.push(instance.type.name)
  // console.log(excludes.value, 'remove');
}
export function resetKeepAliveCache(instance) {
  excludes.value = excludes.value.filter(item => item !== instance.type.name)
  // console.log(excludes.value, 'reset');
}

export const GlobalData = {
  animationMode: ref('animation'),
}

export function useRemoveKeep(arr = ['Home']) {
  let ary = arr.filter(i => i)
  const instance = getCurrentInstance()
  // 上一个用了keep-alive的页面 返回到当前才会触发onBeforeRouteLeave  ary为返回上一页面的名字：目的在于
  // 清楚当前也 比如a,b都缓存，a->b ，需要每次进入b都是新的 那么就需要存a的名字到ary  b返回时就会被移除缓存列表
  // （ps： a,b都为keep-alive 生命周期依然会，触发onDeactivated）
  onBeforeRouteLeave((to, from) => {
    // console.log('onBeforeRouteLeave', 'to', to.name, 'form', from.name)
    if (ary.includes(String(to.name))) {
      removeKeepAliveCache(instance)
    } else {
      resetKeepAliveCache(instance)
    }
  })
}

export function useLastPositon(scrollElement) {
  const refLastPosition = reactive({
    x: 0,
    y: 0,
  })

  onActivated(() => {
    if (!(refLastPosition.x === 0 && refLastPosition.y === 0)) {
      scrollElement.value?.scrollTo({
        top: refLastPosition.y,
        left: refLastPosition.x,
      })
    }
  })

  onDeactivated(() => {
    refLastPosition.x = scrollElement.value?.scrollLeft || 0
    refLastPosition.y = scrollElement.value?.scrollTop || 0
  })
}
