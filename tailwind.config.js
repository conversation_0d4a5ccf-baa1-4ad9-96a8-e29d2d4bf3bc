/** @type {import('tailwindcss').Config} */
const defaultTheme = require('tailwindcss/defaultTheme')

const spacing = Array.from({ length: 1500 }).reduce((map, _, index) => {
  map[index] = `${index}px`
  return map
}, {})

const opacity = Array.from({ length: 8 }).reduce((map, _, index) => {
  map[index + 1] = `${(index + 1) / 100}`
  return map
}, {})

const getNumSpacing = num =>
  Object.keys(spacing)
    .slice(0, num)
    .reduce((map, key) => {
      map[key] = spacing[key]
      return map
    }, {})
module.exports = {
  // 由于 Tailwind 不再在底层使用 PurgeCSS，我们已将该purge选项重命名为content以更好地反映其用途：
  // purge: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  important: true,
  theme: {
    spacing,
    extend: {
      fontSize: ({ theme }) => ({
        ...theme('spacing'),
      }),
      backgroundImage: theme => {
        return {
          confirm: 'linear-gradient(90deg, #3994FF 0%, #2A72FF 100%)',
          cancel: 'linear-gradient(315deg, #EEEEEE 0%, #F5F5F5 100%)',
          signal: 'linear-gradient(180deg, rgba(42,114,255,0.99) 0%, #3994FF 74%)',
          progress: 'linear-gradient(90deg, #2A72FF 0%, #3994FF 71%)'
        }
      },
      colors: {
        '#333': '#333333',
        '#666': '#666666',
        '#999': '#999999',
        '#eee': '#eeeeee',
        primary: '#20263A',
        secondary: '#74798C',
        tertiary: '#525665',
        headline: '#010101',
        selected: '#2A72FF',
        grey: '#f7f7f7',
        danger: '#E72410',
        '#F4F4F4': '#F4F4F4',
        '#20263A': '#20263A',
        '#525665': '#525665',
        '#F7F7F7': '#F7F7F7',
        '#3E7BFA': '#3E7BFA',
      },
      fontWeight: {
        400: '400',
        500: '500',
        600: '600',
      },
      borderRadius: getNumSpacing(20), // 取前 20 个值
      opacity, // 0.01 - 0.09 默认透明度为1
      lineHeight: getNumSpacing(25),
    },
    fontFamily: {
      sans: ['PingFang SC', ...defaultTheme.fontFamily.sans],
      ph: ['Alibaba PuHuiTi', 'sans-serif'],
    },
  },
  plugins: [],
}
