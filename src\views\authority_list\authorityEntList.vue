<template>
  <div class="ent-list-page">
    <div class="top">
      <div class="name">{{ route.query?.name }}</div>
      <div class="inlines">
        <div class="time publish">发布时间：{{ route.query?.release_date }}</div>
        <div class="time source text-ellipsis">来源：{{ route.query?.source }}</div>
      </div>
    </div>
    <div class="list" ref="listRef">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          @load="loadmore"
          offset="1"
          :immediate-check="false"
          :finished="finished"
          :finished-text="entList.length ? '没有更多了' : undefined"
        >
          <EntCard :entList="entList" type="noThreeAddCollect" special />
        </van-list>
      </van-pull-refresh>
      <div class="noData" v-if="noData">
        <img src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/null.png" alt="" />
        <div>暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script setup name="AuthorityEntList">
import { useRoute, useRouter } from 'vue-router'
import { getAuthorityEntList } from '@/api/authority'
import { reactive, ref, onMounted } from 'vue'
import EntCard from '@/components/EntCard/index.vue'

const loading = ref(false)
const finished = ref(false) //是否已加载完成
const refreshing = ref(false) //下拉刷新加载
const noData = ref(false)
const entList = ref([])
const route = useRoute()
const params = reactive({
  data_source: 1,
  id: route.query?.id,
  page_index: 1,
  page_size: 100,
  theme_type: route.query?.name,
  type: route.query?.type,
})
const total = ref(0)
// useLoading()

const onRefresh = cb => {
  params.page_index = 1
  _getAuthorityEntList()
}
const loadmore = function (cb) {
  if (total.value <= entList.value.length) {
    finished.value = true
    return
  }
  params.page_index += 1
  _getAuthorityEntList()
}
const _getAuthorityEntList = () => {
  loading.value = true
  noData.value = false
  getAuthorityEntList(params)
    .then(res => {
      if (res.items.length) {
        res.items = res.items.map(i => {
          return {
            ...i,
            collect: i.collected,
          }
        })
      }
      if (params.page_index === 1) {
        entList.value = res.items || []
      } else {
        entList.value.push(...res.items)
      }

      total.value = res.count || 0
      noData.value = !res.count
    })
    .finally(() => {
      loading.value = false
      refreshing.value = false
    })
}
const listRef = ref(null)
onMounted(() => {
  _getAuthorityEntList()
  nextTick(() => {
    // 计算列表滚动高度
    const { height: h } = useWindowSize()
    const { top } = useElementBounding(listRef, { immediate: true, windowResize: true }) //第一次拿不到
    const remainingHeight = h.value - top.value
    listRef.value.style.height = remainingHeight + 'px'
  })
})
</script>

<style lang="scss" scoped>
.ent-list-page {
  background: #f7f7f7;
  height: 100%;
  & > div {
    margin-bottom: 20px;
  }
  .top {
    height: 174px;
    background: #ffffff;
    padding: 0 24px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-top: 1px solid #eeeeee;
  }
  .list {
    height: calc(100vh - 194px);
    overflow: auto;
  }
  .name {
    font-size: 32px;
    font-family: PingFang SC-Semibold, PingFang SC;
    font-weight: 600;
    color: #20263a;
    line-height: 38px;
    margin-bottom: 16px;
  }
  .inlines {
    display: flex;
    .time {
      font-size: 24px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #74798c;
      line-height: 28px;
    }
    .publish {
      flex-shrink: 0;
    }
    .text-ellipsis {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
  .source {
    padding-left: 40px;
  }
}
.noData {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 240px;
  img {
    width: 230px;
    height: 230px;
  }
  div {
    color: #74798c;
    margin-top: 24px;
  }
}
</style>
