import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'
// import eslintPlugin from 'vite-plugin-eslint'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'
// import qiankun from 'vite-plugin-qiankun'
import { svgBuilder } from './src/plugins/svgBuilder'
// import { viteVConsole } from 'vite-plugin-vconsole'
import pxTovw from 'postcss-px-to-viewport'
import { VitePlugin } from 'svg-preview-plugin'
import vueSetupExtend from 'vite-plugin-vue-setup-extend'
import vuejsx from '@vitejs/plugin-vue-jsx'
import tailwindcss from 'tailwindcss'
import autoprefixer from 'autoprefixer'
// 引入插件
import AutoImport from 'unplugin-auto-import/vite'
// 打包压缩
import viteCompression from 'vite-plugin-compression'

const loaderPxToVw = pxTovw({
  viewportWidth: 750,
  viewportUnit: 'vw',
  exclude: [/node_modules\/vant/i],
})
const vantPxToVw = pxTovw({
  viewportWidth: 375,
  viewportUnit: 'vw',
  exclude: [/^(?!.*node_modules\/vant)/], //忽略除vant之外的
})

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  const { VITE_APP_NAME, VITE_PUBLIC_PATH } = env
  const isProduction = mode === 'production' || mode === 'zg';
  return {
    base: VITE_PUBLIC_PATH,
    server: {
      hmr: true,
      open: true,
      host: '0.0.0.0',
      port: 9004,
      origin: 'http://localhost:9004',
      https: false,
      cors: true,
      proxy: {
        '/hdzhzs/uc': {
          target: 'https://desktest.szcg.cn:443/',
          changeOrigin: true,
        },
        '/hdzhzs/industry_admin': {
          target: 'https://desktest.szcg.cn:443/',
          changeOrigin: true,
        },
        '/hdzhzs/business_admin': {
          target: 'https://desktest.szcg.cn:443/',
          changeOrigin: true,
        },
        '/hdzhzs/hydrogen': {
          target: 'https://desktest.szcg.cn:443/',
          changeOrigin: true,
        },
      },
    },
    css: {
      postcss: {
        plugins: [tailwindcss, autoprefixer, loaderPxToVw, vantPxToVw],
      },
      extract: isProduction ? { ignoreOrder: true } : false,
      sourceMap: !isProduction,
      preprocessorOptions: {
        // 全局样式引入
        scss: {
          // 文件路径，注意最后需要添加 ';'
          additionalData: '@import "@/assets/styles/variables.scss";',
          javascriptEnabled: true,
        },
      },
    },
    plugins: [
      vue(),
      // qiankun(VITE_APP_NAME, {
      //   useDevMode: true,
      // }),
      Components({
        dts: false,
        resolvers: [VantResolver()],
      }),
      svgBuilder('./src/assets/icons/svg/'),
      // eslintPlugin({
      //     include: ['src/**/*.js', 'src/**/*.vue', 'src/*.js', 'src/*.vue']
      // }),
      // localEnabled: command === 'serve',
      // enabled: command === 'serve',
      // viteVConsole({
      //   entry: [path.resolve('src/main.js')],
      //   localEnabled: true,
      //   enabled: true,
      //   config: {
      //     maxLogNumber: 1000,
      //     theme: 'light',
      //   },
      // }),
      VitePlugin({
        dirPath: path.resolve('src/assets/icons/svg'),
        port: 3344,
        open: false,
        deep: true,
        formatName: ({ name }) => {
          return `<svg-icon name="${name}" />`
        },
      }),
      vueSetupExtend(),
      vuejsx(),
      AutoImport({
        imports: ['vue', 'vue-router', '@vueuse/core'],
        dts: 'src/auto-import.d.ts', // 路径下自动生成文件夹存放全局指令,解决ts 报错
      }),
      viteCompression(),
    ],
    build: {
      target: 'es2015',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction,
        },
      },
    },
    resolve: {
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'], // 之前忽略了 .mjs
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '~': fileURLToPath(new URL('./src/components/Common', import.meta.url)),
      },
    },
  }
})
