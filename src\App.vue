<script setup>
import * as echarts from 'echarts'
import { nextTick, provide, ref } from 'vue'
import '@/utils/adapt.js'

provide('echarts', echarts)
const isReload = ref(true)
const reload = () => {
  isReload.value = false
  nextTick(() => {
    isReload.value = true
  })
}
provide('reload', reload)
</script>

<template>
  <!-- 这级没加keep-alive 到时需要需要单独处理 加个字段 -->
  <RouterView v-if="isReload" />
</template>
