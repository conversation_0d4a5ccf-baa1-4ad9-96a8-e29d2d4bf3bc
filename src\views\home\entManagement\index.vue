<template>
  <div class="w-full min-h-full bg-grey">
    <van-sticky>
      <div class="flex justify-between p-24 bg-white">
        <van-checkbox v-model="allChecked" shape="square" @change="handleCheckAll">全选</van-checkbox>
        <span class="flex items-center text-28 text-danger" @click="handleDelete">
          <SvgIcon name="delete-red" class="mr-8 w-36 h-36" />
          删除
        </span>
      </div>
    </van-sticky>

    <div class="px-24 pb-160" :class="noData ? 'h-[90vh]' : ''">
      <div v-if="noData" class="flex flex-col items-center justify-center w-full h-full">
        <SvgIcon name="empty-data" class="w-[230px] h-[230px] mb-24" />
        <p class="text-secondary text-28">暂未检索到相关信息</p>
      </div>

      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          @load="loadmore"
          offset="1"
          :immediate-check="false"
          :finished="finished"
          :finished-text="entData.length ? '没有更多了' : undefined"
        >
          <div v-for="(item, index) in entData" class="p-24 mt-24 bg-white">
            <van-checkbox v-model="item.checked" shape="square">{{ item.ent_name }} </van-checkbox>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <div class="fixed bottom-0 flex justify-between px-24 py-10 bg-white pb-50">
      <div class="w-[340px] mr-22 cancel-btn" @click="handleAddItem">单个添加</div>
      <div class="w-[340px] confirm-btn" @click="handleAddBatch">批量导入</div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { getCurrentInstance } from 'vue'
import { showDialog } from 'vant'
import { getPointEntList, addPointEnt, removePointEntBatch } from '@/api/home'
import { pageChangeHandler } from '@/utils'

const { proxy } = getCurrentInstance()
const router = useRouter()
const allChecked = ref(false)
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
})
const entData = ref([])
const noData = ref(false)
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)

const _getPointEntList = () => {
  loading.value = true
  noData.value = false
  if (refreshing.value) {
    entData.value = []
    refreshing.value = false
  }
  getPointEntList({
    $count: true,
    $offset: (pagination.current - 1) * pagination.pageSize,
    $limit: pagination.pageSize,
  })
    .then(res => {
      if (pagination.current === 1) {
        entData.value = res.items || []
      } else {
        entData.value.push(...res.items)
      }
      pagination.total = res.count || 0
      noData.value = !entData.value.length
    })
    .finally(() => (loading.value = false))
}

_getPointEntList()

const onRefresh = () => {
  pagination.current = 1
  _getPointEntList()
  // refreshing.value = false
}

const loadmore = cb => {
  if (pagination.total <= entData.value.length) {
    finished.value = true
    return
  }
  pagination.current += 1
  _getPointEntList()
}

const handleCheckAll = () => {
  entData.value.forEach(item => {
    item.checked = allChecked.value
  })
}

const handleDelete = () => {
  const ids = entData.value.filter(item => item.checked).map(item => item.id)
  if (!ids.length) return
  showDialog({
    title: '删除企业',
    message: '确定要删除选中企业？',
    showConfirmButton: true,
    showCancelButton: true,
    beforeClose: action => {
      return new Promise(resolve => {
        if (action === 'confirm') {
          removePointEntBatch(ids).then(() => {
            proxy.$showToast('删除成功')
            pagination.current = 1
            _getPointEntList()
            resolve(true)
          })
        } else {
          resolve(true)
        }
      })
    },
  })
}

const handleAddItem = () => {
  pageChangeHandler('/add-ent')
}

const handleAddBatch = () => {
  pageChangeHandler('/import-ent')
}
</script>

<style lang="scss" scoped>
:deep(.van-checkbox__icon) {
  font-size: 32px;
}
</style>
