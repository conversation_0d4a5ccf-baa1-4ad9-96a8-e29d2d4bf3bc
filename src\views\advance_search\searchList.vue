<template>
  <div class="home-search-container">
    <LeadingEnterprises
      :searchParams="oldParams"
      :showTotal="true"
      :open-now="false"
      :filters="['全国', '全部行业', '更多筛选']"
      :filterkEY="['areas', 'trade_types']"
    />
  </div>
</template>

<script setup name="Search">
import LeadingEnterprises from './LeadingEnterprises.vue'
const oldParams = reactive({})
onMounted(() => {
  let params = localStorage.getItem('hParams')
  localStorage.removeItem('hParams')
  params = JSON.parse(decodeURIComponent(params))
  nextTick(() => {
    Object.assign(oldParams, params)

    // console.log(oldParams);
  })
})
</script>

<style lang="scss" scoped>
.home-search-container {
  height: 100%;
  background-color: #f7f7f7;
}
</style>
