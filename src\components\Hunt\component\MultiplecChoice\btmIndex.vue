<script setup name="MmultiplecB">
import { nextTick, onBeforeMount, reactive, watch } from 'vue'
import { getData, constant } from './utils'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
    require: true,
  },
  // 上一次选中的数据，用于回填
  oldData: {
    type: Array,
    default: () => [],
  },
  dataType: {
    type: String,
    default: constant.DistrictAry,
  },
  mark: {
    type: String,
    default: '',
  },
  // 按钮文字
  buttomtext: {
    type: String,
    default: '取消',
  },
})
const emits = defineEmits(['close', 'submit'])

const data = reactive({
  visible: false,
  cityObj: {}, //临时缓存不同省的市列表
  areaObj: {}, // 临时缓存不同市的区列表
  checkedList: [], // 选中列表
})
const regionObj = reactive({
  provinceList: [], // 省列表
  curCityList: [], // 当前选中省的市列表
  curAreaList: [], // 当前选中市的区列表
})

//
onBeforeMount(() => {
  getRegion()
})
// methods
const changeParentStatus = (list, type) => {
  let { provinceList, curCityList } = regionObj
  let temp = type === 'area' ? curCityList : provinceList
  let checkedArr = [], // 存储选中状态的数组
    semiCheckedArr = [], // 存储半选状态的数组
    parentCode = '' // 父级的code
  for (let item of list) {
    parentCode = item.parent
    if (item.status === 'checked') checkedArr.push(item.status)
    if (item.status === 'semi-checked') semiCheckedArr.push(item.status)
  }
  temp = temp?.map(item => {
    if (item.code === parentCode) {
      let len = checkedArr.length,
        semiLen = semiCheckedArr.length
      item.status = len === list.length ? 'checked' : semiLen ? 'semi-checked' : len === 0 ? '' : 'semi-checked'
      setCheckedList(item) // 设置选中项数据
    }
    return item
  })
  let region = type === 'area' ? 'curCityList' : 'provinceList'
  regionObj[region] = temp
}

/**
 * 设置选中项状态
 */
const setCheckedList = item => {
  const { checkedList } = data
  const index = checkedList.findIndex(region => region.code === item.code)

  if (item.status) {
    const newItem = { ...item, status: item.status }
    if (index < 0) {
      checkedList.push(newItem)
    } else {
      checkedList[index] = newItem
    }
  } else {
    if (index > -1) {
      checkedList.splice(index, 1)
    }
  }

  data.checkedList = checkedList
}
/**
 * 清空历史的选中项
 */
const clearChildrenCheckedLsit = (parentCode, status) => {
  if (status) return

  let { checkedList } = data
  const uncheckedList = checkedList.filter(item => item.parent === parentCode)
  checkedList = checkedList.filter(item => item.parent !== parentCode)

  uncheckedList.forEach(item => {
    checkedList = checkedList.filter(i => i.parent !== item.code)
  })

  data.checkedList = checkedList
}
/**
 * 获取地区
 */
const getRegion = async (code = '', level, status = '') => {
  let { cityObj, areaObj, checkedList } = data
  let dataType = props.dataType
  let temp = ''
  let oldData = props.oldData

  temp = !level ? 'provinceList' : level === '1' ? 'curCityList' : 'curAreaList'
  let dataList = await getData({ code, dataType })
  regionObj[temp] = dataList.map(item => {
    if ((level == '1' || level == '2') && status == 'semi-checked') {
      // 回调的特殊情况 当level为1或者2 ,status为'semi-checked' 并且oldData存在的情况需要特殊处理 ,这种说明也没有缓存--直接改变item的值
      oldData.forEach(element => {
        if (element.code == item.code) {
          item.status = element.status
        }
      })
    } else {
      item.status = status // 根据父级选中状态更新子级状态
    }
    // item.status = status; // 根据父级选中状态更新子级状态22.2.28
    if (level === '1' && item.level === '3') item.isarea = true //  如果当前获取的是直辖市的下级数据，添加一个区的标记
    if (item.status) {
      // 不知道为啥这里会push，但是要判断是否重复✨
      const ind = checkedList.findIndex(region => {
        return region.code === item.code
      })
      ind < 0 && checkedList.push(item)
      ind > -1 && (checkedList[ind].status = item.status)
    }
    return item
  })

  if (level === '1') cityObj[code] = dataList // 将该省级的市级数据存入缓存
  if (level === '2') areaObj[code] = dataList // 将该市的区级数据存入缓存

  // 如果是第一次调用，则是获取的省级，取省级数据的第一项再调一次，获取市级
  if (!level) {
    regionObj[temp][0].active = true
    getRegion(regionObj[temp][0].code, regionObj[temp][0].level)
  }
  data['cityObj'] = cityObj
  data['areaObj'] = areaObj
}
/**
 * 点击省-获取孩子节点
 */
const getChildData = obj => {
  let { code, level, active, status } = obj // active：当前点击节点的状态
  let { cityObj, areaObj } = data
  let sendRequest = true // 是否发送请求获取数据
  let key = level === '1' ? 'provinceList' : 'curCityList'
  if (!active) regionObj.curAreaList = [] // 每次切换清空市下面的区列表
  // 当前选中项添加active样式
  //
  regionObj[key] = regionObj[key].map(item => {
    item.active = false
    if (item.code === code) item.active = true
    return item
  })
  // 切换省级的时候重置市级的选中状态
  if (level === '1' && !active) {
    regionObj.curCityList = regionObj.curCityList.map(city => {
      city.active = false
      return city
    })
  }
  // 如果点击的省，并且已经缓存过该省下面的市列表，则从缓存获取市数据
  if (level === '1' && code in cityObj) {
    regionObj.curCityList = cityObj[code]
    sendRequest = false
  }

  // 如果点击的市，并且已经缓存过该市下面的区列表，则从缓存获取区数据
  if (level === '2' && code in areaObj) {
    regionObj.curAreaList = areaObj[code]
    sendRequest = false
  }

  // 如果存在市是区的level的情况，当做区处理
  if (level === '3') sendRequest = false

  // 发送请求获取对应数据
  sendRequest && getRegion(code, level, status)
}

/**
 * 点击复选框
 */
const checkeEvent = item => {
  const { code, level, isarea } = item
  let { status, active } = item
  let { cityObj, areaObj } = data

  const key =
    level === '1' ? 'provinceList' : level === '2' || (level === '3' && isarea) ? 'curCityList' : 'curAreaList'

  const obj = level === '1' ? cityObj : level === '2' ? areaObj : {}

  status = status ? '' : 'checked'

  // 更新当前点击节点的状态
  const newRegionObj = regionObj[key].map(region => {
    if (region.code === code) {
      region.status = status
      setCheckedList(region)
      return item
    }
    return region
  })

  // 如果当前选中的不是区级则修改活动状态
  // 如果对应的市级列表/区级列表已经存在缓存
  if (obj[code]) {
    obj[code] = obj[code].map(city => {
      city.status = status
      setCheckedList(city)

      // 如果是省级触发选中事件，将区级状态同步更新
      if (level === '1' && areaObj[city.code]) {
        areaObj[city.code] = areaObj[city.code].map(area => {
          area.status = status
          setCheckedList(area)
          return area
        })
      }

      return city
    })
  } else if (level === '1' && !isarea) {
    clearChildrenCheckedLsit(code, status)
  }

  // 如果是反向一开始并没有缓存，但是checklist已经有了(孩子节点) 所有也要清除掉
  clearChildrenCheckedLsit(code, status)

  // 如果不是区级或者是区级但是isarea为真，则获取下级数据
  if (!(level === '3' && !isarea)) {
    getChildData({ code, level, active, status })
  }

  regionObj[key] = newRegionObj
  data.cityObj = cityObj
  data.areaObj = areaObj
}
/**
 * 数据回显
 */
const reset = () => {
  const resetStatus = obj => {
    for (let key in obj) {
      obj[key] = obj[key].map(region => ({
        ...region,
        status: '',
      }))
    }
    return obj
  }

  const { cityObj, areaObj } = data

  // 省级状态重置
  let objs = resetStatus(regionObj)

  // 市级备份状态重置
  const newCityObj = resetStatus(cityObj)

  // 区级备份状态重置
  const newAreaObj = resetStatus(areaObj)

  // 选中列表重置
  data.regionObj = objs
  data.checkedList = []
  data.cityObj = newCityObj
  data.areaObj = newAreaObj
}
// 根据回填数据设置选中状态
const setBackFillStatus = (arrData = [], code, status) => {
  return arrData.map(region => {
    if (region?.code === code) {
      region['status'] = status
    }
    return region
  })
}
const backFillRegion = () => {
  let { cityObj, areaObj, checkedList } = data
  let oldData = props.oldData
  reset()
  // console.log('原始的数据源', oldData, regionObj, cityObj, areaObj, checkedList)
  nextTick(() => {
    for (let region of oldData) {
      let { level, code, status, parent, isarea } = region
      if (level === '1') {
        // 特殊情况 -第一个单独处理
        if (code === regionObj['provinceList'][0]?.code && status === 'checked') {
          cityObj[code].forEach(item => (item.status = 'checked'))
          regionObj['curCityList'].forEach(item => {
            if (item.parent == code) {
              item.status = 'checked'
            }
          })
          regionObj['provinceList'] = setBackFillStatus(regionObj['provinceList'], code, status)
        } else {
          regionObj['provinceList'] = setBackFillStatus(regionObj['provinceList'], code, status)
        }
      } else if ((level === '2' || (level === '3' && isarea)) && cityObj[parent]?.length) {
        // 特殊情况
        cityObj[parent] = setBackFillStatus(cityObj[parent], code, status)
      } else if (areaObj[parent]?.length) {
        areaObj[parent] = setBackFillStatus(areaObj[parent], code, status)
      }
    }
    checkedList = JSON.parse(JSON.stringify(oldData))
    data.checkedList = checkedList
    data.cityObj = cityObj
    data.areaObj = areaObj
  })
  // 回显永远选中第一个
  let first = regionObj['provinceList'][0]
  if (first) {
    getChildData(first)
  }
}

/**
 * 弹窗回调
 */
const submit = () => {
  let { checkedList } = data
  data.visible = false
  regionObj.curAreaList = []
  emits('submit', { checkedList, mark: props.mark, visible: false })
}
/**
 * 关闭弹窗
 */
const close = () => {
  data.visible = false
  regionObj.curAreaList = []
  emits('close', { visible: false })
}
// 区监听
watch(
  () => regionObj.curAreaList,
  val => {
    changeParentStatus(val, 'area')
  },
  {
    immediate: true,
  }
)

// 市监听
watch(
  () => regionObj.curCityList,
  val => {
    changeParentStatus(val, 'city')
  },
  {
    immediate: true,
  }
)
// 显示隐藏
watch(
  () => [props.visible, props.oldData],
  ([bool]) => {
    data.visible = bool
    if (!bool) return
    bool && backFillRegion()
  },
  {
    immediate: true,
  }
)
</script>
<template>
  <div class="area">
    <div class="region-wrap">
      <!-- 省 -->
      <div class="list province-list">
        <div class="scroll-view" scroll-y="true" style="height: 100%">
          <div
            class="item province"
            :class="{ active: item.active }"
            @click="() => getChildData(item)"
            v-for="item in regionObj.provinceList"
            :key="item.code"
          >
            <div :class="{ checked: item.status }">
              {{ item.name }}
            </div>
            <div :class="['checkbox', item.status]" @click.stop="checkeEvent(item)"></div>
          </div>
        </div>
      </div>

      <!-- 市 -->
      <div class="list city-list">
        <div class="scroll-view" scroll-y="true" style="height: 100%">
          <div
            class="item city"
            :class="{ active: item.active }"
            @click="() => getChildData(item)"
            v-for="item in regionObj.curCityList"
            :key="item.code"
          >
            <div :class="{ checked: item.status }">
              {{ item.name }}
            </div>
            <div :class="['checkbox', item.status]" @click.stop="checkeEvent(item)"></div>
          </div>
        </div>
      </div>

      <!-- 区 -->
      <div class="list area-list">
        <div class="scroll-view" scroll-y="true" style="height: 100%">
          <div class="item area" v-for="item in regionObj.curAreaList" :key="item.code">
            <div :class="{ checked: item.status }">
              {{ item.name }}
            </div>
            <div :class="['checkbox', item.status]" @click.stop="checkeEvent(item)"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="footer">
    <button class="close" @click="close">{{ props.buttomtext }}</button>
    <button class="submit" @click="submit">确认</button>
  </div>
</template>

<style lang="scss" scoped>
// 定义颜色变量
$primary-color: #20263a;
$secondary-color: #3e7bfa;
$border-color: #dedede;
$background-color: #fff;
$disabled-color: rgba(247, 247, 247, 0.6);

// 定义尺寸变量
$font-size: 27px;
$checkbox-size: 29px;
$checkbox-border-radius: 6px;
$checkbox-margin-left: 20px;
$list-width: 250px;
$list-padding: 30px 31px 30px 35px;

.region-wrap {
  height: 712px;
  width: 100%;
  display: flex;
  color: $primary-color;
  font-size: $font-size;

  .checked {
    color: $secondary-color;
  }
  .checkbox.checked {
    width: 32px;
    height: 33px;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABSVJREFUWMN1Vz9v3EUQfXP3i32XlAlEfIngQAQI4QqBFCIq+kCkoBgi0VA534IAiSMKhPgC2A1pSQMIkogGiQoXFIgQoRR27DvfexSzM7t7GEvn+/3Zm9mZee/NrF26q0sCvqRwRgJEmgRIJknw6/Kd7yC/lxGQGOvatTJf17yjTBQISNJjQe/ZW3f1F4UzoKzYQTXYfC9kEkBAoEwwiTTBRNK6Dcc9ZZLga4pxCCQAARQfDxm5/9hEwG/bnQOESaIJBt9jFykkmTIb5hkCIPjG4k9sA7PTgyR/WBYKdQGpshuUfx41skSQUDbuzjPFiI2Vn6Ytqq4FNHgEbpgwN5lOgYi0XkdEqpHUjEmRonzuzjPL5ZlvAhgkK1bLmx4DJjXRsDHQlqAYa2oOuhfHVQK0te1YGdqdC0CCMZ7TQSS4YS9ZcdpEkgzJmhcc5POyboktg+QO25x1Oz6OFVFDAWwz1JSsZYBEKxeV3g0GAFhijR2dVMoQlAv+Vy1Ag1PGPWmAqQF0AV2DtZKpEVU4upBp4SjuaxzgMXWRZbQCF4kRC0ekkqLvv7hip09a2kQER2AEylAUrRMiNqgNpyVyFucoDmFNSaCGzsDmayv28asTfP3OKZw5aTWzxebgumPIHy1c3ZaFKIWmx4k6dCPUz7O1ub6Kd9dWAQB/PCGe7DN0wplKJQsaJzXVIwBHQT3AqvaHBXeGdFpBtrm+auH8u9/n+GBnX0+PXBohGQsmR2oQ7KmhibSr5wbceuMEJoOJ5V1VOIW4SCrNBYHsJee7c2zs7OtgXkoWcZbyjY4D2vlnR7h6bsDa2TFuvn7CpmMYE3j/ERbzTbvxG+uTTPu93SNtbO/rcNZoBGUUo/FpVOtdpfX+nwt88cuRAGDt7BifvrmC6bhtx1rCgadzc31il59fsUj7te09HMyVUk3SmdMgfqQFMzo19Nh6OMedB3MAwPmzY9y6ONV0bLX7OZJTsj3ylVrzcN40tjpnVIEbCb3Ws2HA1sM5th7M4JkY2a2LE0wHkzrlAzbXJ7i85pHf2z3Sxs6+nh7nvEp2KQWQGCAFUoZQqqJ8t+/PtfVgLgB44bkxti5NbDquGLixvmpt5Ne293A4K1RmNy9UVS2lA4ERFzQCQrZrX8yYaCj7/KcZbv88y03ceXuK6Vi2ueR8Y9vRzmhCaEoV8g2Fghol2IWvnjK7lqN0aRyrI9aHF07Y9Zcc4Y/2iGdOjTrnh3PPXipdNy/KyDKVUaBkEDS03KxcLlRbOK+DKZ/9eAgIuv7yqnXOd/Z0cISSV7QdMzsqS9MDaS4lbnMIZeuc13FKZaMxYOCTHw5sIeGjVyYBOBzMI2OmiKTp++bKWQdT/9BE03C8tnsPz/YaI1nJzM3vD7H7D/HtbzM7OCrDC7tOGSN5pl2VCRJoIX5DfdFFr6CKt4B+moWAb36dpRSn82Z+zUwghox2IqpUH3JibQZLT7f5o8SEgLY0DNB4lMjsKQ82VIqV+nE8zg0+knlnW8hkTsOcgLk0y6ECFarRuJYFcoveQ8efjgIrhXUDYw4rqPXh5H/oqNZofZbO3Q5YN62u1SfOqiCVmbCmvT/rdSOUNT2rO0N6CitI46JPezfMKFg1oB4wDe3BQc2JBmEgz3wN5RzRqab1nGhLTou9nlkjyh5FfZn6TauhVnrlAFtkNJYwT0BBr0K3en4retAcl9zG3yOQV0A9jmkzx+eu9g4sVCERPDuW54KF6nlRzTG+lkOx8bL5R4Ku/AtTMPjHM+h3JAAAAABJRU5ErkJggg==');
    background-size: 100% 100%;
    border: none;
  }
  .checkbox.semi-checked {
    width: 32px;
    height: 33px;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAA+9JREFUGBkFwTHRGFQUhcHvMq+gJwpwgA1iIgjCAtiIB5wQMkNF959l9z5/9Vn9OX2ibEdxEIUo21GEMrdioihEmSOIosyZVvBdvtyvX/09fWpuojJRiEI+HLXSnI7tdLYdB1G245hDOpuo0lap2fc3faIQnZWCKMpqHXa6KiPKHIVTTBxKpYOqqqwQxf30kJUOUiFqU4qK6tguxSESnWLOtNIcpaI2pcwp5pRnjmO7dSKKSo2o1IiDSohSEKGrohRqE4UUSrF6nDgKVZkoHGmOMikUoswpVuaQrlG6CUVRJoqy3UOETtXcBBGTjqQLmVMoxcp2lA6qQ9mOUqwoClEPN4lSiKIolYlClGKOmjRHUSPKHNJhlw5xkDnlmepSVHMQZcJxbKdQVoRKKaqplO3qqBAxp2O7deqgNwc1RxEVoqwkHWSOUKPUPlwXcykdE04Hh1RUlYl6zUUrlMpEIcWcYkWmUHNUF3OUoFQUstKhKGROeSt1IZWPnQ6iKCucQiGKUJRJxxyHIuYUc5RpVcXcQ5QVTocyZ1Ip6sytWFXhOM1xEAeZo5Stckbp4tYhz5wOZcJROnI6o6aV5ghFkCKZc6EoxVxCNadwxZkeRSEdHIWs/vry41VVVVdVXVVVVVVVVXVVVdX9/Pu/mtPRsd2Io3o6CFEU4kJVVVVVVVVVVVVVVVVVVVVVVAVtQlZI9XzsVlKUiVpK/fLHf1qNcFaKiaJwZFMVZW6CUpsoDk5lekpzOqZ1stNFpTaHKIIj1JQpnWKFTCrKRFmx4+Amj7JaMhet03Y6SKFTKEJRUWpSRTWnY8IRRMehSKu3j51OSqGTODiTjmKlMqeYWyFVmyiKnY6Joog0FTd5OpTtdAiFiC7bcWyns5Ki0pmU5nSIdRKhcFtUTaPUa07hSOYUyoeTdBClak5nYk6hIhWlmCjVprrabg4HvRUryZxijlK4pFFqdiQ7ThcrxMERisJZ1dlEUezsPApRhOKwm0pRqRWKIrbTQVbmEB3BbVGZKIt2VuiZKHOKOULM1ZmyQlSK0kFW5hQVhSOq5ogVxUHUozZVEBnlKswpqDlKMUfitiQUjuKMTMQKUew4Vg9V+XAuU6mRlWJF6UjmUhSnNIVyzC2ZI4iiEMd2Om+qOcXU3DomClEUMkcoCk2h5lQrVoSywlEEx6GeqTLKtWKiEJUyRwhlO4pTlVqhWiErRBEKkVHqNbdiLlIoitpURdhxTBwcO6uVlO04OEIhitoqRaV+mPtG6axjZTuEUubQnE24EaFWqG1nRVlwiMKZUihu/PND22/N98hkTgdRFMdcE0VSm5s050Mcwo4iVsiCG43om/z2P5BL6iIzKxw2AAAAAElFTkSuQmCC');
    background-size: 100% 100%;
    border: none;
  }

  .checkbox {
    flex-shrink: 0;
    width: $checkbox-size;
    height: $checkbox-size;
    border: 1px solid $border-color;
    border-radius: $checkbox-border-radius;
    position: relative;
    margin-left: $checkbox-margin-left;
  }

  .list {
    width: $list-width;
    height: 100%;
    scroll-behavior: smooth;
    overflow-y: scroll;
    /* 隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
    .item {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $list-padding;
    }
  }

  &.province-list {
    background-color: $disabled-color;

    &.active {
      background-color: $background-color;
    }
  }

  &.city-list {
    background-color: $background-color;

    &.active {
      background-color: $background-color;
    }
  }

  &.area-list {
    background-color: $background-color;
  }
}
.footer {
  width: 100%;
  display: flex;
  padding: 10px 0;
  justify-content: space-between;

  button {
    width: 340px;
    height: 80px;
    line-height: 80px;
    border: none !important;
    padding: 0;
    font-size: 32px;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 400;
    text-align: CENTER;
    color: #74798c;
    margin: 0 12px 10px;
    background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
    border-radius: 8px;
    &.close {
      background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
      color: #74798c;
    }
    &.submit {
      background: linear-gradient(272deg, #076ee4 0%, #53a2ff 100%) !important;
      color: #fff;
    }
  }
}
</style>
