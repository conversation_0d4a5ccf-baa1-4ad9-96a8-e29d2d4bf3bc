/* eslint-disable no-case-declarations */
import { ref, getCurrentInstance, watch } from 'vue'
import { useRoute } from 'vue-router'
import {
  searRadioConstant,
  searMultiSelectConstant,
  handleData,
  getHeightStatus,
  divisionStr,
  setTagStatus,
  searMultPopConstant,
  getNameFromPop,
  params,
  searCheckPopConstant,
  searMultIptConstant,
  searMultAllConstant,
  dateTagList,
} from './huntAbout'
import { clone } from '@/utils/utils'
import { handleSearchHight, debounce } from '@/utils/utils'

export const useMethods = (data, other) => {
  let { emit } = other
  const { proxy } = getCurrentInstance()
  const rightScrollRef = ref(null)
  // request
  const getSearchList = debounce(async ([...val]) => {
    let value = val[0].toString()
    data.searchList = []
    let [err, res] = await proxy.$to('getNameList', value)
    if (err) {
      data.text = '暂无相关内容!'
      return
    }
    if (res.length <= 0) {
      data.text = '请输入更精确的内容!'
      return
    }
    data.searchList = handleSearchHight(res, 'ent_name', data.params.ent_name)
  })
  const getAddressList = debounce(async ([...val]) => {
    let value = val[0].toString()
    data.searchList = []
    let [err, res] = await proxy.$to('getMapSearch', {
      ent_name: value,
      search_address: true,
    })
    if (err) {
      data.text = '暂无相关内容!'
      return
    }
    if (res.length <= 0) {
      data.text = '请输入更精确的内容!'
      return
    }

    const arr = res.items
      .map(item => {
        return {
          ...item,
          ent_address: item.register_address,
        }
      })
      .filter(i => i.ent_address)
    data.searchList = handleSearchHight(arr, 'ent_address', data.params.ent_address)
  })
  // methods
  /**
   * 过滤掉没有用的字段
   */
  const filterEmptyData = obj => {
    const filteredObj = {}
    for (const key in obj) {
      if (
        obj[key] !== null &&
        obj[key] !== undefined &&
        ((typeof obj[key] === 'string' && obj[key].trim() !== '') ||
          (Array.isArray(obj[key]) &&
            obj[key].length > 0 &&
            !(Array.isArray(obj[key]) && obj[key].every(item => item.start === '' && item.end === ''))))
      ) {
        filteredObj[key] = obj[key]
      }
    }
    return filteredObj
  }
  /**
   * 将结果抛出去
   * @param {*} val
   */
  const result = val => {
    let paramsData = val ? val : data.params
    data.isHeight = getHeightStatus(paramsData) //高亮
    paramsData = filterEmptyData(JSON.parse(handleData(paramsData)))
    //把结果抛出去
    emit('submit', {
      isHeight: data.isHeight,
      paramsData: clone(paramsData),
    })
  }
  /**
   * 关闭展开左边
   */
  const closeleft = item => {
    //展开关闭右边
    let leftList = data.leftList
    let { title, isOpen } = item
    leftList.some(item => {
      if (item.title == title) {
        item.isOpen = !isOpen
        return true
      }
    })
  }
  /**
   * 右边单个item点击高亮
   */
  const leftactvie = (item, key) => {
    let leftList = data.leftList
    let { title } = item
    leftList.some(item => {
      if (item.onlyText) {
        item.isActive = false
        item.map.forEach(i => (i.active = false))
      }
      if (item.title == title) {
        item.map?.forEach?.(i => {
          // i.active = false
          if (i.key == key) {
            i.active = true
            item.isActive = true
            data.idName = i.key
            rightScrollRef.value.goTopById(i.key, 1000)
          }
        })
      }
    })
  }
  /**
   * 右边展开收起
   * @param {*} item
   */
  const openright = item => {
    let { type, isOpen } = item
    let itemList = data.itemList
    // console.log(item)
    itemList.some(i => {
      if (i.type == type) {
        i.isOpen = !isOpen
        return true
      }
    })
  }
  /**
   * 选择右边单个item高亮处理
   * @param {*} item
   * @param {*} child
   */
  const selectTag = async (item, child) => {
    let { id, name } = child
    let type = item.type
    // 为了不失去响应，也不想用toRefs
    let itemList = data.itemList,
      params = data.params
    let searMultiSelect = [...searRadioConstant['list'], ...searMultiSelectConstant['list']]
    data.itemList = itemList.map(item => {
      if (item.type === type) {
        item.list = item.list.map(tag => {
          if (searRadioConstant['list'].includes(type)) tag.active = false //单选
          if (tag.id === id) {
            //  if (type === 'phone_data' || type === 'contacts_style')
            if (searRadioConstant['list'].includes(type)) {
              //单选
              if (params[type] == id) {
                tag.active = false
                params[type] = []
              } else {
                tag.active = true
                params[type] = [id] //单选-并且上传数组形式
              }
            }
            if (searMultiSelectConstant['list'].includes(type)) {
              //多选
              let arr = params[type]
              if (arr.includes(id)) {
                arr.splice(
                  arr.findIndex(i => i === id),
                  1
                )
                tag.active = false
              } else {
                //
                arr.push(id) //这里有问题 不应该pushid
                tag.active = true
              }
            }
            if (!searMultiSelect.includes(type)) {
              //有start和end这种
              let obj = divisionStr(id)
              //
              params[type] = params[type].filter(i => !i.special)
              let arr = params[type]
              let idx = -1
              if (type == 'register_time') {
                idx = arr.findIndex(i => i.start === obj.start && i.end === obj.end)
              } else {
                idx = arr.findIndex(i => JSON.stringify(i) == JSON.stringify(obj))
              }
              if (idx >= 0) {
                arr.splice(idx, 1)
                tag.active = false
              } else {
                //
                if (type === 'register_time') {
                  obj['name'] = name
                }
                arr.push(obj) //这里有问题 不应该pushid
                tag.active = true
              }
              if (type === 'register_capital') {
                data.minCapital = data.maxCapital = ''
                data.capitalActive = false
              }
              if (type === 'super_dimension_social_num') {
                data.socialminPeson = data.socialmaxPeson = ''
                data.socialActive = false
              }
              if (type === 'register_time') {
                data.minDate = data.maxDate = ''
                data.dateActive = false
              }
            }
          }
          return tag
        })
      }
      return item
    })
  }

  /**
   * 注册资本输入框获取焦点时清除选中的注册资本标签
   * @param {*} type
   */
  const inputFocus = type => {
    setTagStatus(data, type)
  }
  /**
   * 注册资本输入框变化时，其它对应的变化
   * @param {*} e
   */
  const inputChange = e => {
    let minCapital = data.minCapital,
      maxCapital = data.maxCapital,
      socialmaxPeson = data.socialmaxPeson,
      socialminPeson = data.socialminPeson
    // console.log(minCapital, maxCapital)
    let types = e
    let type = types
    // console.log(type)
    if (types == 'super_dimension_social_num') {
      data.socialActive = socialminPeson !== '' || socialmaxPeson !== ''
      data.params[type] = [
        {
          start: socialminPeson,
          end: socialmaxPeson,
          special: true,
        },
      ]
      result()
    } else if (types == 'register_capital') {
      data.capitalActive = minCapital !== '' || maxCapital !== ''
      data.params[type] = [
        {
          start: minCapital,
          end: maxCapital,
          special: true,
        },
      ]
      result()
    }
  }
  /**
   * 企业名称输入文字
   * @param {*} event
   */
  const onInput = (event, item) => {
    if (item.type === 'ent_name') {
      getSearchList(event.target.value)
    } else {
      getAddressList(event.target.value)
    }
    result()
  }
  /**
   * 企业名称搜索框点击外面关闭
   */
  const closeIptCont = () => {
    data.ent_name_focus = false
    data.address_focus = false
    const activeElement = document.activeElement
    if (activeElement && ['INPUT', 'TEXTAREA'].includes(activeElement.tagName)) {
      activeElement.blur()
    }
  }
  /**
   * 点击企业名称下拉框
   * @param {*} entnames
   */
  const clickItem = (entnames, type) => {
    let str = entnames.join('')

    //id用来判断是不是数据库里面的
    data.params[type] = str
    data[`${type}_focus`] = false
    result()
  }
  /**
   * 时间选择
   * @param {*} type
   */
  const showtDatePicker = type => {
    let date = data.date,
      minDate = data.minDate,
      maxDate = data.maxDate
    // eslint-disable-next-line no-unused-vars
    date = type === 'startDate' ? minDate : maxDate
    data.datePop = true
    data.dateType = type
    console.log(data.dateType)
    // 这里有问题 date没有被使用
  }
  const setDate = dates => {
    let minDate = data.minDate,
      maxDate = data.maxDate,
      name = ''
    if (dates) {
      if (data.dateType === 'startDate') {
        minDate = dates
        data.minDate = dates
      }
      if (data.dateType === 'endDate') {
        maxDate = dates
        data.maxDate = dates
      }
    }
    if (minDate) name = '最低年限' + minDate
    if (maxDate) name += '最高年限' + maxDate
    setTagStatus(data, 'register_time')

    data.dateActive = minDate || maxDate
    data.params['register_time'] = [
      {
        start: minDate,
        end: maxDate,
        special: true,
        name,
      },
    ]
    result()
  }
  /**
   * 地区行业选择弹窗
   */
  const handlePop = item => {
    console.log(item, 'handlePop')
    const { title } = item
    switch (
      title //这里目前写死
    ) {
      case '所在地区':
        data.regionPop = true
        break
      case '所属行业':
        data.eleseicPop = true

        break
      case '企业类型':
        data.enttypePop = true

        break
      case '企业许可':
        data.districtPop = true

        break
      case '所属产业链':
        data.chainCodePop = true
        break

      default:
        break
    }
  }
  /**
   * 地区弹窗回调
   * @param {visible,mark,checkedList} obj
   */
  const submitSub = items => {
    const { itemList } = data
    let { checkedList: obj, mark } = items
    let str = searMultPopConstant[mark]['data']
    // if (obj.length < 0) return
    let name = getNameFromPop(obj)
    itemList.forEach(item => item.type == mark && (item.content = name))
    let arr = ['chainCodePop', 'enttypePop', 'regionPop', 'eleseicPop']
    arr.forEach(i => {
      data[i] = false
    })
    data['itemList'] = itemList
    data.params[str] = obj
    // 这里应该要过滤数据得 目前只考虑为空得情况
    data.params[mark] = obj?.length ? data.params[mark] : []
    result()
    // console.log(data)
  }
  /**
   *
   * @param {data} 清空所有数据
   * @returns
   */
  const clearSear = () => {
    // 后面copy组件的时候renderList名字会跟着换
    let list = data.renderList.map(item => {
      if (item.isOpenIcon) {
        item.isOpen = data.itemList.filter(i => item.type === i.type)[0]?.isOpen
      }
      return item
    })
    data.itemList = JSON.parse(JSON.stringify(list))
    data.params = JSON.parse(JSON.stringify(params))
    data.minCapital = ''
    data.maxCapital = ''
    data.minDate = ''
    data.maxDate = ''
    data.date = ''
    data.capitalActive = false
    data.socialActive = false
    data.socialminPeson = ''
    data.socialmaxPeson = ''
    data.dateActive = false
    data.dateType = ''
  }
  /**
   * 回显数据
   * @param {*} tempObj
   */
  const setBackfillData = (tempObj = {}) => {
    clearSear()
    let {
      itemList,
      params,
      minCapital,
      maxCapital,
      capitalActive,
      socialActive,
      socialminPeson,
      socialmaxPeson,
      minDate,
      maxDate,
      dateActive,
    } = data
    params = Object.assign(params, tempObj)
    // 设置上一页传递过来的企业名称
    const route = useRoute()
    // let { ent_name } = route.query
    params.ent_name = route?.query.ent_name || params.ent_name || ''
    itemList = itemList.map(item => {
      for (let key in params) {
        if (item.type === key) {
          let tagId = ''
          // 根据数据类型设置对应的tagId
          switch (item.type) {
            case 'register_capital': //  注册资本
              let strs = params[key].length ? params[key][0].start + '$' + params[key][0].end : ''
              let tagIdArr = ['0$100', '100$200', '500$1000', '1000$5000', '5000$'] //这里后续要弄成三元 还有其它形式
              // 如果返回的注册资本的数据不是tag存在的数据则设置给input组件
              if (!tagIdArr.includes(strs)) {
                minCapital = params[key].length ? params[key][0].start : ''
                maxCapital = params[key].length ? params[key][0].end : ''

                // 只要input存在数据，则设置为活动状态
                if (minCapital || maxCapital) capitalActive = true
                tagId = strs
              } else {
                tagId = params[key]
              }
              //
              break
            case 'super_dimension_social_num': //  注册资本
              let strss = params[key].length ? params[key][0].start + '$' + params[key][0].end : ''
              let tagIdArrs = ['0$49', '50$99', '100$499', '500$999', '1000$4999', '5000$'] //这里后续要弄成三元 还有其它形式
              // 如果返回的注册资本的数据不是tag存在的数据则设置给input组件
              if (!tagIdArrs.includes(strss)) {
                socialminPeson = params[key].length ? params[key][0].start : ''
                socialmaxPeson = params[key].length ? params[key][0].end : ''

                // 只要input存在数据，则设置为活动状态
                if (socialminPeson || socialmaxPeson) socialActive = true
                tagId = strss
              } else {
                tagId = params[key]
              }
              //
              break

            case 'register_time': //  注册时间
              let str = params[key].length ? params[key][0].start + '$' + params[key][0].end : ''
              // 如果返回的注册时间的数据不是tag存在的数据则设置给input组件
              if (!dateTagList.includes(str)) {
                minDate = params[key].length ? params[key][0].start : ''
                maxDate = params[key].length ? params[key][0].end : ''

                // 只要input存在数据，则设置为活动状态
                if (minDate || maxDate) dateActive = true
                tagId = str
              } else {
                tagId = params[key]
              }
              break
            // case 'ent_scale': //企业规模
            //     tagId = params[key];
            // case 'capital_event': // --这个目前没有，不知道渲染起没有
            //     tagId = params[key];
            //     break;
            // case 'shit_type':
            //     tagId = params[key];
            //     break;
            // case 'ent_status':
            //     tagId = params[key];
            //     break;
            // case 'phone_data': // 手机号码
            //     tagId = params[key];
            //     break;
            // case 'contacts_style': // 联系电话
            //     tagId = params[key];
            //     break;
            // case 'technology_types': // 联系电话
            //     tagId = params[key];
            //     break;
            default:
              tagId = params[key]
              break
          }
          // 设置地区-行业-企业类型，企业许可
          if (Object.keys(searMultPopConstant).includes(key)) {
            let obj = params[searMultPopConstant[key]['data']]

            if (obj?.length < 0) return
            item.content = getNameFromPop(obj)
          } else if (searRadioConstant['list'].includes(key) || searMultiSelectConstant['list'].includes(key)) {
            // console.log(tagId)1
            // 通过tagId比对判断是否选中
            item.list = item.list.map(tag => {
              const isArray = Array.isArray(tagId) // 判断是否是联系方式的回填数据
              if (isArray) {
                for (let number of tagId) {
                  if (tag.id === number) {
                    tag.active = true
                  }
                }
              } else {
                if (tag.id === tagId) {
                  tag.active = true
                }
              }
              return tag
            })
          } else if (searMultAllConstant['list'].includes(key)) {
            item.list = item.list.map(tag => {
              Array.isArray(tagId) &&
                tagId.length > 0 &&
                tagId.forEach((itm, idx) => {
                  let ids = params[key][idx].start + '$' + params[key][idx].end
                  if (tag.id == ids) tag.active = true
                })
              return tag
            })
          }
        }
      }
      return item
    })
    // console.log(params)
    data.params = params
    data.itemList = itemList
    data.minCapital = minCapital
    data.maxCapital = maxCapital
    data.capitalActive = capitalActive
    data.socialActive = socialActive
    data.socialminPeson = socialminPeson
    data.socialmaxPeson = socialmaxPeson
    data.minDate = minDate
    data.maxDate = maxDate
    data.dateActive = dateActive
  }
  /**
   *校验自定义输入的情况
   * @param {} paramsData
   * @returns
   */
  const checkoutSear = function (paramsData = {}) {
    let list = [...searCheckPopConstant['list'], ...searMultIptConstant['list']]
    let bool = Object.keys(paramsData).some(i => {
      if (list.includes(i) && paramsData[i].length == 1) {
        //只考虑自定义 ，所以数组只会有一个
        if (searCheckPopConstant['list'].includes(i)) {
          let minDate = paramsData[i][0]?.start
          let maxDate = paramsData[i][0]?.end
          let startTime = new Date(minDate).getTime()
          let endTime = new Date(maxDate).getTime()
          if (startTime >= endTime) {
            proxy.$showToast(
              `${searCheckPopConstant['tip'][i]['min']}不能大于等于${searCheckPopConstant['tip'][i]['max']}`
            )
            return true
          }
        } else if (searMultIptConstant['list'].includes(i)) {
          if (parseFloat(paramsData[i][0]?.start) >= parseFloat(paramsData[i][0]?.end)) {
            proxy.$showToast(
              `${searMultIptConstant['tip'][i]['min']}不能大于等于${searMultIptConstant['tip'][i]['max']}`
            )
            return true
          }
        }
      }
    })
    return !bool
  }
  watch(
    () => data.params,
    val => {
      result(val)
    },
    {
      deep: true,
    }
  )
  return {
    rightScrollRef,
    closeleft,
    leftactvie,
    openright,
    selectTag,
    inputFocus,
    closeIptCont,
    inputChange,
    onInput,
    clickItem,
    showtDatePicker,
    setDate,
    handlePop,
    submitSub,
    clearSear,
    setBackfillData,
    checkoutSear,
    filterEmptyData,
  }
}
