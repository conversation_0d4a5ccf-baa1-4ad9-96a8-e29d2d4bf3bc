import { createApp, nextTick } from 'vue'
// import { renderWithQiankun, qiankunWindow } from 'vite-plugin-qiankun/dist/helper'
import AMapLoader from '@amap/amap-jsapi-loader'
import { showToast, showLoadingToast, closeToast } from 'vant'
import svgIcon from '@/components/SvgIcon/index.vue'
import App from '@/App.vue'
import { routerHandler } from '@/router'
// import router from '@/router'
import { setupStore } from '@/store'
import { to } from '@/utils/utils'
import { getUrlKey } from '@/utils'
import HImage from '@/components/Common/Image/index'
import Scroll from '@/components/Common/Scroll/index'
import mitt from 'mitt'
import { useUserStore } from '@/store/modules/user'

import 'tailwindcss/tailwind.css'
import '@/assets/styles/tailwind.css'
import 'vant/lib/index.css'
import '@/assets/styles/index.scss'

let app
export let router
const { VITE_APP_NAME } = import.meta.env
function getDefaultChainHandler({ detail }) {
  console.log('----from-main', detail)
  nextTick(() => {
      const { name, code } = detail
      useUserStore().setChain({label: name, value: code})
  })
}

app = createApp(App)
app.config.globalProperties.$showToast = options => {
  if (typeof options === 'string') {
    showToast({
      message: options,
      className: 'global-toast',
    })
  } else {
    showToast({
      ...options,
      className: options.className + ' global-toast',
    })
  }
}
// 高德地图
AMapLoader.load({
  key: '1a8cc229f92dd0abd502d13c0b356e3e', // 申请好的Web端开发者Key，首次调用 load 时必填
  version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
  plugins: ['AMap.Geolocation', 'AMap.ControlBar', 'AMap.Geocoder', 'AMap.MouseTool'], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  AMapUI: {
    //是否加载 AMapUI，缺省不加载
    version: '1.1', //AMapUI 版本
    plugins: ['overlay/SimpleMarker', 'misc/PoiPicker', 'geo/DistrictExplorer'], //需要加载的 AMapUI ui 插件
  },
})

/**
 *
 * @param {obj||String} options
 * @param {number 禁用} forbidClick
 * @param {*} duration
 */
app.config.globalProperties.$loading = (options, forbidClick = true, duration = 0) => {
  if (typeof options === 'string') {
    //说明不是对象
    showLoadingToast({ message: options, forbidClick, duration, loadingType: 'spinner' })
  } else {
    showLoadingToast({ ...options, loadingType: 'spinner' })
  }
}
app.config.globalProperties.$close = closeToast
app.config.globalProperties.$to = to
app.config.globalProperties.emitter = mitt()
router = routerHandler()
setupStore(app)
app.component('SvgIcon', svgIcon)
  .use(router)
  .use(HImage)
  .use(Scroll)
  .mount('#app')

