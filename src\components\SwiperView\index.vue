<template>
    <div class="swiper-view">
        <van-swipe :autoplay="autoplay" :loop="loop" @change="swiperChange">
            <van-swipe-item v-for="(item, index) in images" :key="index">
                <img :src="item.images" class="main" />
                <img :src="getTypeImageUrl(item.industry_news_type)" class="label" />
                <div class="title">{{ item.title }}</div>
            </van-swipe-item>
        </van-swipe>
    </div>
</template>
<script setup>
import { onMounted } from 'vue'

const props = defineProps({
    autoplay: {
        type: Number,
        default: 3000
    },
    loop: {
        type: Boolean,
        default: true
    },
    images: {
        type: Array,
        default() {
            return []
        }
    }
})
const getTypeImageUrl = v => new URL(`../../assets/images/info/${v}.png`, import.meta.url).href
const swiperChange = item => {
    // console.log('swiperChange-item', item)
}

onMounted(() => {
    // console.log('images', props.images)
})
</script>
<style lang="scss" scoped>
.swiper-view {
    margin: 0 -24px;
    height: 340px;
    width: 750px;
    .van-swipe-item {
        padding: 0 24px;
        height: 340px;
        position: relative;
        .main {
            display: block;
            width: 100%;
            height: 100%;
        }
        .label {
            position: absolute;
            width: 152px;
            height: 56px;
            left: 6px;
            top: 24px;
        }
        .title {
            position: absolute;
            padding: 0 24px;
            left: 24px;
            bottom: 0;
            width: 702px;
            height: 64px;
            line-height: 64px;
            font-size: 28px;
            font-weight: 400;
            color: #ffffff;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 0 0 4px 4px;
        }
    }
}
:deep(.van-swipe__indicators) {
    left: inherit;
    right: 0;
    .van-swipe__indicator {
        background: rgba(255, 255, 255, 0.5);
        &--active {
            background: #fff;
        }
    }
}
</style>
