export const asideOptionsOne = [
  // chain_codes
  {
    title: '产业链环节',
    hideTitle: true,
    isBorder: true,
    keys: 'chain_codes',
    map: {
      chain_codes: '产业链环节',
    },
  },
  // {
  //   title: '所属行业',
  //   hideTitle: true,
  //   keys: 'trade_types',
  //   map: {
  //     trade_types: '所属行业',
  //   },
  // },
  {
    title: '注册资本',
    hideTitle: true,
    keys: 'register_capital',
    map: {
      register_capital: '注册资本',
    },
  },
  {
    title: '成立年限',
    hideTitle: true,
    keys: 'register_time',
    map: {
      register_time: '成立年限',
    },
  },
  {
    title: '企业状态',
    hideTitle: true,
    keys: 'ent_status',
    map: {
      ent_status: '企业状态',
    },
  },
  {
    title: '企业类型',
    hideTitle: true,
    keys: 'ent_type',
    map: {
      ent_type: '企业类型',
    },
  },
  {
    title: '疑似扩张',
    isBorder: true,
    hideTitle: true,
    keys: 'expand_status',
    map: {
      expand_status: '疑似扩张',
    },
  },
  {
    title: '企业规模',
    hideTitle: true,
    isBorder: true,
    keys: 'ent_scale',
    map: {
      ent_scale: '企业规模',
    },
  },
  {
    title: '效益评估',
    hideTitle: true,
    isBorder: true,
    keys: 'benefit_assess',
    map: {
      benefit_assess: '效益评估',
    },
  },
  {
    title: '科技型企业',
    hideTitle: true,
    isBorder: true,
    keys: 'technology_types',
    map: {
      technology_types: '科技型企业',
    },
  },
  {
    title: '融资信息',
    hideTitle: true,
    keys: 'financing_info',
    map: {
      financing_info: '融资信息',
    },
  },
  {
    title: '上市状态',
    hideTitle: true,
    keys: 'listed_status',
    map: {
      listed_status: '上市状态',
    },
  },
  {
    title: '失信信息',
    hideTitle: true,
    keys: 'untrustworthy_info_data',
    map: {
      untrustworthy_info_data: '失信信息',
    },
  },
  {
    title: '专利信息',
    hideTitle: true,
    keys: 'super_dimension_patent',
    map: {
      super_dimension_patent: '专利信息',
    },
  },
  {
    title: '裁判文书',
    hideTitle: true,
    keys: 'judgment_doc_data',
    map: {
      judgment_doc_data: '裁判文书',
    },
  },
  {
    title: '行政处罚',
    hideTitle: true,
    keys: 'adminstrative_penalties_data',
    map: {
      adminstrative_penalties_data: '行政处罚',
    },
  },
  {
    title: '动产抵押',
    hideTitle: true,
    keys: 'chattel_mortage_data',
    map: {
      chattel_mortage_data: '动产抵押',
    },
  },
  {
    title: '经营异常',
    hideTitle: true,
    keys: 'abnormal_operation_data',
    map: {
      abnormal_operation_data: '经营异常',
    },
  },
]
export const asideOptionsTwo = [
  {
    title: '所属行业',
    hideTitle: true,
    keys: 'trade_types',
    map: {
      trade_types: '所属行业',
    },
  },
  {
    title: '注册资本',
    hideTitle: true,
    keys: 'register_capital',
    map: {
      register_capital: '注册资本',
    },
  },
  {
    title: '成立年限',
    hideTitle: true,
    keys: 'register_time',
    map: {
      register_time: '成立年限',
    },
  },
  {
    title: '联系电话', // pc名字是联系电话 高级搜索哪里这里是固定电话 -这里有点出入后续测试看到再说
    hideTitle: true,
    keys: 'tel_data',
    map: {
      tel_data: '联系电话',
    },
  },
]
export const asideOptionsThree = [
  {
    title: '产业链环节',
    hideTitle: true,
    keys: 'chain_codes',
    map: {
      chain_codes: '产业链环节',
    },
  },
  {
    title: '所属行业',
    hideTitle: true,
    keys: 'trade_types',
    map: {
      trade_types: '所属行业',
    },
  },
  {
    title: '注册资本',
    hideTitle: true,
    keys: 'register_capital',
    map: {
      register_capital: '注册资本',
    },
  },
  {
    title: '成立年限',
    hideTitle: true,
    keys: 'register_time',
    map: {
      register_time: '成立年限',
    },
  },
  {
    title: '企业状态',
    hideTitle: true,
    keys: 'ent_status',
    map: {
      ent_status: '企业状态',
    },
  },
  {
    title: '企业类型',
    hideTitle: true,
    keys: 'ent_type',
    map: {
      ent_type: '企业类型',
    },
  },
  {
    title: '疑似扩张',
    isBorder: true,
    hideTitle: true,
    keys: 'expand_status',
    map: {
      expand_status: '疑似扩张',
    },
  },
  {
    title: '企业规模',
    hideTitle: true,
    isBorder: true,
    keys: 'ent_scale',
    map: {
      ent_scale: '企业规模',
    },
  },
  {
    title: '效益评估',
    hideTitle: true,
    isBorder: true,
    keys: 'benefit_assess',
    map: {
      benefit_assess: '效益评估',
    },
  },
  {
    title: '科技型企业',
    hideTitle: true,
    isBorder: true,
    keys: 'technology_types',
    map: {
      technology_types: '科技型企业',
    },
  },
  {
    title: '融资信息',
    hideTitle: true,
    keys: 'financing_info',
    map: {
      financing_info: '融资信息',
    },
  },
  {
    title: '上市状态',
    hideTitle: true,
    keys: 'listed_status',
    map: {
      listed_status: '上市状态',
    },
  },
  {
    title: '失信信息',
    hideTitle: true,
    keys: 'untrustworthy_info_data',
    map: {
      untrustworthy_info_data: '失信信息',
    },
  },
  {
    title: '专利信息',
    hideTitle: true,
    keys: 'super_dimension_patent',
    map: {
      super_dimension_patent: '专利信息',
    },
  },
  {
    title: '裁判文书',
    hideTitle: true,
    keys: 'judgment_doc_data',
    map: {
      judgment_doc_data: '裁判文书',
    },
  },
  {
    title: '行政处罚',
    hideTitle: true,
    keys: 'adminstrative_penalties_data',
    map: {
      adminstrative_penalties_data: '行政处罚',
    },
  },
  {
    title: '动产抵押',
    hideTitle: true,
    keys: 'chattel_mortage_data',
    map: {
      chattel_mortage_data: '动产抵押',
    },
  },
  {
    title: '经营异常',
    hideTitle: true,
    keys: 'abnormal_operation_data',
    map: {
      abnormal_operation_data: '经营异常',
    },
  },
]

export const asideOptionsChanye = [
  // chain_codes
  // {
  //   title: '产业链环节',
  //   hideTitle: true,
  //   isBorder: true,
  //   keys: 'chain_codes',
  //   map: {
  //     chain_codes: '产业链环节',
  //   },
  // },
  {
    title: '所属行业',
    hideTitle: true,
    keys: 'trade_types',
    map: {
      trade_types: '所属行业',
    },
  },
  {
    title: '注册资本',
    hideTitle: true,
    keys: 'register_capital',
    map: {
      register_capital: '注册资本',
    },
  },
  {
    title: '成立年限',
    hideTitle: true,
    keys: 'register_time',
    map: {
      register_time: '成立年限',
    },
  },
  {
    title: '企业状态',
    hideTitle: true,
    keys: 'ent_status',
    map: {
      ent_status: '企业状态',
    },
  },
  {
    title: '企业类型',
    hideTitle: true,
    keys: 'ent_type',
    map: {
      ent_type: '企业类型',
    },
  },
  {
    title: '疑似扩张',
    isBorder: true,
    hideTitle: true,
    keys: 'expand_status',
    map: {
      expand_status: '疑似扩张',
    },
  },
  {
    title: '企业规模',
    hideTitle: true,
    isBorder: true,
    keys: 'ent_scale',
    map: {
      ent_scale: '企业规模',
    },
  },
  {
    title: '效益评估',
    hideTitle: true,
    isBorder: true,
    keys: 'benefit_assess',
    map: {
      benefit_assess: '效益评估',
    },
  },
  {
    title: '科技型企业',
    hideTitle: true,
    isBorder: true,
    keys: 'technology_types',
    map: {
      technology_types: '科技型企业',
    },
  },
  {
    title: '融资信息',
    hideTitle: true,
    keys: 'financing_info',
    map: {
      financing_info: '融资信息',
    },
  },
  {
    title: '上市状态',
    hideTitle: true,
    keys: 'listed_status',
    map: {
      listed_status: '上市状态',
    },
  },
  {
    title: '失信信息',
    hideTitle: true,
    keys: 'untrustworthy_info_data',
    map: {
      untrustworthy_info_data: '失信信息',
    },
  },
  {
    title: '专利信息',
    hideTitle: true,
    keys: 'super_dimension_patent',
    map: {
      super_dimension_patent: '专利信息',
    },
  },
  {
    title: '裁判文书',
    hideTitle: true,
    keys: 'judgment_doc_data',
    map: {
      judgment_doc_data: '裁判文书',
    },
  },
  {
    title: '行政处罚',
    hideTitle: true,
    keys: 'adminstrative_penalties_data',
    map: {
      adminstrative_penalties_data: '行政处罚',
    },
  },
  {
    title: '动产抵押',
    hideTitle: true,
    keys: 'chattel_mortage_data',
    map: {
      chattel_mortage_data: '动产抵押',
    },
  },
  {
    title: '经营异常',
    hideTitle: true,
    keys: 'abnormal_operation_data',
    map: {
      abnormal_operation_data: '经营异常',
    },
  },
]
