<script setup>
import { reactive, watch } from 'vue'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  isFooter: {
    type: Boolean,
    default: true,
  },
  confirmBtnText: {
    type: String,
    default: '确定',
  },
  cancelBtnText: {
    type: String,
    default: '取消',
  },
  position: {
    type: String,
    default: 'bottom',
  },
  cls: {
    type: String,
  },
})

const emits = defineEmits(['close', 'submit'])
const data = reactive({
  show: false,
})
watch(
  () => props.visible,
  val => {
    data.show = val
  }
)

/**
 * 关闭触发回调
 */
const close = () => {
  data.show = false //应该交给外面控制
  emits('close')
}
/**
 * 确定回调
 */
const submit = () => {
  data.show = false
  emits('submit')
}
</script>
<template>
  <van-popup
    teleport="#app"
    v-model:show="data.show"
    v-bind="$attrs"
    :position="position"
    @closed="close"
    safe-area-inset-bottom
    @click-overlay.prevent.stop
  >
    <div :class="['popupWrap', cls]" @click.prevent.stop>
      <slot></slot>
      <slot name="foot" v-if="isFooter">
        <div class="footer">
          <button class="close" @click="close">{{ cancelBtnText }}</button>
          <button class="submit" @click="submit">{{ confirmBtnText }}</button>
        </div>
      </slot>
    </div>
  </van-popup>
</template>

<style lang="scss" scoped>
:deep(.van-popup) {
  border-radius: 16px 16px 0px 0px !important;
  box-shadow: 0px 4px 17px 0px rgba(221, 221, 221, 0.5);
  overflow: hidden;
  bottom: 0;
  left: 0;
  width: 100%;
}
.popupWrap {
  .footer {
    width: 100%;
    display: flex;
    padding: 10px 0;
    justify-content: space-between;

    button {
      width: 340px;
      height: 80px;
      line-height: 80px;
      border: none !important;
      padding: 0;
      font-size: 32px;
      font-family: PingFang SC, PingFang SC-Semibold;
      font-weight: 400;
      text-align: CENTER;
      color: #74798c;
      margin: 0 12px 50px;
      background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
      border-radius: 8px;
      &.close {
        background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
        color: #74798c;
      }
      &.submit {
        background: linear-gradient(272deg, #076ee4 0%, #53a2ff 100%) !important;
        color: #fff;
      }
    }
  }
}
</style>
