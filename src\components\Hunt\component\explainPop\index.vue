<script setup name="ExplainPop">
import { reactive, watch } from 'vue'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['close', 'submit'])
const data = reactive({
  show: props.visible,
})
watch(
  () => props.visible,
  val => {
    data.show = val
  }
)

/**
 * 关闭触发回调
 */
const close = () => {
  data.show = false //应该交给外面控制
  emits('close')
}
/**
 * 确定回调
 */
const submit = () => {
  data.show = false
  emits('submit')
}
</script>
<template>
  <div class="popwrap">
    <!--     @opened="opened" -->
    <van-popup
      v-model:show="data.show"
      position="bottom"
      @closed="close"
      safe-area-inset-bottom
      @click-overlay.prevent.stop
      @click.prevent.stop
      teleport="#app"
    >
      <div class="title">提示</div>
      <div class="popupWrap">
        <div class="content">
          根据不同招商场景，对企业规模进行的等级划分，大型、特大型企业适合政府园区招商；中小型、中型企业适合民营园区招商；微型、小微型企业可能存在高成长性企业
        </div>

        <div class="footer">
          <button class="close" @click="submit">知道了</button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style lang="scss" scoped>
:deep(.van-popup) {
  border-radius: 16px 16px 0px 0px !important;
  box-shadow: 0px 4px 17px 0px rgba(221, 221, 221, 0.5);
  overflow: hidden;
  bottom: 0;
  left: 0;
  width: 100%;
}
.title {
  padding: 28px 32px 28px;
  display: flex;
  justify-content: center;
  position: relative;
  font-size: 32px;
  font-weight: 600;
  color: #20263a;
}
.popupWrap {
  font-size: 28px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263a;
  .content {
    padding: 48px 32px 60px;
    border-bottom: 1px solid #f7f7f7;
  }
  .footer {
    width: 100%;
    display: flex;
    padding: 10px 0;
    justify-content: space-between;

    button {
      width: 100%;
      height: 80px;
      line-height: 80px;
      border: none !important;
      padding: 0;
      font-size: 32px;
      font-family: PingFang SC, PingFang SC-Semibold;
      font-weight: 400;
      text-align: CENTER;
      color: #74798c;
      margin: 0 12px 50px;
      background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
      border-radius: 8px;
      &.close {
        background: linear-gradient(90deg, rgba(238, 238, 238, 1), rgba(245, 245, 245, 1)) !important;
        color: #74798c;
      }
    }
  }
}
</style>
