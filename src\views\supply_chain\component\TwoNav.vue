<template>
  <div class="w-full h-104 bg-white border-b border-solid border-[#eee] f-all-center">
    <div class="w-[536px] h-72 bg-[#F4F4F4] p-6 flex rounded-14">
      <div
        v-for="item in list"
        :key="item.id"
        class="flex-1 w-262 h-60 rounded-8 f-all-center text-30"
        :class="curIdx === item.id ? 'bg-white text-#20263A' : 'text-#525665'"
        @click="onChange(item.id)"
      >
        {{ item.name }}
      </div>
    </div>
  </div>
</template>

<script setup>
const { proxy } = getCurrentInstance()
const list = [
  { name: '单链主招商', id: 'a' },
  { name: '多链主招商', id: 'b' },
]
// 这里的 curIdx 应该是接收父组件传来的值
const props = defineProps({
  curIdx: {
    type: String,
  },
})
const emits = defineEmits(['update:curIdx'])
const onChange = curIdx => {
  if (curIdx === 'b' && !window.sessionStorage.getItem('preset')) {
    proxy.$showToast('请前往设置招商预设！')
    return
  }
  emits('update:curIdx', curIdx)
}
</script>

<style lang="scss" scoped></style>
