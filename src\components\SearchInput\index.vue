<template>
  <div ref="inputRef" class="input-container">
    <van-field
      ref="iptRef"
      v-model="inputValue"
      clearable
      clear-trigger="always"
      enterkeyhint="search"
      v-bind="$attrs"
      :placeholder="placeholder"
      @clear="handleClear"
      @click-right-icon="handleClickRightIcon"
      @keyup.enter="handleEnterUp"
      :disabled="disabled"
    >
      <template #left-icon>
        <div class="input-ico">
          <svg-icon name="Search" class="search-icon" />
        </div>
      </template>
      <template #right-icon v-if="isRightIcon">
        <span class="cancel">取消</span>
      </template>
    </van-field>
  </div>
</template>

<script setup>
import { nextTick, onBeforeMount, watch, ref } from 'vue'
import { useVModel } from '@vueuse/core'

const iptRef = ref(null)
const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  placeholder: {
    type: String,
    default: '',
  },
  isRightIcon: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['update:modelValue', 'clear', 'cancel', 'change', 'enter'])
const inputRef = ref(null)

const inputValue = useVModel(props, 'modelValue')

const handleClear = () => {
  inputValue.value = ''
  emits('clear')
}

const handleClickRightIcon = () => {
  iptRef?.value?.blur()
  setTimeout(() => {
    emits('cancel')
  })
}

watch(inputValue, val => {
  emits('change', val)
})

const handleEnterUp = (...args) => {
  emits('enter', inputValue, ...args)
}
onBeforeUnmount(() => {
  if (iptRef.value) {
    iptRef.value.blur() // 调用输入框的失去焦点方法
  }
})
</script>

<style scoped lang="scss">
.input-container {
  width: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  // height: 72px;
  box-sizing: border-box;

  .search-icon {
    width: 36px;
    height: 36px;
  }

  :deep(.van-field) {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    background: #f7f7f7;
    font-size: 28px;
    font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-weight: 400;
    outline: none;
    color: #282d30;
    caret-color: rgba(7, 110, 228, 1) !important;
    padding: 16px 0 16px 28px !important;

    .van-field__body {
      display: flex;
      align-items: center;
      height: 40px;
      color: #9b9eac;
      //   border: 1px solid red;
      padding-right: 10px;
      input {
        padding-left: 16px;
        color: inherit;
      }
      input::placeholder {
        font-size: 28px;
        color: #999;
      }

      .van-icon-clear {
        z-index: 333;
      }
    }
  }
}

.cancel {
  display: inline-block;
  border-left: 1px solid #dedede;
  padding: 0 24px;
  color: #20263a;
  font-size: 28px;
}
</style>
