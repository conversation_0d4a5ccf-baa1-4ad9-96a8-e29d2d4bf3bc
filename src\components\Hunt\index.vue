<script setup name="Hunt">
import VanPicker from '@/components/Van/VanPicker/index.vue'
import ExplainPop from './component/explainPop/index.vue'
import MultiplecChoice from './component/MultiplecChoice/index.vue'
import ChainPop from './component/chainPop/index.vue'
import { onClickOutside } from '@vueuse/core'

import { getRenderList, params } from './common/huntAbout'
import { useMethods } from './common/huntMethods'
import { nextTick, reactive, ref, toRefs, watch, onMounted } from 'vue'
import { setPx } from '@/utils/height'
const props = defineProps({
  height: {
    //因为用了Scroll这里需要一个写死的高度
    type: String,
    default: '60vh',
  },
  filter: {
    //更多需要过滤调的数据
    type: Array,
    default: [], //eg:['所在地区','所属行业']
  },
  asideList: {
    type: Array,
    default: undefined,
  },
  searchTermList: {
    type: Array,
    default: undefined,
  },
  fixChainPop: {
    //针对某个弹窗的特殊需求 - 可删
    type: Boolean,
    default: false,
  },
})
const data = reactive({
  height: props.height,
  // 后期会根据props传进来的东西过滤掉renderList
  renderList: [], //用于回显
  itemList: [], //渲染左边
  leftList: [], //渲染右边
  idName: '', //滚动到id名字的位置
  isIphoneX: false,
  params: JSON.parse(JSON.stringify(params)),
  minCapital: '', // 最低资本
  maxCapital: '', // 最高资本
  dateType: '', // 日期类型（开始/结束）
  dateActive: false, // 日期输入框活动状态
  date: '', // 日期组件回填数据
  // input类型
  capitalActive: false, // 注册资本输入框活动状态
  minDate: '', // 最低年限
  maxDate: '', // 最高年限
  socialActive: false,
  socialminPeson: '',
  socialmaxPeson: '',
  // /------------------/
  saveSearVal: '', //保存选项值
  // 弹窗
  searPop: false,
  saveSearPop: false,
  regionPop: false,
  datePop: false,
  eleseicPop: false,
  enttypePop: false,
  districtPop: false,
  chainCodePop: false,
  // 模版
  ent_name_focus: false, //企业名称
  ent_address_focus: false, //企业地址
  searchList: [],
  templateAry: [],
  renderAry: [], //模版渲染数组

  //
  isHeight: false, //是否有选中，用于高亮
  text: '努力加载中...',
})
// console.log('这个data是什么', data)
let emit = defineEmits(['submit'])
const lifeScrollRef = ref(null)
const {
  rightScrollRef,
  closeleft,
  leftactvie,
  openright,
  selectTag,
  inputFocus,
  closeIptCont,
  inputChange,
  onInput,
  clickItem,
  showtDatePicker,
  setDate,
  handlePop,
  submitSub,
  clearSear,
  setBackfillData,
  checkoutSear,
} = useMethods(data, { emit })
const cloneArray = arr => JSON.parse(JSON.stringify(arr))
watch(
  () => props.height,
  val => {
    data.height = val
    nextTick(() => {
      lifeScrollRef.value?.refresh()
      rightScrollRef.value?.refresh()
    })
  },
  {
    immediate: true,
  }
)
watch(
  () => [props.filter, props.asideList],
  ([val]) => {
    const renderList = getRenderList(props.asideList, props.searchTermList)
    data.renderList = cloneArray(renderList).filter(i => {
      if (i?.map?.length) {
        i.map = cloneArray(i.map).filter(itm => !val.includes(itm.value)) || []
      }
      return !val.includes(i.title)
    })
    data.itemList = cloneArray(data.renderList)
    // console.log('ddd', data.itemList)

    data.leftList = cloneArray(data.renderList)
  },
  {
    immediate: true,
    deep: true,
  }
)
defineExpose({
  clearSear, //清空
  setBackfillData, //回显数据
  checkoutSear, //校验
  rightScrollRef, // 刷新滚动
})

onUpdated(() => {
  setTimeout(() => {
    rightScrollRef.value?.refresh()
  }, 500)
})

const formItem = ref(null)

onMounted(() => {
  // 点击企业名称模糊搜索输入框及弹窗之外, 关闭弹窗
  onClickOutside(formItem?.value?.[0], () => {
    data.ent_name_focus = false
  })
  onClickOutside(formItem?.value?.[1], () => {
    data.ent_address_focus = false
  })
  const backup = JSON.parse(localStorage.getItem('searchBackup'));
  // console.log(backup)
  data.params.ent_name = backup?.ent_name || '';
})
</script>

<template>
  <div class="searWrap" :style="{ height: data.height }">
    <!-- 左边 -->
    <div class="searWrap-r" :style="{ height: data.height }">
      <Scroll ref="lifeScrollRef" isScrollY>
        <div class="searL" v-for="(item, index) in data.leftList" :key="index">
          <div :class="{ active: item.isActive && item.onlyText }" v-if="item.onlyText">
            <div v-if="!item.hideTitle" class="tit" @click="closeleft(item)">
              <div>{{ item.title }}</div>
              <img
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-down.png"
                v-if="!item.isOpen"
                alt=""
              />
              <img
                src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-upward.png"
                v-else
                alt=""
              />
            </div>
            <div class="tit_cont" :style="{ height: item.isOpen ? 'auto' : 0 }">
              <div
                v-for="(itm, key) in item.map"
                @click="leftactvie(item, itm.key)"
                :key="key"
                :class="{ 'child-active': itm.active }"
              >
                {{ itm.value }}
              </div>
            </div>
          </div>
        </div>
      </Scroll>
    </div>
    <!-- 右边 -->
    <div class="searWrap-l bg-white" :style="{ height: data.height }">
      <Scroll ref="rightScrollRef" :timeBounce="0">
        <div class="content cursor-pointer">
          <div v-for="(item, index) in data.itemList" :key="item.type" :id="item.type">
            <!-- input -->
            <div
              class="content-item-pop cnt-ipt"
              ref="formItem"
              v-click-outside="closeIptCont"
              v-if="item.special === 'input'"
              :class="{ '!mt-0': '企业地址' === item.title }"
            >
              <div class="title" :style="{ fontWeight: data.idName === item.type ? 600 : 400 }">
                {{ item.title }}
              </div>
              <div class="ipt">
                <input
                  placeholder="请输入企业名称(非必填)"
                  @focus="() => (data[`${item.type}_focus`] = true)"
                  @input="onInput($event, item)"
                  v-model="data.params[item.type]"
                  :maxlength="24"
                />
              </div>
              <!-- 搜索框  -->
              <div
                class="child-box"
                v-if="data[`${item.type}_focus`] && data.params[item.type]?.length > 0"
                @touchmove.prevent
              >
                <Scroll stopPropagation class="child-box-ul" v-if="data.searchList.length > 0">
                  <div v-for="(obj, index) in data.searchList" :key="index">
                    <div class="search-li" :class="{ hover: true }" @click="clickItem(obj[item.type], item.type)">
                      <template v-for="(i, index) in obj[item.type]" :key="index">
                        <span class="listtext" :class="{ searchHigh: i === data.params[item.type] }">{{ i }}</span>
                      </template>
                    </div>
                  </div>
                </Scroll>
                <div v-else>
                  <div class="loading">
                    <div class="hloading size"></div>
                    <!-- text -->
                    <div class="text">{{ data.text }}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 弹窗  -->
            <div
              class="content-item-pop"
              :class="{ sscyl: item.title === '所属产业链', yxbglt1: !item.isBorder && item.title === '所属产业链' }"
              v-else-if="item.special === 'pop'"
            >
              <div class="title" :style="{ fontWeight: data.idName === item.type ? 600 : 400 }">
                <!-- 针对特殊弹窗的需求 - 后续可删 -->
                <!-- {{ item.title }} -->
                {{ item.isBorder && item.title === '所属产业链' ? '产业链环节' : item.title }}
              </div>
              <div class="content-item-pop-r" @click="handlePop(item)">
                <span :class="{ has: item.content }">{{ item.content || '全部' }}</span>
                <img src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home_arrow.png" />
              </div>
            </div>
            <!-- 列表 -->
            <div
              :class="[
                'content-item',
                { yxbg: item.title === '产业优选' },
                { yxbglt: item.title === '龙头企业' },
                {
                  yxbglt1:
                    !item.isBorder &&
                    (item.title === '疑似扩张' ||
                      item.title === '企业规模' ||
                      item.title === '效益评估' ||
                      item.title === '外资企业' ||
                      item.title === '所属产业链'),
                },
                { yxbgend: !item.isBorder && item.title === '科技型企业' },
                { yxbgImg: item.title === '产业优选' },
                `${item.hideTitle ? 'pt-0 ' : 'pt-48'}`,
              ]"
              v-else
            >
              <!-- 如果是产业优选背景不一样 -->
              <div v-if="item.title === '产业优选'" class="yxbgImg">
                <img src="@/assets/image/zsyx.png" />
              </div>
              <div v-if="!item.hideTitle && item.onlyText" class="tit-text">
                <div v-if="item.title !== '产业优选'"></div>
                {{ item.title }}
                <div v-if="item.title !== '产业优选'"></div>
              </div>
              <div
                v-else-if="!item.onlyText && !item.isOpenIcon"
                class="tit"
                :style="{ 'font-weight': data.idName === item.type ? 600 : 400 }"
              >
                {{ item.title }}
                <span v-if="item.icon" class="filterexp" @click="() => (data.isexpPop = true)"></span>
                <div v-if="item.vip" class="vip"></div>
              </div>
              <div
                v-else-if="item.isOpenIcon"
                class="zhankai"
                :style="{ 'font-weight': data.idName === item.type ? 600 : 400 }"
              >
                <div style="display: flex; align-items: center" class="tit">
                  {{ item.title }}
                  <div v-if="item.vip" class="vip"></div>
                </div>
                <div class="img" v-if="!item.isOpen" @click="openright(item)">
                  <img
                    src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-upward.png"
                    mode="aspectFill"
                  />
                </div>
                <div class="img" v-else @click="openright(item)">
                  <img
                    src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-down.png"
                    mode="aspectFill"
                  />
                </div>
              </div>
              <div class="wrap" :style="{ height: item.isOpenIcon && item.isOpen ? setPx(158) + 'px' : 'auto' }">
                <span
                  v-for="(child, index) in item.list"
                  :key="index"
                  @click="selectTag(item, child)"
                  :class="[child.active && 'active']"
                  >{{ child.name }}</span
                >
                <div v-if="item.unit" class="input-wrap">
                  <div
                    v-if="item.genre === 'input' && item.type === 'register_capital'"
                    :class="[data.capitalActive && 'active']"
                  >
                    <input
                      type="number"
                      v-model="data.minCapital"
                      :maxlength="10"
                      :placeholder="item.min"
                      @input="inputChange(item.type)"
                      @focus="inputFocus(item.type)"
                    />
                    <span class="short-line">—</span>
                    <input
                      type="number"
                      v-model="data.maxCapital"
                      :maxlength="10"
                      :placeholder="item.max"
                      @input="inputChange(item.type)"
                      @focus="inputFocus(item.type)"
                    />
                  </div>
                  <div
                    v-if="item.genre === 'input' && item.type === 'super_dimension_social_num'"
                    :class="[data.socialActive && 'active']"
                  >
                    <input
                      type="number"
                      v-model="data.socialminPeson"
                      :maxlength="5"
                      :placeholder="item.min"
                      @input="inputChange(item.type)"
                      @focus="inputFocus(item.type)"
                    />
                    <span class="short-line">—</span>
                    <input
                      type="number"
                      v-model="data.socialmaxPeson"
                      :maxlength="5"
                      :placeholder="item.max"
                      @input="inputChange(item.type)"
                      @focus="inputFocus(item.type)"
                    />
                  </div>

                  <div v-if="item.genre === 'tpop'" :class="[data.dateActive && 'active']">
                    <div class="year" @click="showtDatePicker('startDate')">
                      {{ data.minDate || item.min }}
                    </div>
                    <span class="short-line">—</span>
                    <div class="year" @click="showtDatePicker('endDate')">
                      {{ data.maxDate || item.max }}
                    </div>
                  </div>
                  <span>{{ item.unit }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Scroll>
    </div>
  </div>
  <!-- 时间弹窗弹窗 -->
  <VanPicker
    :dateType="data.dateType"
    :visible="data.datePop"
    :date="data.date"
    @submit="setDate"
    @close="() => (data.datePop = false)"
  />
  <!-- 企业类型说明弹窗 -->
  <ExplainPop :visible="data.isexpPop" @submit="() => (data.isexpPop = false)" @close="() => (data.isexpPop = false)" />

  <!-- 地区弹窗 -->
  <MultiplecChoice
    :visible="data.regionPop"
    mark="areas"
    dataType="DistrictAry"
    @submit="submitSub"
    :oldData="data.params.regionData"
    @close="() => (data.regionPop = false)"
  />
  <!-- 行业 -->
  <MultiplecChoice
    :visible="data.eleseicPop"
    mark="trade_types"
    dataType="EleseicAry"
    @submit="submitSub"
    :oldData="data.params.eleseic_data"
    @close="() => (data.eleseicPop = false)"
  />
  <!-- 企業类型 -->
  <MultiplecChoice
    :visible="data.enttypePop"
    mark="ent_type"
    dataType="EnttypeAry"
    @submit="submitSub"
    :oldData="data.params.enttype_data"
    @close="() => (data.enttypePop = false)"
  />
  <!-- 企业许可 -->
  <MultiplecChoice
    :visible="data.districtPop"
    mark="ent_cert"
    dataType="AllCertAry"
    @submit="submitSub"
    :oldData="data.params.all_cert_data"
    @close="() => (data.districtPop = false)"
  />

  <!-- 所属产业链ChainPop -->
  <ChainPop
    :visible="data.chainCodePop"
    mark="chain_codes"
    :special="props.fixChainPop ? true : false"
    @submit="submitSub"
    :oldData="data.params.chain_codes_data"
    @close="() => (data.chainCodePop = false)"
  />
</template>
<style lang="scss" scoped>
// 筛选相关
$searWrap-bg: #f2f2f2;
$searWrap-padding-top: 12px;
$searWrap-r-width: 208px;
$searWrap-scrollbar-width: 2px;
$searWrap-scrollbar-height: 2px;
$searWrap-scrollbar-border-radius: 10px;
$searWrap-scrollbar-thumb-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
$searWrap-scrollbar-thumb-bg: #39b54a;
$searWrap-l-width: 542px;
$color-red: #3e7bfa;
$color-grey: #525665;
$color-black: #20263a;
$gradient-red: linear-gradient(90deg, #076ee4 26%, #53a2ff 98%);
$gradient-red-2: #3e7bfa;
$font-family: PingFang SC-Regular, PingFang SC;
$font-size-large: 28px;
$font-size-medium: 24px;
$font-weight-normal: 400;
$font-weight-bold: 600;
$border-radius: 16px 0 0 16px;
$icon-size: 26px;
$line-height-large: 88px;
$line-height-small: 32px;
$padding-left: 32px;
$white: #fff;
$gray-light: #f5f5f5;
$gray-medium: #eeeeee;
$gray-dark: #74798c;
$red-light: #ffb2aa;
$red-dark: #3e7bfa;
$font-family: 'PingFang SC', 'PingFang SC-Semibold';
$font-size: 32px;
$gray-light: #eee;
$gray-medium: #5c6070;
$gray-dark: #20263a;
$red: #3e7bfa;
$font-size-small: 28px;
$font-size-medium: 24px;
$font-weight-regular: 400;
$font-weight-semi-bold: 600;

.searWrap {
  display: flex;
  background: $searWrap-bg;

  &-r {
    width: $searWrap-r-width;

    &::-webkit-scrollbar {
      display: inline-block;
      width: $searWrap-scrollbar-width !important;
      height: $searWrap-scrollbar-height !important;
      color: #ffffff;
    }

    &::-webkit-scrollbar-track {
      border-radius: $searWrap-scrollbar-border-radius;
      background-color: $white;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: $searWrap-scrollbar-border-radius;
      -webkit-box-shadow: $searWrap-scrollbar-thumb-shadow;
      box-shadow: $searWrap-scrollbar-thumb-shadow;
      background-color: $searWrap-scrollbar-thumb-bg;
    }
  }

  &-l {
    width: $searWrap-l-width;

    &::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      color: transparent;
    }
  }
}

// Converted CSS/SCSS
.searL {
  width: 100%;
  height: 100%;

  .active {
    position: relative;
    background: $white;
    border-radius: $border-radius;
  }

  .active::after {
    position: absolute;
    content: '';
    top: -$icon-size;
    right: 0;
    width: $icon-size;
    height: $icon-size;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAAMlJREFUSEvt1SEOAjEQheF/EwQSQQICsQKxAoFE4DjCHmEFEssBcBgk90AgkUiOwTEeGTKbEEICIe0IQpPafn3TTKcgaBVBDn/o60r/VukkdUMSSepFQf0oqIyCJlHQIgpqoqBtFHTKDlkPAdcIaAnss0L29QAXoMoNrYCdffnZIEmVp7FUeSBJQ+AMlO0AS57IkSMwfZySSSEv1wEYP4/iJJCkDmAPvwHub5IU8masgfWrFB+VznugvZ11t+0BMPJDZ8AcsDRv1w3mV0k0AXl2AAAAAABJRU5ErkJggg==');
    background-size: contain;
    background-repeat: no-repeat;
  }

  .active::before {
    position: absolute;
    content: '';
    bottom: -$icon-size;
    right: 0;
    width: $icon-size;
    height: $icon-size;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAaCAYAAACpSkzOAAAAAXNSR0IArs4c6QAAANlJREFUSEvt1jEKwkAQheE/IJhCMKVlCkHLFBbewCvYp7AVPIQWHsF7aGfhBew8hIcYGZiVCK6CZqcQA4GwDPPxZkmyGZFLRLpADnSAnt0DoATGwASYWk2szX09e1vxokBE+sAcWBoerf4KCl1FRJMvgE0sYStQAxwBBxvvQ7pWIe0sIrqPe6BqSq1DDewEDAOWBDJMx3gOe5YMMmwFbPU5NaTv4EVHmBSyVDWw84AK4JocslRHL2jtBdVe0MwLqryg0gsqfg7KXRIl/6gm//E9O6H8R/fxMfAGrshJOEGy/j4AAAAASUVORK5CYII=');
    background-size: contain;
    background-repeat: no-repeat;
  }

  .tit {
    position: relative;
    width: 100%;
    height: 100px;
    display: flex;
    align-items: center;
    padding-left: $padding-left;
    img {
      width: 20px;
      height: 20px;
      margin-left: 12px;
    }

    > div {
      font-size: $font-size-large;
      font-family: $font-family;
      font-weight: $font-weight-normal;
      color: $color-black;
    }
  }

  .active .tit::after {
    position: absolute;
    bottom: 14px;
    left: 52px;
    content: '';
    width: 72px;
    height: 6px;
    background: $gradient-red;
    border-radius: 2px;
    opacity: 0.7;
  }

  .tit_cont {
    overflow: hidden;
    transition: all 0.3s;

    div {
      height: $line-height-large;
      padding-left: $padding-left;
      font-size: $font-size-medium;
      font-family: $font-family;
      font-weight: $font-weight-normal;
      color: $color-grey;
      line-height: $line-height-large;

      &.child-active {
        position: relative;
        color: $color-red;
        font-weight: $font-weight-bold;
        font-size: $font-size-medium;

        &::after {
          content: '';
          position: absolute;
          bottom: 14px;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: $line-height-small;
          background: $gradient-red-2;
        }
      }
    }
  }

  .active .tit > div {
    font-weight: $font-weight-bold;
  }
}

.footer {
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 1;
  width: 100%;
  background-color: $white;
  display: flex;
  justify-content: space-between;
  padding: 10px 24px;
  box-shadow: 8px 0px 8px 2px rgba(204, 204, 204, 0.2);
}

.footer span {
  width: 220px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  border-radius: 8px;
  background: linear-gradient(90deg, $red-light 0%, $red-dark 100%);
  color: $white;
  font-size: $font-size;
  font-family: $font-family;
}

.footer span:nth-child(1),
.footer span:nth-child(2) {
  color: $gray-dark;
  background: linear-gradient(315deg, $gray-medium 0%, $gray-light 100%);
}

.company-filter {
  display: flex;
  flex-direction: column;
  position: relative;
  background: $white;
}
$content-bg-color: #fff;
$content-padding: 0 32px 32px;
$content-border-radius: 16px;

.content {
  width: 100%;
  background-color: $content-bg-color;
  padding: $content-padding;
  overflow-y: auto;
  border-radius: $content-border-radius;
}

.content-item-pop {
  position: relative;
  display: flex;
  justify-content: space-between;
  height: 92px;
  align-items: center;
}

.content-item-pop .ipt {
  position: relative;
  flex: 1;
  margin-left: 28px;
}

.content-item-pop .ipt > input {
  font-size: 28px;
  font-family: 'PingFang SC-Regular', 'PingFang SC';
  font-weight: 400;
  color: #3e7bfa;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  border: none;
}

input::placeholder {
  font-size: 28px;
  font-family: 'PingFang SC-Regular', 'PingFang SC';
  font-weight: 400;
  color: #9b9eac;
}

.cnt-ipt {
  position: relative;
  margin-top: 40px;
}
.content .cnt-ipt::before,
.content .content-item-pop::after {
  content: ' ';
  width: 100%;
  height: 1px;
  background: $gray-light;
  position: absolute;
  transform: scaleY(0.5);
}

.content .cnt-ipt::before {
  top: 0;
  left: 0;
}

.content .content-item-pop::after {
  bottom: 0;
  left: 0;
}

.content .content-item-pop-r {
  display: inline-flex;
  align-items: center;
  flex: 1;
  justify-content: space-between;
}

.content .content-item-pop-r span {
  font-size: $font-size-small;
  font-family: $font-family;
  font-weight: $font-weight-regular;
  color: $gray-medium;
  flex: 1;
  margin-left: 28px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.content .content-item-pop-r span.has {
  color: $red !important;
}

.content .content-item-pop-r img {
  width: 24px;
  height: 24px;
  margin-left: 10px;
}

.content .content-item-pop .title {
  font-size: $font-size-small;
  font-family: $font-family;
  font-weight: $font-weight-regular;
  color: $gray-dark;
  flex-shrink: 0;
}

.content .content-item {
  font-size: $font-size-small;
  color: $gray-medium;
  padding: 48px 0 0;
  background-color: $white;
}

.content-item .tit {
  display: flex;
  align-items: center;
  font-size: $font-size-small;
  font-family: $font-family;
  font-weight: $font-weight-regular;
  color: $gray-dark;
}

.content-item .tit-text {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-family: $font-family;
  font-weight: $font-weight-semi-bold;
  color: $gray-dark;
}

.content-item .tit-text div:nth-child(1),
.content-item .tit-text div:nth-child(2) {
  width: 60px;
  height: 1px;
  background: $gray-medium;
  margin: 0 20px;
  transform: scaleY(0.5);
}
/* 企业规模说明--icon*/
.filterexp {
  display: inline-block;
  margin-left: 12px;
  width: 32px;
  height: 32px;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAAXNSR0IArs4c6QAAANhQTFRFAAAAgICAn5+flZWqn5+vmZmzn5+1m5u2paW9n5+3oKi9nKO4o6O+n5+5o6O4o6i4oKW5oqe5oqa6n6O3oqW7oKO7oaW4oaW7oKa7oaS5oKW5oKW8oaS5oKW5oaO6oaa6oKS6oaW6oKS6oaW7oKW5oKS5oKa5oKS6oKW6oKW6oKS6oKW6oKS6oKW6oKS5oKW5oKS5oKW5oKS5oKS6oKW6oKW6oKS6oKW5oKW6oKS5oKS6oKW5oKW6oKS5oKW5oKW5oKS5oKS6oKW6oKW6oKS6oKW6oKW6oKW66g9uAAAAAEd0Uk5TAAQIDBAUGBwfICMkJygvLzM3P0BHS09PU1dbW19jZ2drb3N/i4+Pl5ebn5+jp6uvs7O3x8fLz8/P09PX19vf4+fr6/P39/vWY4YUAAABYklEQVQ4y32T2VrCMBBGxxIXrLivFVxBhSgoWhCLMWDsef838qaFtrbO1SwnmfmSf0QWpgI9nsM87AZK/tqm/mFh7n6rUPbaMRBHL1oPJgCu4+WOT4HX1mrSqzkGoo1lfceCOcyeODZgd9Now0K/Vuj5BHYz8afQ+Tt1ByIv9QZpdu/6ej/1+9AWEdlymMXEt5nLaobYF5F7OJYyQA5BiyjHeNn4YjS6WUZvOCUBtKTCWhBIF9aqgNWYnoSYTKp+cOBnwiljmTPMZG7zT/LCXEBXAxoEHv8H/m0xwMooN2QB+CAUDetVgIKunMNlFdCEM1GOjyrgHadEenBaDpzAg4j4MV+qDPAMriEi0obnlSS53WwulPiUwl5UKrk7mCRC8i08F0Rb64Otp9GuBXOSrR8ZsI3ML0fA+1UijLWLEJjUc2vQcQBmqB+HnwBx2yvM1Hhwy+X97vklAlNBN5zBLL/+v69tTgQXnGC4AAAAAElFTkSuQmCC');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.vip {
  width: 64px;
  height: 32px;
  margin-left: 8px;
  background: url('data:image/png;base64,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')
    no-repeat;
  background-size: 100% 100%;
}

.expPop {
  /* 企业规模说明弹窗 */
  font-size: 28px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263a;
  padding: 48px 32px 60px;
}
.content-item .wrap {
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  transition: all 1s ease;
  > span {
    padding: 0 26px;
    height: 56px;
    line-height: 56px;
    background-color: #f4f4f4;
    /* border: 2px solid #F4F4F4; */
    margin-right: 20px;
    margin-top: 24px;
    border-radius: 8px;
    font-weight: 400;
    overflow: hidden;

    &.active {
      /* border: 2px solid #3E7BFA; */
      color: #ffffff !important;
      background: #3e7bfa;
    }
  }

  .input-wrap {
    display: flex;
    align-items: center;
    margin-top: 25px;

    > div {
      display: flex;
      width: 380px;
      height: 56px;
      padding: 0 19px;
      background-color: #f4f4f4;
      border-radius: 4px;
      margin-right: 19px;
      color: #9b9eac;
      &.active {
        /* border: 2px solid #3E7BFA; */
        color: #3e7bfa !important;
        background-color: rgba(42, 114, 255, 0.1);
        input {
          color: #3e7bfa;
          background-color: rgba(231, 36, 16, 0);
        }
      }

      .year {
        flex-shrink: 0;
        line-height: 56px;
      }

      input {
        height: 100%;
        width: 100%;
        border: none;
        background: #f4f4f4;
        color: #3e7bfa;
      }

      .short-line {
        width: 200px;
        height: 100%;
        line-height: 52px;
        text-align: center;
      }
    }
  }
}
.zhankai {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  font-size: 28px;
  .img {
    display: flex;
    justify-content: flex-end;
    width: 90px;
    > img {
      width: 24px;
      height: 24px;
      // padding-left: 30px;
      flex-shrink: 0;
    }
  }
}

/* 保存选项弹窗 */
.fadeIn {
  -webkit-animation: c 0.3s forwards;
  animation: c 0.3s forwards;
}

@-webkit-keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes c {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.mask {
  position: fixed;
  z-index: 99;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  /* display: flex;
    justify-content: center;
    align-items: center; */
}

.mask .box {
  width: 604px;
  height: 354px;
  background: #ffffff;
  border-radius: 16px 16px 16px 16px;
  opacity: 1;
  margin: 434px auto 0;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.mask .box .tit {
  padding: 48px 0 24px;
  font-size: 32px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263a;
}

.mask .box .inpt {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 524px;
  height: 88px;
  background: #f7f7f7;
  border-radius: 4px 4px 4px 4px;
  padding: 24px 28px;
}

.mask .box .inpt > input {
  width: 100%;
  height: 100%;
  font-size: 28px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #5b5f6f;
}

.placeholder-input {
  font-size: 28px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9b9eac;
}

.mask .box .btn {
  position: relative;
  display: flex;
  height: 100px;
  width: 100%;
  margin-top: 48px;
}

.mask .box .btn::before {
  content: ' ';
  width: 100%;
  height: 1px;
  background: #eee;
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleY(0.5);
}

.mask .box .btn::after {
  content: ' ';
  width: 1px;
  height: 100px;
  background: #eee;
  position: absolute;
  top: 0;
  left: 50%;
  transform: scaleX(0.5) translateX(-50%);
}

.mask .box .btn div {
  flex: 1;
  font-size: 32px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263a;
  line-height: 100px;
  text-align: center;
}

/* input下面搜索框样式 */
.child-box {
  position: absolute;
  display: flex;
  align-content: center;
  justify-content: center;
  width: 100%;
  top: 106px;
  background: #fff;
  z-index: 15;
  box-shadow: 0px 0px 16px 0px rgba(32, 38, 58, 0.2);
  height: 480px;
}

.child-box-ul {
  height: 480px;
}

.search-li {
  height: 96px;
  line-height: 96px;
  margin: 0 28px;
  border-bottom: 1px solid #f7f7f7;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.search-li .listtext {
  font-size: 28px;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
}

.search-li .searchHigh {
  color: $color-red !important;
}

.child-box .loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.loading .size {
  width: 80px;
  height: 80px;
}

/* loading 样式 */
.hloading {
  margin: 0 5px;
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
  -webkit-animation: spin 1s steps(12, end) infinite;
  animation: spin 1s steps(12, end) infinite;
  background: transparent
    url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=)
    no-repeat;
  background-size: 100%;
}

.loading .text {
  padding-top: 20px;
  font-size: 28px;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #9b9eac;
}

/* 产业优选特殊样式23.2.23 */
.yxbg {
  position: relative;
  width: 100% !important;
  margin-top: 48px;
}

.yxbg .tit-text {
  position: absolute;
  top: 16px;
  width: 100%;
  z-index: 10;
  height: 100%;
  font-size: 32px;
  font-weight: 600;
  color: #20263a;
}

.yxbg .yxbgImg {
  position: absolute;
  left: -24px;
  right: -24px;
  top: 0;
  /* border: 1px solid blue; */
  background: linear-gradient(311deg, rgba(255, 255, 255, 0.84) 0%, #e0edff 100%);
  width: 526px;
  height: 120px;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  overflow: hidden;
}

.yxbgImg img {
  width: 100%;
  height: 100%;
}

.yxbglt {
  position: relative;
  z-index: 1;
  margin-top: 36px;
  width: 526px;
  transform: translateX(-24px);
  border-left: 8px solid #e0edff;
  border-right: 8px solid #e0edff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 36px 16px 20px !important;
}

.yxbglt1 {
  width: 526px;
  transform: translateX(-24px);
  border-left: 8px solid #e0edff;
  border-right: 8px solid #e0edff;
  padding: 20px 16px 20px !important;
}

.yxbgend {
  width: 526px;
  transform: translateX(-24px);
  border-left: 8px solid #e0edff;
  border-right: 8px solid #e0edff;
  border-bottom: 8px solid #e0edff;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  padding: 20px 16px 20px !important;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
