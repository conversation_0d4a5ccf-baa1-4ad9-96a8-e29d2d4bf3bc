<template>
  <!-- 静默登录 -->
  <div class="silent-login">
    <van-loading type="spinner" size="24px">加载中...</van-loading>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user' // 上线删除
import { getUrlKey } from '@/utils/utils'
import { openReport } from '@/utils/index'
// entId=eoEXMiyhRCJ
// http://localhost:8004/login?entId=eoEXMiyhRCJ
const { VITE_APP_JUMP_URL } = import.meta.env
const router = useRouter()
const route = useRoute()
// 上线删除--start
const userStore = useUserStore()
const { proxy } = getCurrentInstance()
const reportEntId = computed(() => userStore.reportEntIdGet)

// 单点登录
const singleSignOnWithCode = code => {
  const { protocol, origin } = window.location
  localStorage.clear()
  proxy.$loading('加载中...')
  userStore
    .CodeLogin({
      code,
      redirect_url: `${origin}/hdzhzs/login`,
    })
    .then(() => {
      if (reportEntId.value && reportEntId.value !== 'undefined') {
        openReport({
          ent_id: reportEntId.value,
        })
      } else {
        router.replace({ path: '/home' })
      }
    })
    .catch(err => {
      proxy.$showToast(err.message)
    })
    .finally(() => {
      proxy.$close()
    })
}

onMounted(() => {
  const { code, entId, state } = getUrlKey()
  // 如果存在企业id,则存储企业id信息
  if (entId || (state && state !== undefined && state !== 'undefined')) {
    userStore.SetReportId(entId ?? state)
  }
  code && singleSignOnWithCode(code)
  if (!code) {
    // debugger
    const { protocol, origin } = window.location
    location.href = `${VITE_APP_JUMP_URL}&redirect_uri=${origin}/hdzhzs/login&scope=basic&state=${entId}`
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  .login {
    &-title {
      margin: 10px auto;
      font-size: 36px;
      letter-spacing: 2px;
      text-align: center;
    }
    &-tip {
      margin: 20px auto;
      padding-left: 64px;
      color: red;
      font-size: 24px;
    }
    &-line-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .icon-demo {
        width: 36px;
        height: 36px;
        color: orange;
      }
    }
  }
}

// 静默登录样式
.silent-login {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
}
</style>
