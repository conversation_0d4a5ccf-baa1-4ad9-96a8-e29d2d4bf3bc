<template>
  <div class="drop-down">
    <van-dropdown-menu active-color="#2A72FF " style="width: 100%; height: 100%">
      <van-dropdown-item
        v-model="options[0].value"
        :options="options[0].children"
        :title-class="options[0].value ? 'activeMenuItem' : ''"
        :title="options[0].value ? '' : options[0].name"
        @change="val => changeDropdown(val, options[0].code)"
      >
      </van-dropdown-item>
      <van-dropdown-item
        v-model="options[1].value"
        :options="options[1].children"
        @change="val => changeDropdown(val, options[1].code)"
        :title-class="options[1].value ? 'activeMenuItem' : ''"
        :title="options[1].value ? '' : options[1].name"
      >
      </van-dropdown-item>
      <van-dropdown-item
        v-if="options.length > 2"
        v-model="options[2].value"
        :options="options[2].children"
        @change="val => changeDropdown(val, options[2].code)"
        :title-class="options[2].value ? 'activeMenuItem' : ''"
        :title="options[2].value ? '' : options[2].name"
      >
      </van-dropdown-item>
    </van-dropdown-menu>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'

const emit = defineEmits(['change'])
const activeKey = reactive('')

const props = defineProps({
  options: {
    type: Array,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
  params: {
    type: Object,
    default: {},
  },
})

const changeDropdown = function (val, code) {
  emit('change', val, code)
}
</script>

<style lang="scss" scoped>
.drop-down {
  width: 100%;
  height: 100%;
}
:deep(.van-dropdown-menu__bar) {
  box-shadow: none;
  height: 100%;
}
:deep(.van-overlay) {
  background: rgba(0, 0, 0, 0.2);
}
:deep(.activeMenuItem) {
  color: #3e7bfa !important;
  span {
    color: #3e7bfa !important;
  }
}
:deep(.activeMenuItem:after) {
  border-color: transparent transparent currentColor currentColor;
}
:deep(.van-dropdown-item__content) {
  position: absolute;
  max-height: 800px !important;
}
</style>
