<template>
  <div class="head-wrapper">
    <div class="head-container">
      <div class="zw_bg"></div>
      <!-- 搜索框加下拉框 -->
      <div class="search-dropdown-wrapper">
        <!-- 左边搜索框 -->
        <div class="search-container">
          <van-field
            v-model="searchValue"
            clearable
            clear-trigger="always"
            enterkeyhint="search"
            placeholder="请输入产业链关键字"
            @clear="handleClear"
            @keyup.enter="handleSearch"
            @blur="handleBlur"
          >
            <template #left-icon>
              <div class="search-icon-wrapper">
                <svg-icon name="Search" class="search-icon" />
              </div>
            </template>
          </van-field>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineEmits, defineProps } from 'vue'
import { useVModel } from '@vueuse/core'

// Props
const props = defineProps({
  searchValue: {
    type: String,
    default: '',
  },
  searchPlaceholder: {
    type: String,
    default: '请输入搜索内容',
  },
})

// Emits
const emits = defineEmits(['update:searchValue', 'search', 'clear'])

// 使用 useVModel 处理双向绑定
const searchValue = useVModel(props, 'searchValue', emits)

// Methods
const handleClear = () => {
  searchValue.value = ''
  emits('clear')
}

const handleSearch = () => {
  emits('search', searchValue.value)
}

// 输入框失去焦点时触发搜索
const handleBlur = () => {
  emits('search', searchValue.value)
}
</script>

<style lang="scss" scoped>
.head-wrapper {
  @apply relative;
}

.head-container {
  @apply p-24  w-[100vw] h-[156px] flex items-center
   bg-[url('@/assets/image/cyl/cyl_bg.png')] bg-no-repeat
   bg-cover relative;
  .zw_bg {
    @apply absolute w-full h-1 bg-[#07a6f0] top-0 left-0;
  }
}

.search-dropdown-wrapper {
  @apply flex items-center h-72 w-full;
}

.search-container {
  @apply w-full;

  :deep(.van-field) {
    @apply flex items-center rounded-8 bg-white  font-400 outline-none h-72;
    font-size: 28px;
    color: #282d30;
    caret-color: rgba(7, 110, 228, 1) !important;
    padding: 18px 24px !important;
    box-sizing: border-box;

    .van-field__body {
      @apply flex items-center h-40 pr-10;
      color: #9b9eac;

      input {
        @apply pl-16;
        color: inherit;

        &::placeholder {
          font-size: 28px;
          color: #999;
        }
      }

      .van-icon-clear {
        @apply z-[333];
      }
    }
  }
}

.search-icon-wrapper {
  @apply f-all-center;
}

.search-icon {
  @apply w-36 h-36;
}
</style>
