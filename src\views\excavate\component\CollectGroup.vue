<template>
  <VanbPop v-bind="$attrs" :visible="visible" :isFooter="false" @close="emits('close')">
    <!-- 头部 -->
    <div class="head flex justify-between">
      <div @click="emits('close')">取消</div>
      <div>请选择项目分类</div>
      <div @click="handleConfirm">确定</div>
    </div>
    <!-- 内容 -->
    <div class="cont">
      <div class="box">
        <template v-for="(item, index) in sourceData" :key="index">
          <div :class="['item', 'flx-center', item.isActive && 'active']" @click="onItem(item)">
            {{ item.type }}
          </div>
        </template>
      </div>
    </div>
  </VanbPop>
</template>
<script setup>
import VanbPop from '@/components/Van/VanbPop/index.vue'
import { getAllReserveItemsClassificationDat } from '@/api/excavate/index.js'

const { proxy } = getCurrentInstance()
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['close', 'handleSelect'])

const sourceData = ref([])

watch(
  () => props.visible,
  newVal => {
    if (!newVal) return
    sourceData.value.forEach((i, idx) => {
      i.isActive = false
      // if (i.id === props.id) {
      //   i.isActive = true
      // }
      // 每次进来默认第一个为高亮
      if (idx === 0) {
        i.isActive = true
      }
    })
  },
  { immediate: true }
)
// 确定
const handleConfirm = () => {
  let item = sourceData.value.filter(i => i.isActive)?.[0] || ''
  emits('handleSelect', item)
}
const onItem = item => {
  sourceData.value = sourceData.value.map(i => {
    i.isActive = false
    if (item.id === i.id) {
      i.isActive = true
    }
    return i
  })
}
// 初始化
const init = async () => {
  const arr = await getAllReserveItemsClassificationDat()
  if (arr?.length) {
    arr.unshift({ type: '重点企业', id: '-1' })
    sourceData.value = arr
  } else {
    sourceData.value = [{ type: '重点企业', id: '-1' }]
  }
}
onMounted(() => {
  init()
})
</script>

<style scoped lang="scss">
.head {
  height: 112px;
  background: #ffffff;
  border-bottom: 1px solid #eeeeee;
  padding: 0 32px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 32px;
  color: #595c69;
  div:nth-of-type(2) {
    font-weight: 600;
    font-size: 32px;
    color: #20263a;
    line-height: 48px;
  }
  div:nth-of-type(3) {
    color: #3e7bfa;
  }
}
$inset-bottom: env(safe-area-inset-bottom);
.cont {
  padding-bottom: calc(#{$inset-bottom} + 40px); //这个
  .box {
    min-height: 380px;
    max-height: 500px;
    overflow-y: scroll;
    scroll-behavior: smooth;
    &::-webkit-scrollbar {
      display: none !important;
      width: 0px;
      height: 0px;
      background: transparent;
      color: transparent;
    }
  }
}
.item {
  height: 104px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 28px;
  color: #4c4c4c;
  //   border-bottom: 1px solid #eee;
  &.active {
    background: rgba(62, 123, 250, 0.1);
    color: #3e7bfa;
  }
  img {
    margin-right: 8px;
    width: 40px;
    height: 40px;
  }
}
</style>
