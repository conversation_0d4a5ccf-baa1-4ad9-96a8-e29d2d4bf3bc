<template>
  <div>
    <div class="ent-card" v-for="item in sourceData" :key="item.ent_id">
      <div>
        <div class="ent-name-tag">
          <HImage
            class="imgs"
            :src="
              item.ent_logo || item.logo
                ? item.ent_logo || item.logo
                : 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png'
            "
            @click="handleClickCard(item)"
          />
          <div>
            <div
              class="flex justify-between pr-20"
              :class="type === 'noThreeAddCollect' ? ['items-start', 'w-[580px]'] : ['items-center']"
            >
              <div class="flex items-center ent-name" @click="handleClickCard(item)">
                <HightlightText
                  v-if="from !== 'report'"
                  :query="query"
                  class="hightlight-text"
                  hightlight-class="hightlight"
                  :text="item.ent_name"
                />
                <Text
                  v-else
                  :query="query"
                  class="hightlight-text"
                  hightlight-class="hightlight"
                  :text="item.ent_name"
                />
                <slot name="after-name" :data="item"></slot>
              </div>
              <div
                v-if="leftBtn === 'monitor'"
                @click="emits('btnChange', item)"
                class="cursor-pointer flex justify-center items-center w-[96px] flex-shrink-0 h-[48px] rounded-[8px] border border-solid-[1px] border-#3E7BFA text-[24px] text-#3E7BFA"
              >
                监控
              </div>
              <div
                v-if="type === 'noThreeAddCollect'"
                class="w-[36px] h-[36px] cursor-pointer"
                @click="handleCollect(item)"
              >
                <img
                  v-if="!item.collect"
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
                  class="w-[36px] h-[36px] cursor-pointer"
                />
                <img
                  class="w-[36px] h-[36px] cursor-pointer"
                  v-else
                  src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"
                />
              </div>
            </div>
            <SetTagColor :tags="maxTagCount ? item.tags.slice(0, maxTagCount) : item.tags"/>
          </div>
        </div>
        <div class="card_c" v-if="type !== 'noThreeAddCollect' || special">
          <div class="card_c_i">
            <span class="name">法人代表人</span>
            <span class="cont contblue">{{ item.legal_person || item.fa_ren || '--' }}</span>
          </div>
          <div class="card_c_i card_c_is">
            <span class="name">注册资本</span>
            <span class="cont"
            >{{
                from === 'bus' ? item.registered_capital || '--' : item.register_capital || item.reg_cap || '--'
              }}万</span
            >
          </div>
          <div class="card_c_i">
            <span class="name">成立日期</span>
            <span class="cont">{{ item.register_date || item.es_date || '--' }}</span>
          </div>
        </div>
      </div>
      <div class="footer" :class="type === 'noThreeAddCollect' && !special && ['items-center']">
        <div v-if="type === 'noThreeAddCollect' && !special" class="flex items-center flex-1">
          <img class="w-[20px] h-[20px]" src="@/assets/image/yzwj/biaoci.png"/>
          <div class="!text-[#74798C] text-[28px] ml-[2px]">距您{{ item.distance || '0' }}km</div>
        </div>
        <EntRelatedOperations
          v-if="!slots.footer"
          :data="item"
          :showContacts="showContacts"
          @visible-change="visible => emits('visibleChange', visible)"
        />
        <slot name="footer" :item="item"></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import {defineEmits, useSlots} from 'vue'
import {useRouter} from 'vue-router'
import EntRelatedOperations from './entRelatedOperations.vue'
import SetTagColor from '@/components/ColorfulTag/index.vue'
import HightlightText from '@/components/HightlightText/index.vue'
import Text from '../HightlightText/text.vue'
import {openReport} from '@/utils/index'
import {batch, removeBatch} from '@/api/advanceSearch/index'

const props = defineProps({
  entList: {
    type: Array,
    default: () => [],
  },
  openNow: {
    type: Boolean,
    default: true,
  },
  query: {
    type: String,
    default: undefined,
  },
  from: {
    type: String,
    default: '',
  },
  leftBtn: {
    // 是否显示按钮
    type: String,
    default: '', //monitor
  },
  type: {
    // 类型
    type: String,
    default: '',
  },
  special: {
    //临时区分
    type: Boolean,
  },
  onClickEntName: {
    type: Function,
    default: undefined,
  },
  maxTagCount: {
    type: Number,
    default: 6,
  },
  showContacts: {
    type: Boolean,
    default: false,
  }
})
const sourceData = ref([]) // 为了收藏不发请求最后一起发
const {proxy} = getCurrentInstance()
watch(
  () => props.entList,
  val => {
    sourceData.value = val
  },
  {
    immediate: true,
  }
)

const slots = useSlots()
const emits = defineEmits(['visibleChange', 'clickItem', 'btnChange'])
const router = useRouter()

const handleClickCard = item => {
  // proxy.$loading('加载中...')
  const {ent_id} = item
  if (props.onClickEntName) {
    props.onClickEntName?.(item)
  } else {
    openReport({
      ent_id: ent_id,
    })
  }
  // setTimeout(() => {
  //   proxy.$close()
  // }, 2000)
}

// 收藏相关
const tempCollect = ref({})
const handleCollect = useDebounceFn(item => {
  tempCollect.value = item
  handleSelect(item)
}, 500)
const handleSelect = async item => {
  try {
    const {type} = item
    const {ent_id, ent_name, region_code, collect} = tempCollect.value
    if (!collect) {
      //收藏
      const {IDFAIL} = await batch([
        {enterprise_id: ent_id, enterprise_name: ent_name, region_code, reserve_type: type},
      ])
      if (IDFAIL?.includes?.(ent_id)) {
        throw new Error()
      }
    } else {
      // 取消收藏
      await removeBatch([
        {
          enterprise_id: ent_id,
          enterprise_name: ent_name,
          region_code,
        },
      ])
    }
    // 走到这里说明成功了
    sourceData.value.forEach(item => {
      if (item.ent_id === ent_id) {
        item.collect = !item.collect
      }
    })
  } catch (error) {
    console.log(error)
    proxy.$showToast(error?.message || '收藏失败!')
  }
}
</script>

<style lang="scss" scoped>
.ent-card {
  padding: 32px 24px 20px 24px;
  background: #fff;
  margin-bottom: 20px;

  .ent-name-tag {
    display: flex;

    .imgs {
      width: 96px;
      height: 96px;
      margin-right: 30px;
      flex-shrink: 0;
    }

    .ent-name {
      width: calc(100vw - 306px);
      display: flex;
      // justify-content: space-between;
      margin-bottom: 16px;
    }

    .hightlight-text {
      font-size: 32px;
      font-weight: 600;
      color: #20263a;

      :deep(.hightlight) {
        color: #e72410 !important;
        font-size: 32px;
        font-weight: 600;
      }
    }
  }

  .card_c {
    display: flex;
    justify-content: space-around;
    padding: 20px 0 20px;

    .card_c_i {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .name {
        font-size: 24px;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: center;
        color: #9b9eac;
        padding-bottom: 12px;
      }

      .cont {
        font-size: 28px;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: 400;
        text-align: center;
        color: #3d4255;
      }

      .contblue {
        color: #1e75db;
      }
    }

    .card_c_is::after {
      position: absolute;
      content: '';
      left: 0;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      border-right: 1px solid rgba(238, 238, 238, 0.8);
      width: 0;
      height: 48px;
    }

    .card_c_is::before {
      position: absolute;
      content: '';
      right: 0;
      top: 50%;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
      border-right: 1px solid rgba(238, 238, 238, 0.8);
      width: 0;
      height: 48px;
    }
  }

  .footer {
    position: relative;
    display: flex;
    justify-content: flex-end;
    flex-wrap: nowrap;
    padding: 20px 0px 0 0;
  }

  .footer::after {
    content: ' ';
    width: calc(100% + 48px);
    height: 1px;
    background: #eee;
    /* background: red; */
    position: absolute;
    top: 0;
    left: -24px;
    transform: scaleY(0.5);
  }
}
</style>
