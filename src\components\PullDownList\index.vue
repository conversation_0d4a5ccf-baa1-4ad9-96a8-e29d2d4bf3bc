<template>
  <div class="h-full">
    <van-pull-refresh v-model="refreshing" @refresh="onRefreshFn">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        :finished-text="dataList.length == total && dataList.length >= pageParams.pageSize ? '已显示全部数据' : ''"
        @load="loadmoreFn"
        :offset="pageParams.pageSize"
        :immediate-check="false"
      >
        <div class="van-clearfix">
          <slot :sourceData="dataList" name="data"></slot>
        </div>
      </van-list>
    </van-pull-refresh>
    <!--  -->
    <div class="noData flx-center flex-column" v-if="noDataflag">
      <img src="@/assets/image/null.png" alt="" />
      <div>暂无数据</div>
    </div>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'
import useRequestHook from '@/hooks/useTable'
const props = defineProps({
  // 接口地址
  apiUrl: {
    type: String,
    default: '',
  },
  // 请求参数
  queryParams: {
    type: Object,
    default: () => {},
  },
  // 数据处理函数
  callback: {
    type: Function,
  },
  // 是否自动发起请求(默认自动)
  requestAuto: {
    type: Boolean,
    default: true,
  },
  requestType: {
    //请求类型
    type: String,
    default: 'page_index',
  },
})
const broRef = ref(null)
const selfRef = ref(null)

const { pageParams, total, loading, refreshing, finished, noDataflag, getDataList, dataList, onRefresh, loadmore } =
  useRequestHook(props.apiUrl, props.callback, props.requestType, props.queryParams)
onMounted(() => {
  props.requestAuto && getDataList()
})

// 监听页面 queryParams 改化，重新获取数据
watch(
  () => props.queryParams,
  newVal => {
    // console.log('下拉刷新', newVal)
    props.requestAuto && onRefresh(newVal)
  },
  { deep: true }
)
const onRefreshFn = params => {
  onRefresh(params ? params : props.queryParams)
}
// 动态改变url
const onUrlChangeRefreshFn = (queryParams, url) => {
  // console.log('111', props.queryParams)
  onRefresh(queryParams ? queryParams : props.queryParams, url)
}
const loadmoreFn = () => {
  loadmore()
}
// 导出变量，将变量暴露给外部组件
defineExpose({
  pageParams,
  total,
  loading,
  refreshing,
  finished,
  noDataflag,
  dataList,
  onRefreshFn,
  loadmoreFn,
  getDataList,
  onUrlChangeRefreshFn,
})
</script>
