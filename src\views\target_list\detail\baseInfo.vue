<template>
  <div class="mt-24 h-max">
    <van-collapse v-model="activeNames">
      <!-- <van-collapse-item title="登记信息" name="1" readonly :border="false" :is-link="false">
        <div v-for="(item, i) in informations" class="px-24 pt-32" :class="[i === 0 ? 'border-b border-[#eee]' : '']">
          <div v-for="info in item" class="flex justify-between pb-32 text-28">
            <span class="text-secondary">{{ info.label }}</span>
            <span class="text-tertiary">{{ getValue(info.prop) }}</span>
          </div>
        </div>
      </van-collapse-item> -->
      <van-collapse-item title="经营范围" name="1" class="mt-24" :border="false">
        <div class="p-24 text-tertiary">
          {{ data.operate_range }}
        </div>
      </van-collapse-item>
      <van-collapse-item title="高管信息" name="2" class="mt-24" :border="false">
        <template #title>
          <span>高管信息</span>
          <span :class="totalClass">
            {{ data.personnels?.length }}
          </span>
        </template>
        <div class="p-24">
          <div
            v-for="(person, index) in personnels"
            class="flex items-center p-16 text-28 bg-grey"
            :class="{
              'mb-24': index !== personnels.length - 1,
            }"
          >
            <img v-if="person.logo" :src="person.logo" class="w-56 h-56 mr-24" />
            <SvgIcon v-else name="avatar" class="w-56 h-56 mr-24" />
            <span class="mr-24 font-semibold text-primary">{{ person.name }}</span>
            <span>{{ person.position || '' }}</span>
          </div>

          <div
            v-if="data.personnels?.length > 3 && !isShowAllPerson"
            class="flex justify-center text-[#1E75DB] text-28 pt-40"
            @click="isShowAllPerson = true"
          >
            查看全部
          </div>
        </div>
      </van-collapse-item>
      <van-collapse-item title="年度规模/效益" name="3" class="mt-24" :border="false">
        <div class="p-24">
          <div class="flex items-center justify-around py-28 px-36 bg-[#f5f7fe]">
            <div class="flex items-center text-tertiary w-[49%]">
              <SvgIcon name="scale" class="w-40 h-40" />
              <span class="mx-8">企业规模</span>
              <span class="text-36 font-[500] text-selected">{{ annualBenefit.vend_inc }}</span>
            </div>
            <div class="w-1 h-48 bg-[#eee]"></div>
            <div class="flex items-center text-tertiary w-[49%] justify-end">
              <SvgIcon name="benefit" class="w-40 h-40" />
              <span class="mx-8">企业效益</span>
              <span class="text-36 font-[500] text-selected">{{ annualBenefit.rat_gro }}</span>
            </div>
          </div>
          <div class="flex items-center mt-24 mb-16 text-primary">
            <span class="inline-block w-4 mr-8 h-28 bg-signal"></span>
            利润表
          </div>
          <Table :columns="columns1" :data="financeReport" />
          <div class="flex items-center mt-24 mb-16 text-primary">
            <span class="inline-block w-4 mr-8 h-28 bg-signal"></span>
            资产负债表
          </div>
          <Table :columns="columns2" :data="financeReport" />
        </div>
      </van-collapse-item>

      <van-collapse-item title="融资历程" name="4" class="mt-24" :border="false">
        <div class="p-24 text-tertiary">
          <steps v-if="financingData.length" :data="isShowAllPerson ? financingData : financingData.slice(0, 3)" />
          <Empty v-else> 暂无数据 </Empty>
          <div
            v-if="financingData.length > 3 && !isShowAllFinancing"
            class="flex justify-center text-[#1E75DB] text-28"
            @click="isShowAllFinancing = true"
          >
            查看全部
          </div>
        </div>
      </van-collapse-item>

      <van-collapse-item name="5" class="mt-24" :border="false">
        <template #title>
          <span>权威产业榜单收录情况</span>
          <span :class="totalClass">
            {{ authorityData?.length }}
          </span>
        </template>
        <template v-if="authorityData.length">
          <div
            class="p-24 border-b text-primary text-28 min-h-96 border-[#eee] flex items-center"
            v-for="item in isShowAllAuthority ? authorityData : authorityData.slice(0, 3)"
          >
            {{ item.theme_name }}
            <span class="px-10 bg-[rgba(160,165,186,0.1)] h-36 ml-16 break-keep">{{ item.theme_type }}</span>
          </div>
          <div
            v-if="authorityData.length > 3 && !isShowAllAuthority"
            class="flex justify-center text-[#1E75DB] text-28 py-16 pb-24"
            @click="isShowAllAuthority = true"
          >
            查看全部
          </div>
        </template>
        <Empty v-else> 暂无数据 </Empty>
      </van-collapse-item>

      <van-collapse-item id="patentWrapper" name="6" class="mt-24" readonly :border="false">
        <template #title>
          <span>专利信息</span>
          <span :class="totalClass">
            {{ entPatentTotal }}
          </span>
        </template>
        <template #right-icon>
          <span class="flex items-center text-28 text-[#9b9eac] mr-8" @click="handleMorePatent">
            更多
            <SvgIcon name="arrow" class="w-24 h-24" />
          </span>
        </template>
        <div v-if="entPatent.length">
          <div
            v-for="(item, index) in entPatent"
            :key="index"
            class="py-32 px-24 border-[#eee]"
            :class="{ 'border-b': index !== entPatent.length }"
          >
            <div class="flex justify-between mb-16">
              <span class="font-semibold text-primary text-28">{{ item.patent_name }}</span>
              <!-- <span class="flex items-center px-10 border text-danger rounded-4 border-danger break-keep h-36 text-24">
                {{ item.type }}
              </span> -->
            </div>

            <p class="mb-12 text-24 text-secondary">
              专利号： <span class="text-tertiary">{{ item.patent_no }}</span>
            </p>
            <p class="mb-12 text-24 text-secondary">
              专利类型： <span class="text-tertiary">{{ item.type }}</span>
            </p>
            <p class="text-24 text-secondary">
              IPC分类： <span class="text-tertiary">{{ item.ipc_type }}</span>
            </p>
            <p class="mb-12 text-24 text-secondary">
              申请日期： <span class="text-tertiary">{{ item.release_date }}</span>
            </p>
          </div>
        </div>
        <Empty v-else> 暂无数据 </Empty>
      </van-collapse-item>

      <van-collapse-item id="investmentWrapper" title="对外投资" name="7" class="mt-24" readonly :border="false">
        <van-tabs>
          <van-tab title="企业列表">
            <div v-if="investmentList.length">
              <div
                v-for="(item, index) in investmentList"
                :key="index"
                class="px-24 py-32 border-b border-[#eee]"
                :class="index === 0 ? 'border-t' : ''"
              >
                <div class="flex justify-between mb-16">
                  <span class="text-primary text-28 text-font-semibold">{{ item.ent_name }}</span>
                  <span
                    class="flex items-center px-10 border rounded-4 text-24"
                    :class="`${STATUS_MAP[item.ent_status] || STATUS_MAP.default}`"
                  >
                    {{ item.ent_status }}
                  </span>
                </div>

                <div class="[&{p}]:text-secondary [&{p}]:mb-12 text-24 [&{span}]:text-tertiary">
                  <p>
                    法定代表人：<span>{{ item.fa_ren }}</span>
                  </p>
                  <div class="flex [&>p]:w-[50%] flex-wrap">
                    <p>
                      投资数额：<span>{{ item.sub_con_am_usd }}万元</span>
                    </p>
                    <p>
                      持股比例：<span>{{ item.con_prop }}%</span>
                    </p>
                    <p class="mb-0">
                      成立日期：<span>{{ item.es_date }}</span>
                    </p>
                    <p class="mb-0">
                      投资时间：<span>{{ item.con_date }}</span>
                    </p>
                  </div>
                </div>
              </div>

              <div class="flex justify-center text-[#1E75DB] text-28 py-32 pb-24" @click="handleMoreInvestment">
                查看更多
              </div>
            </div>
            <Empty v-else> 暂无数据 </Empty>
          </van-tab>
          <van-tab title="投资地图">
            <MapChart class="mb-24" title="企业对外投资区域分布图（前五）" :showTotal="false" :data="heatMapData" />

            <div v-for="item in heatMapData.slice(0, 5)" :key="item.name" class="px-24 text-tertiary text-24">
              <div class="flex justify-between">
                <span>{{ item.name }}</span>
                <span>{{ item.value }}家</span>
              </div>

              <div class="w-full bg-[#eee] h-12 mt-12 mb-32 rounded-12 relative">
                <span
                  class="absolute h-12 bg-progress rounded-12"
                  :style="{ width: `${(item.value / heatMapData[0].value) * 100}%` }"
                ></span>
              </div>
            </div>

            <PieChart class="[&>ul]:px-24" title="企业对外投资行业占比图（前五）" :data="investRatioData" />
          </van-tab>
        </van-tabs>
      </van-collapse-item>
    </van-collapse>
  </div>
</template>

<script setup>
import { defineProps, computed, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  getAnnualBenefit,
  getEntFinancing,
  getEntPatent,
  getInvestAreaSpread,
  getInvestIndustryRatio,
  getAuthorityList,
  getInvestmentList,
} from '@/api/qualityReport'
import Steps from './steps.vue'
import { provinces } from '@/utils/maps'
import Table from './components/table.vue'
import { getEntFinanceReport } from '@/api/qualityReport'
import MapChart from '@/views/supply_chain/component/Map.vue'
import PieChart from '@/views/supply_chain/component/Pie.vue'
import { pageChangeHandler } from '@/utils'
import Empty from './components/empty.vue'

const route = useRoute()
const router = useRouter()
const props = defineProps({
  data: {
    type: Object,
    default: () => { },
  },
})

// const anchorDom = ref(null)
// onActivated(() => {
//   if (anchorDom.value) {
//     setTimeout(() => {
//       const dom = document.querySelector(`#${anchorDom.value}`)
//       dom?.scrollIntoView({ block: 'nearest', behavior: 'smooth' })
//       anchorDom.value = null
//     })
//   }
// })

const totalClass = `px-14 h-48 inline-block text-center rounded-full bg-[#F5F7FE] ml-12 text-32 font-semibold text-selected`

const STATUS_MAP = {
  吊销: '!text-danger !border-danger',
  在营: '!text-[#26c8a7] !border-[#26c8a7]',
  default: '!text-danger !border-danger',
}

const activeNames = ref(['1', '2', '3', '4', '5', '6', '7', '8'])
const isShowAllPerson = ref(false)
const personnels = computed(() => {
  return isShowAllPerson.value ? props.data.personnels : props.data.personnels?.slice?.(0, 3)
})

const columns1 = [
  { title: '年份', prop: 'ancheyear', class: 'mb-16', cellClass: 'text-[#1E75DB]' },
  { title: '营业收入（亿）', prop: 'vendinc' },
  { title: '同比变化', prop: 'vendinc_change', class: 'mb-16' },
  { title: '利润总额（亿）', prop: 'progro' },
  { title: '同比变化', prop: 'progro_change', class: 'mb-16' },
  { title: '净利润（亿）', prop: 'netinc' },
  { title: '同比变化', prop: 'netinc_change' },
]
const columns2 = [
  { title: '年份', prop: 'ancheyear', cellClass: 'text-[#1E75DB]' },
  { title: '负债总额（亿）', prop: 'liagro' },
  { title: '资产总额（亿）', prop: 'assgro' },
  { title: '净资产（亿））', prop: 'net_asset' },
]

const financeReport = ref([])
getEntFinanceReport(route.query.ent_id).then(res => {
  financeReport.value = res.datalist || []
})

// const informations = [
//   [
//     { label: '统一社会信用代码', prop: 'usc_code' },
//     { label: '组织机构代码', prop: 'org_code' },
//     { label: '工商注册号', prop: 'br_no' },
//     { label: '纳税人识别号', prop: 'ti_no' },
//     // { label: '纳税人资质', prop: '' },
//   ],
//   [
//     { label: '核准日期', prop: 'authorize_date' },
//     { label: '登记机关', prop: 'register_department' },
//     { label: '企业类型', prop: 'org_type' },
//     { label: '营业期限', prop: ['operate_date_start', 'operate_date_end'] },
//     { label: '所属地区', prop: 'region_id' },
//   ],
// ]

// const getValue = prop => {
//   if (typeof prop === 'string') {
//     return props.data[prop]
//   } else {
//     let result = []
//     prop?.forEach(key => {
//       result.push(props.data[key])
//     })
//     if (!result[0]) {
//       return ``
//     }

//     return `${result[0] || ''}至${result[1] || '无固定期限'}`
//   }
// }

const annualBenefit = ref({})
getAnnualBenefit(route.query.ent_id).then(res => {
  annualBenefit.value = res
})

const financingData = ref([])
const isShowAllFinancing = ref(false)
getEntFinancing(route.query.ent_id).then(res => {
  financingData.value = res?.datalist?.map(item => ({ ...item.single_inv, org: item.org || [] })) || []
})

const entPatent = ref([])
const entPatentTotal = ref(0)
getEntPatent({ ent_id: route.query.ent_id, page_index: 1, page_size: 3, sort: { name: 'sqrq', order: 'DESC' } }).then(
  res => {
    entPatent.value = res.items || []
    entPatentTotal.value = res.count
  }
)

const authorityData = ref([])
const isShowAllAuthority = ref(false)
getAuthorityList({ ent_id: route.query.ent_id }).then(res => {
  authorityData.value = res?.datalist || []
})

const handleMorePatent = () => {
  anchorDom.value = 'patentWrapper'
  pageChangeHandler('/target-detail/patent', {
    ent_id: route.query.ent_id,
  })
}

// 对外投资区域分布
const heatMapData = ref([])

getInvestAreaSpread(route.query.ent_id).then(res => {
  const result = provinces
    .slice(1)
    .map(item => {
      return {
        id: item.code,
        region: item.code,
        name: item.fullName,
        // shortName: item.name,
        value: res.datalist?.find(i => i.province === item.fullName)?.count || 0,
      }
    })
    .sort((a, b) => b.value - a.value)

  const filters = ['香港', '澳门', '台湾']
  const data = result.filter(item => !filters.some(item1 => item.name.includes(item1))).slice(0, 5)
  heatMapData.value = data
})

const investRatioData = ref([])
getInvestIndustryRatio(route.query.ent_id).then(res => {
  investRatioData.value =
    res?.datalist
      ?.map(item => ({
        name: item.nic_id,
        total: item.count,
        ...item,
      }))
      .filter(item => item.name !== '其他') || []
})

const investmentList = ref([])
getInvestmentList({ ent_id: route.query.ent_id, page_index: 1, page_size: 5 }).then(res => {
  investmentList.value = res?.datalist || []
})

const handleMoreInvestment = () => {
  anchorDom.value = 'investmentWrapper'
  pageChangeHandler('/target-detail/investment', {
    ent_id: route.query.ent_id,
  })
}
</script>

<style lang="scss" scoped>
:deep(.van-collapse-item__content) {
  padding: 0;
}

:deep(.van-collapse-item__title) {
  padding: 24px;
  color: #20263a;
  font-size: 32px;
  font-weight: 600;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
}
</style>
