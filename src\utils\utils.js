import questObj from '@/api/index'
/**
 *拷贝
 * @param {*} any
 * @returns any
 */
function clone(any) {
  switch (Object.prototype.toString.call(any).slice(8, -1)) {
    case 'Object':
      let o = {}
      for (let key in any) {
        o[key] = clone(any[key])
      }
      return o
    case 'Array':
      return any.map(clone)
    case 'Function':
      return new Function('return ' + any.toString())()
    case 'Date':
      return new Date(any.valueOf())
    case 'RegExp':
      return new RegExp(any)
    case 'Map':
      let m = new Map()
      any.forEach((v, k) => {
        m.set(k, clone(v))
      })
      return m
    case 'Set':
      return new Set([...any].map(clone))
    default:
      return any
  }
}
/**
 * 节流函数
 * @param {*} fn
 * @param {*} interval
 * @returns
 */
function debounce(fn, interval) {
  var timer
  var gapTime = interval || 1000 //间隔时间，如果interval不传，则默认1000ms
  return function () {
    clearTimeout(timer)
    var context = this
    var args = arguments //保存此处的arguments，因为setTimeout是全局的，arguments不是防抖函数需要的。
    timer = setTimeout(function () {
      fn.call(context, args)
    }, gapTime)
  }
}
/**
 * 对象里面--字段里面的字符串--返回成数组
 * @param {为发请求后台返回的值-数组} res
 * @param { 数组里面需要高亮对象里面的字段--为了拿到值} fild
 * @param {搜索框里的内容} inputVal
 * @returns 最后返回一个fild字段为数组的res数组
 */
function handleSearchHight(res, fild, inputVal) {
  if (!Array.isArray(res) || typeof fild != 'string') {
    return console.warn('请输入正确格式！')
  }
  let ary = res
  const getInf = (str, key) => str.replace(new RegExp(`${key}`, 'g'), `%%${key}%%`).split('%%')
  ary.forEach((item, index, arr) => (arr[index][fild] = getInf(item[fild], inputVal).filter(str => !!str)))
  // this.handleSearchHight(new_blogs, 'title', 'a') 调用
  return ary
}
/**
 * 将请求同步话
 * @param {请求名字} name
 * @param {请求阐述} data
 * @returns  异步结果
 */
async function to(name, data) {
  //如果要用promise.all就不能用这套
  return await questObj[name](data)
    .then(data => [null, data]) // 成功,返回[null,响应结果]
    .catch(err => {
      return [err, undefined] // 失败,返回[错误信息,undefined]
    })
}
/**
 *
 * @returns 返回当前日期时分秒
 */
function getDateTime(date = new Date()) {
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份加前导零
  const day = date.getDate().toString().padStart(2, '0') // 日期加前导零
  const hours = date.getHours().toString().padStart(2, '0') // 小时加前导零
  const minutes = date.getMinutes().toString().padStart(2, '0') // 分钟加前导零
  const seconds = date.getSeconds().toString().padStart(2, '0') // 秒数加前导零

  return {
    year,
    month,
    day,
    hours,
    minutes,
    seconds,
  }
}

/**
 * 导航到h5
 * @param {id,路由路径,当前程序名字} param0
 */
function goH5(params) {
  if (!params.entId) {
    return
  }
  const domainName = 'https://reporth5.handidit.com'
  // const domainName = 'http://j-h5-report.ihdwork.com'
  const { entId = '', src = '/', type = 'YuQiCha', ...rest } = params
  const queryString = Object.entries({ entId, entid: entId, type, ...rest })
    .filter(([key, value]) => value !== '')
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&')
  const fullUrl = `${domainName}${src}?${queryString}`
  window.open(fullUrl, '_self')
}

/***
 * 获取url 参数对象
 * @returns {{}}
 */
function getUrlKey() { //截取url 参数
  let urlParams = {};
  let name, value, str = location.href, num = str.indexOf("?"); //取得整个地址栏
  str = str.substr(num + 1); //取得所有参数 stringvar.substr(start [, length ]
  let arr = str.split("&"); //各个参数放到数组里
  for (let i = 0; i < arr.length; i++) {
    num = arr[i].indexOf("=");
    if (num > 0) {
      name = arr[i].substring(0, num);
      value = arr[i].substr(num + 1);
      urlParams[name] = decodeURI(value)
    }
  }
  return urlParams;
}

export { clone, debounce, handleSearchHight, getDateTime, goH5, to, getUrlKey }
