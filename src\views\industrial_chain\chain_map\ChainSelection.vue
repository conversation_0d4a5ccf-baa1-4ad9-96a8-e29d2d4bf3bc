<template>
  <div v-if="visible" class="dropdown-overlay" @click="close" :style="{ top: headHeight + 'px' }">
    <div class="dropdown-content" @click.stop>
      <div class="chain-wrap">
        <!-- 第一级：产业大类 -->
        <div class="parent">
          <div v-if="data.loading" class="loading-container">
            <div class="loading-text">加载中...</div>
          </div>
          <div
            v-else
            v-for="item in data.chainObj.parentList"
            :key="item.chain_code"
            @click="selectChain(item)"
            :class="{ item: true, active: item.active }"
          >
            <div class="name text-ellipsis">{{ item.name }}</div>
            <img
              v-if="item.selected && data.finalSelection && data.finalSelection.level === '1'"
              src="@/assets/image/cyl/i_gougou.png"
              class="check-icon"
            />
          </div>
        </div>
        <!-- 第二级：具体产业链 -->
        <div class="children flex-1">
          <div v-if="data.childLoading" class="loading-container">
            <div class="loading-text">加载中...</div>
          </div>
          <div
            v-else
            v-for="item in data.chainObj.curChildList"
            :key="item.chain_code"
            @click="selectChain(item)"
            class="child-item text-ellipsis"
          >
            <span class="child-name" :class="{ active: item.selected }">{{ item.name }}</span>
            <img
              v-if="item.selected && data.finalSelection && data.finalSelection.level === '2'"
              src="@/assets/image/cyl/i_gougou.png"
              class="check-icon"
            />
          </div>
        </div>
        <!-- 第三级：产业链节点（如果有的话） -->
        <div class="children flex-1" v-if="data.chainObj.curNodeList.length > 0">
          <div
            v-for="item in data.chainObj.curNodeList"
            :key="item.chain_code"
            @click="selectChain(item)"
            class="child-item text-ellipsis"
          >
            <span class="child-name" :class="{ active: item.selected }">{{ item.name }}</span>
            <img
              v-if="item.selected && data.finalSelection && data.finalSelection.level === '3'"
              src="@/assets/image/cyl/i_gougou.png"
              class="check-icon"
            />
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="footer">
        <div class="btn-group">
          <button class="btn reset-btn" @click="reset">重置</button>
          <button class="btn submit-btn" @click="submit">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, onMounted } from 'vue'
import { getChainList, getChildNodeList } from '@/api/iChain/index'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  defaultCode: {
    type: [String, Object, Array],
    default: '',
  },
  headHeight: {
    type: Number,
    default: 88,
  },
})

const emits = defineEmits(['close', 'submit'])

const data = reactive({
  visible: false,
  loading: false, // 第一级加载状态
  childLoading: false, // 第二级加载状态
  chainObj: {
    parentList: [], // 第一级：产业大类
    curChildList: [], // 第二级：具体产业链
    curNodeList: [], // 第三级：产业链节点
  },
  childObj: {}, // 缓存子级数据
  nodeObj: {}, // 缓存节点数据
  selectedPath: {
    parent: null, // 选中的产业大类
    child: null, // 选中的具体产业链
    node: null, // 选中的产业链节点
  },
  initialPath: {
    parent: null, // 路由初始的产业大类
    child: null, // 路由初始的具体产业链
    node: null, // 路由初始的产业链节点
  },
  openingState: null, // 弹窗打开时的状态，用于重置
  finalSelection: null,
  readyToShow: false,
})

// 监听显示状态
watch(
  () => props.visible,
  visible => {
    data.visible = visible
    if (visible) {
      // 保存弹窗打开时的状态作为重置基准
      data.openingState = {
        selectedPath: {
          parent: data.selectedPath.parent ? { ...data.selectedPath.parent } : null,
          child: data.selectedPath.child ? { ...data.selectedPath.child } : null,
          node: data.selectedPath.node ? { ...data.selectedPath.node } : null,
        },
        finalSelection: data.finalSelection ? { ...data.finalSelection } : null,
      }

      if (props.defaultCode) {
        data.readyToShow = false
        const code = extractCodeFromDefaultCode(props.defaultCode)
        if (code) {
          expandToCode(code)
        } else {
          data.readyToShow = true
        }
      } else {
        data.readyToShow = true
      }
    }
  },
  { immediate: true }
)

/**
 * 从 defaultCode 中提取 code 值
 */
const extractCodeFromDefaultCode = defaultCode => {
  if (!defaultCode) return ''

  if (typeof defaultCode === 'string') {
    return defaultCode
  }

  if (Array.isArray(defaultCode)) {
    if (defaultCode.length === 0) return ''
    const firstItem = defaultCode[0]
    if (typeof firstItem === 'string') {
      return firstItem
    }
    if (typeof firstItem === 'object' && firstItem.chain_code) {
      return firstItem.chain_code
    }
    return ''
  }

  if (typeof defaultCode === 'object' && defaultCode.chain_code) {
    return defaultCode.chain_code
  }

  return ''
}

/**
 * 展开到指定代码
 */
const expandToCode = async targetCode => {
  if (!targetCode) return

  const { chainObj, childObj, nodeObj } = data
  let selectedPath = {
    parent: null,
    child: null,
    node: null,
  }

  // 先确保有第一级数据
  if (chainObj.parentList.length === 0) {
    await getChainData()
  }

  // 1. 在第一级中查找
  let targetItem = chainObj.parentList.find(item => item.chain_code === targetCode)
  if (targetItem) {
    selectedPath.parent = targetItem
    // 加载第二级数据
    await loadChildData(targetCode)
  } else {
    // 2. 在第二级中查找
    let foundInSecond = false
    for (let parentCode in childObj) {
      targetItem = childObj[parentCode]?.find(item => item.chain_code === targetCode)
      if (targetItem) {
        const parent = chainObj.parentList.find(p => p.chain_code === parentCode)
        selectedPath.parent = parent
        selectedPath.child = targetItem
        await loadChildData(parentCode)
        foundInSecond = true
        break
      }
    }

    // 3. 如果还没找到，在第三级中查找
    if (!foundInSecond) {
      for (let childCode in nodeObj) {
        targetItem = nodeObj[childCode]?.find(item => item.chain_code === targetCode)
        if (targetItem) {
          // 找到第三级，需要找到对应的第一级和第二级
          const secondItem = Object.values(childObj)
            .flat()
            .find(item => item.chain_code === childCode)
          if (secondItem) {
            const parent = chainObj.parentList.find(p => p.chain_code === secondItem.parentCode)
            selectedPath.parent = parent
            selectedPath.child = secondItem
            selectedPath.node = targetItem
            await loadChildData(secondItem.parentCode)
            await loadNodeData(childCode)
          }
          break
        }
      }
    }
  }

  data.selectedPath = selectedPath
  data.finalSelection = getFinalSelection(selectedPath)

  // 如果是通过 defaultCode 初始化的，保存为初始路由值
  if (targetCode && selectedPath.parent) {
    data.initialPath = {
      parent: selectedPath.parent,
      child: selectedPath.child,
      node: selectedPath.node,
    }
  }

  data.readyToShow = true

  updateListSelections()
}

/**
 * 获取产业链数据
 */
const getChainData = async () => {
  try {
    data.loading = true
    const res = await getChainList()
    const chainData = res || []

    // 处理数据格式：取所有第二层作为第一级数据
    const firstLevelData = []
    chainData.forEach(item => {
      if (item.children && item.children.length > 0) {
        item.children.forEach(child => {
          firstLevelData.push({
            ...child,
            level: '1',
            selected: false,
            active: false,
          })
        })
      }
    })

    data.chainObj.parentList = firstLevelData
    data.readyToShow = true
  } catch (error) {
    console.error('获取产业链数据失败:', error)
    data.chainObj.parentList = []
    data.readyToShow = true
  } finally {
    data.loading = false
  }
}

/**
 * 加载子级数据（具体产业链）
 */
const loadChildData = async parentCode => {
  const { childObj, chainObj } = data
  if (!childObj[parentCode]) {
    try {
      data.childLoading = true
      // 调用 getChildNodeList 获取动态数据
      const res = await getChildNodeList(parentCode)
      const datalist = res?.datalist || []

      if (datalist.length > 0) {
        const detail = datalist[0]?.detail || []

        // 第二级数据：detail 数组
        const secondLevelData = detail.map(item => ({
          ...item,
          chain_code: item.expert_chain_code,
          level: '2',
          selected: false,
          active: false,
          parentCode: parentCode, // 保存父级code用于回显
        }))

        childObj[parentCode] = secondLevelData

        // 同时缓存第三级数据
        detail.forEach(secondItem => {
          if (secondItem.childs && secondItem.childs.length > 0) {
            const thirdLevelData = secondItem.childs.map(child => ({
              ...child,
              chain_code: child.expert_chain_code || child.chain_code || child.tag_id, // 确保有chain_code
              level: '3',
              selected: false,
              active: false,
              parentCode: secondItem.expert_chain_code, // 保存父级code
            }))
            data.nodeObj[secondItem.expert_chain_code] = thirdLevelData
          }
        })
      } else {
        childObj[parentCode] = []
      }
    } catch (error) {
      console.error('获取子级数据失败:', error)
      childObj[parentCode] = []
    } finally {
      data.childLoading = false
    }
  } else {
    data.childLoading = false
  }
  chainObj.curChildList = childObj[parentCode] || []
}

/**
 * 加载节点数据（产业链节点）
 */
const loadNodeData = async childCode => {
  const { nodeObj, chainObj } = data
  // 第三级数据已经在 loadChildData 中缓存了
  const nodeList = nodeObj[childCode] || []

  // 确保所有第三级项目都不被选中（除非在selectedPath中指定）
  chainObj.curNodeList = nodeList.map(item => ({
    ...item,
    selected: data.selectedPath.node?.chain_code === item.chain_code,
  }))
}

/**
 * 更新列表选中状态
 */
const updateListSelections = () => {
  const { chainObj, selectedPath } = data

  chainObj.parentList = chainObj.parentList.map(item => ({
    ...item,
    selected: selectedPath.parent?.chain_code === item.chain_code,
    active: selectedPath.parent?.chain_code === item.chain_code,
  }))

  // 只有当数组不为空时才更新选中状态
  if (chainObj.curChildList.length > 0) {
    chainObj.curChildList = chainObj.curChildList.map(item => ({
      ...item,
      selected: selectedPath.child?.chain_code === item.chain_code,
      active: selectedPath.child?.chain_code === item.chain_code,
    }))
  }

  // 只有当数组不为空时才更新选中状态
  if (chainObj.curNodeList.length > 0) {
    chainObj.curNodeList = chainObj.curNodeList.map(item => ({
      ...item,
      selected: selectedPath.node?.chain_code === item.chain_code,
    }))
  }
}

/**
 * 获取最终选择
 */
const getFinalSelection = (path = null) => {
  const selectedPath = path || data.selectedPath
  return selectedPath.node || selectedPath.child || selectedPath.parent
}

/**
 * 选择产业链
 */
const selectChain = async item => {
  const { chain_code, level } = item

  if (level === '1') {
    // 选择第一级
    data.selectedPath.parent = item
    data.selectedPath.child = null
    data.selectedPath.node = null

    // 立即清空第二级和第三级数据
    data.chainObj.curChildList = []
    data.chainObj.curNodeList = []

    // 加载第二级数据
    await loadChildData(chain_code)
  } else if (level === '2') {
    // 选择第二级
    data.selectedPath.child = item
    data.selectedPath.node = null

    // 加载第三级数据
    loadNodeData(chain_code)
  } else if (level === '3') {
    // 选择第三级
    data.selectedPath.node = item
  }

  data.finalSelection = getFinalSelection()
  updateListSelections()
}

/**
 * 重置选择
 */
const reset = async () => {
  if (data.openingState) {
    // 恢复到弹窗打开时的状态
    data.selectedPath = {
      parent: data.openingState.selectedPath.parent ? { ...data.openingState.selectedPath.parent } : null,
      child: data.openingState.selectedPath.child ? { ...data.openingState.selectedPath.child } : null,
      node: data.openingState.selectedPath.node ? { ...data.openingState.selectedPath.node } : null,
    }
    data.finalSelection = data.openingState.finalSelection ? { ...data.openingState.finalSelection } : null

    // 重新加载对应的数据以确保UI正确显示
    if (data.selectedPath.parent) {
      await loadChildData(data.selectedPath.parent.chain_code)

      if (data.selectedPath.child) {
        await loadNodeData(data.selectedPath.child.chain_code)
      } else {
        data.chainObj.curNodeList = []
      }
    } else {
      data.chainObj.curChildList = []
      data.chainObj.curNodeList = []
    }
  } else {
    // 如果没有保存的状态，重置为空
    data.selectedPath = {
      parent: null,
      child: null,
      node: null,
    }
    data.finalSelection = null
    data.chainObj.curChildList = []
    data.chainObj.curNodeList = []
  }

  // 更新选中状态
  updateListSelections()
}

/**
 * 提交选择
 */
const submit = () => {
  const selection = getFinalSelection()
  if (selection) {
    // 获取父级code用于回显
    let parentCode = null
    if (selection.level === '2' && selection.parentCode) {
      parentCode = selection.parentCode
    } else if (selection.level === '3' && data.selectedPath.child) {
      parentCode = data.selectedPath.child.parentCode
    } else if (selection.level === '1') {
      parentCode = selection.chain_code
    }

    emits('submit', {
      selection,
      path: data.selectedPath,
      parentCode: parentCode, // 用于回显的父级code
    })
  }
}

/**
 * 关闭弹窗
 */
const close = () => {
  emits('close')
}

// 页面挂载时获取数据
onMounted(() => {
  getChainData()
})
</script>

<style lang="scss" scoped>
.dropdown-overlay {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 100;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.dropdown-content {
  width: 100vw;
  max-width: 100vw;
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 16px 16px;
  overflow: hidden;
}

.chain-wrap {
  background-color: #fff;
  display: flex;
  height: 756px;
}

.parent {
  width: 308px;
  height: 756px;
  background-color: rgba(247, 247, 247, 0.6);
  overflow-y: scroll;
  scroll-behavior: smooth;
  position: relative;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}

.item {
  min-height: 84px;
  padding: 16px 24px;
  font-size: 26px;
  color: #20263a;
  font-family: PingFang SC-Regular, PingFang SC;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.item.active {
  background-color: #fff;
  font-family: PingFang SC-Semibold, PingFang SC;
  .name {
    font-size: 26px;
    font-weight: 600;
    color: #07a6f0 !important;
  }
}

.item .name {
  width: 240px;
  font-weight: 400;
  font-size: 26px;
  color: #404040;
}

.text-ellipsis {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.2;
}

// 子级项目样式
.child-item {
  min-height: 84px;
  padding: 16px 24px;
  font-size: 26px;
  color: #20263a;
  font-family: PingFang SC-Regular, PingFang SC;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  .check-icon {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    margin-left: 8px;
  }
  .child-name {
    flex: 1;
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    line-height: 1.3;

    &.active {
      color: #07a6f0 !important;
      font-weight: 600;
      font-family: PingFang SC-Semibold, PingFang SC;
    }
  }
}

.children {
  height: 756px;
  top: 0;
  overflow-y: scroll;
  scroll-behavior: smooth;
  position: relative;
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}

.check-icon {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
}

.footer {
  padding: 20px 24px;
  background: #fff;
  border-top: 1px solid #eee;
}

.btn-group {
  display: flex;
  gap: 16px;
}

.btn {
  flex: 1;
  height: 80px;
  border: none;
  border-radius: 8px;
  font-size: 28px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;

  &:hover {
    background: #e8e8e8;
  }
}

.submit-btn {
  background: linear-gradient(90deg, #4ab8ec 0%, #07a6f0 100%);
  color: #fff;

  &:hover {
    opacity: 0.9;
  }
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;

  .loading-text {
    font-size: 28px;
    color: #999;
  }
}
</style>
