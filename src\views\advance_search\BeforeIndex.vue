<template>
  <div class="before-index bg-[#f7f7f7] h-full">
    <div class="p-20 bg-white flex justify-between items-center top-search">
      <van-search
        v-model="ent_name"
        class="!p-0 w-[522px]"
        :maxlength="16"
        :clear-trigger="ent_name ? 'always' : 'focus'"
        placeholder="请输入企业名称，帮您查找企业"
        @input="getEntListNameData"
        @clear="() => {ent_name = ''}"
        @cancel="() => ((ent_name = ''))"
      />
      <span class="text-[#07A6F0] text-28" @click="goDetailSearch">高级查询</span>
      <div class="ent-list" v-if="showEntList">
        <div v-if="searchEntList.length">
          <div class="ent-name" v-for="ent in searchEntList" :key="ent.ent_id" @click="chooseEnt(ent)">{{ ent.ent_name }}</div>
        </div>
        <div v-else>
          <div class="loading">
            <div class="hloading size"></div>
            <!-- text -->
            <div class="text">{{ text }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="p-24 bg-white text-[#404040] text-32 mt-24 font-600">高频搜索</div>

    <div class="content-item-pop">
      <div class="title" :style="{ fontWeight: 400, color: '#404040' }">所属行业</div>
      <div class="content-item-pop-r" @click="popData.eleseicPop = true">
        <span :class="{has: trade_content}">{{ trade_content || '全部' }}</span>
        <img src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home_arrow.png" alt="" />
      </div>
    </div>
    <div class="content-item-pop">
      <div class="title" :style="{ fontWeight: 400, color: '#404040' }">所属地区</div>
      <div class="content-item-pop-r" @click="popData.regionPop = true">
        <span :class="{has: area_content}">{{ area_content || '全部' }}</span>
        <img src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home_arrow.png" alt="" />
      </div>
    </div>
    <div class="list-content">
      <div class="title">企业规模</div>
      <div class="list-item-wrap">
        <div
          class="item"
          v-for="item in scaleOptions.list"
          :key="item.id"
          :class="{ active: ent_scale.includes(item.id) }"
          @click="handleItemClick('ent_scale', item)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="list-content">
      <div class="title">效益评估</div>
      <div class="list-item-wrap">
        <div
          class="item"
          v-for="item in benefitOptions.list"
          :key="item.id"
          :class="{ active: benefit_assess.includes(item.id) }"
          @click="handleItemClick('benefit_assess', item)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="list-content">
      <div class="title">科技型企业</div>
      <div class="list-item-wrap">
        <div
          class="item"
          v-for="item in technologyOptions.list"
          :key="item.id"
          :class="{ active: technology_types.includes(item.id) }"
          @click="handleItemClick('technology_types', item)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>

    <!-- 地区弹窗 -->
    <MultiplecChoice
      :visible="popData.regionPop"
      mark="areas"
      dataType="DistrictAry"
      @submit="submitSub"
      :oldData="popData.params.regionData"
      @close="() => (popData.regionPop = false)"
    />
    <!-- 行业 -->
    <MultiplecChoice
      :visible="popData.eleseicPop"
      mark="trade_types"
      dataType="EleseicAry"
      @submit="submitSub"
      :oldData="popData.params.eleseic_data"
      @close="() => (popData.eleseicPop = false)"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {pageChangeHandler, debounce} from "@/utils";
import { getNameList } from "@/api/advanceSearch";
import MultiplecChoice from "@/components/Hunt/component/MultiplecChoice/index.vue";

const router = useRouter()
const ent_name = ref('')
const ent_scale = ref([]) // 企业规模
const benefit_assess = ref([]) // 效益评估
const technology_types = ref([]) // 科技型企业
const searchEntList = ref([]) // 搜索企业列表
const showEntList = ref(false)
const text = ref('加载中')

const getEntListNameData = debounce(async () => {
  searchEntList.value = []
  showEntList.value = true
  if (!ent_name.value) {
    searchEntList.value = []
    return
  }
  const res = await getNameList(ent_name.value)
  if( res.length === 0) {
    text.value = '暂无相关数据'
  } else {
    text.value = '加载中'
  }
  searchEntList.value = res
}, 300)
const chooseEnt = (ent) => {
  ent_name.value = ent.ent_name
  searchEntList.value = []
  showEntList.value = false
}

const popData = reactive({
  regionPop: false, // 地区弹窗
  eleseicPop: false, // 行业弹窗
  params: {
    regionData: [], // 地区数据
    eleseic_data: [], // 行业数据
  },
})

const trade_content = ref('') // 行业内容
const area_content = ref('') // 地区内容

// 处理item点击事件
const handleItemClick = (type, item) => {
  let targetArray
  let searchObjKey
  switch (type) {
    case 'ent_scale':
      targetArray = ent_scale
      searchObjKey = 'ent_scale'
      break
    case 'benefit_assess':
      targetArray = benefit_assess
      searchObjKey = 'benefit_assess'
      break
    case 'technology_types':
      targetArray = technology_types
      searchObjKey = 'technology_types'
      break
    default:
      return
  }

  const index = targetArray.value.indexOf(item.id)
  if (index > -1) {
    targetArray.value.splice(index, 1)
  } else {
    targetArray.value.push(item.id)
  }
  updateSearchObj(searchObjKey, targetArray.value)
}

// 更新search_obj
const updateSearchObj = (key, selectedIds) => {
  let options
  switch (key) {
    case 'ent_scale':
      options = scaleOptions.list
      break
    case 'benefit_assess':
      options = benefitOptions.list
      break
    case 'technology_types':
      options = technologyOptions.list
      break
    default:
      return
  }

  const labels = []
  const values = []

  selectedIds.forEach(id => {
    const option = options.find(opt => opt.id === id)
    if (option) {
      labels.push(option.name)
      values.push(option.id)
    }
  })

  search_obj.value[key].label = labels
  search_obj.value[key].value = values
}

const localBackup = ref({
  regionData: [],
  eleseic_data: [],
  area_content: '',
  trade_content: ''
})
const submitSub = (data) => {
  let labels = [];
  let values = [];

  if (data.mark === 'areas') {
    popData.params.regionData = data.checkedList
    localBackup.value.regionData = data.checkedList
    area_content.value = getAreaContent(data.checkedList, data.mark)
    localBackup.value.area_content = area_content.value
    data.checkedList.forEach(item => {
      labels.push(item.name)
      values.push(item.code)
    })
    search_obj.value.areas.label = labels
    search_obj.value.areas.value = values
  } else if (data.mark === 'trade_types') {
    trade_content.value = getTradeContent(data.checkedList, data.mark)
    localBackup.value.trade_content = trade_content.value
    popData.params.eleseic_data = data.checkedList
    localBackup.value.eleseic_data = data.checkedList
    data.checkedList.forEach(item => {
      labels.push(item.name)
      values.push(item.code)
    })
    search_obj.value.trade_types.label = labels
    search_obj.value.trade_types.value = values
  }
}

const scaleOptions = {
  title: '企业规模',
  type: 'ent_scale',
  icon: true,
  list: [
    { id: '0$500', name: '0-500:微型' },
    { id: '500$1000', name: '500-1000:小型A' },
    { id: '1000$2000', name: '1000-2000:小型B' },
    { id: '2000$5000', name: '2000-5000:中型A' },
    { id: '5000$10000', name: '5000-10000:中型B' },
    { id: '10000$20000', name: '10000-20000:大型A' },
    { id: '20000$30000', name: '20000-30000:大型B' },
    { id: '30000$', name: '大于30000:特大型' },
  ],
}
const benefitOptions = {
  title: '效益评估',
  type: 'benefit_assess',
  isOpenIcon: true,
  isOpen: true,
  list: [
    {id: '$50', name: '0-50:小型A',},
    {id: '50$100', name: '50-100:小型B',},
    {id: '100$200', name: '100-200:中型A',},
    {id: '200$300', name: '200-300:中型B',},
    {id: '300$500', name: '300-500:中型C',},
    {id: '500$1000', name: '500-1000:中型D',},
    {id: '1000$2000', name: '1000-2000:大型A',},
    {id: '2000$3000', name: '2000-3000:大型B',},
    {id: '3000$5000', name: '3000-5000:大型C',},
    {id: '5000$10000', name: '5000-10000:大型D',},
    {id: '10000$', name: '大于10000:特大型',},
  ],
}

const technologyOptions = {
  title: '科技型企业',
  type: 'technology_types',
  isOpenIcon: true,
  isOpen: true,
  list: [
    { name: '高新技术企业', id: 'HN' },
    { name: '科技型中小企业', id: 'MST' },
    { name: '瞪羚企业', id: 'G' },
    { name: '专精特新企业', id: 'PSN' },
    { name: '独角兽企业', id: 'U' },
    { name: '科技小巨人企业', id: 'TG' },
  ],
}
const search_obj = ref({
  technology_types: {
    prop: "technology_types",
    label: [],
    title: "科技型企业",
    value: []
  },
  ent_scale: {
    prop: "ent_scale",
    label: [],
    title: "企业规模",
    value: []
  },
  benefit_assess: {
    prop: "benefit_assess",
    label: [],
    title: "效益评估",
    value: []
  },
  areas: {
    prop: "areas",
    label: [],
    title: "所在地区",
    value: []
  },
  trade_types: {
    prop: "trade_types",
    label: [],
    title: "所属行业",
    value: []
  }
})

const goDetailSearch = () => {
  const query = {
    ent_name: ent_name.value,
    search_json: JSON.stringify(search_obj.value)
  }
  localStorage.setItem('searchBackup', JSON.stringify(query))
  localStorage.setItem('searchBackupData', JSON.stringify(localBackup.value))
  pageChangeHandler('/advance-search', query)
}

onMounted(() => {
  nextTick(()=>{
    const source = localStorage.getItem('searchBackup')
    const searchBackupData = localStorage.getItem('searchBackupData')
    if (source) {
      const { ent_name: storedEntName, search_json } = JSON.parse(source)
      const searchData = JSON.parse(search_json)

      ent_name.value = storedEntName || ''

      search_obj.value.technology_types.label = searchData.technology_types.label || []
      search_obj.value.technology_types.value = searchData.technology_types.value || []
      search_obj.value.ent_scale.label = searchData.ent_scale.label || []
      search_obj.value.ent_scale.value = searchData.ent_scale.value || []
      search_obj.value.benefit_assess.label = searchData.benefit_assess.label || []
      search_obj.value.benefit_assess.value = searchData.benefit_assess.value || []

      ent_scale.value = searchData.ent_scale.value || []
      benefit_assess.value = searchData.benefit_assess.value || []
      technology_types.value = searchData.technology_types.value || []

      localStorage.removeItem('searchBackup')
    }
    if(searchBackupData) {
      const searchBackup = JSON.parse(searchBackupData)
      const { eleseic_data, regionData } = searchBackup
      submitSub({
        checkedList: eleseic_data,
        mark: 'trade_types',
        visible: false
      })
      submitSub({
        checkedList: regionData,
        mark: 'areas',
        visible: false
      })

      localStorage.removeItem('searchBackupData')
    }
  })
})

function getContent(checkedList, options = {}) {
  if (!checkedList || !checkedList.length) return ''
  const {
    parentCondition = (item) => !item.parent,
    skipCondition = () => false,
    maxLevel = null,
    showOrphanChildren = false // 是否显示没有父级代表的子项
  } = options

  const parentMap = {}     // 父code -> 子项数组
  const itemMap = {}       // code -> item
  const selectedNames = [] // 最终展示的名字
  const hiddenChildren = new Set() // 被父级覆盖的子项 code

  checkedList.forEach(item => {
    itemMap[item.code] = item
    if (item.parent) {
      if (!parentMap[item.parent]) parentMap[item.parent] = []
      parentMap[item.parent].push(item)
    }
  })
  // 找出哪些父级已全选其子项 → 子项不再单独显示
  checkedList.forEach(item => {
    // 检查所有checked的项目
    if (item.status === 'checked') {
      const children = parentMap[item.code] || []
      const allChildrenChecked = children.length
        ? children.every(child => child.status === 'checked')
        : true
      if (allChildrenChecked && children.length > 0) {
        children.forEach(child => hiddenChildren.add(child.code))
      }
    }
  })
  // 按原顺序展示
  checkedList.forEach(item => {
    if (item.status !== 'checked') return
    if (skipCondition(item)) return
    // 如果是被隐藏的子项，跳过
    if (item.parent && hiddenChildren.has(item.code)) return
    if (maxLevel && parseInt(item.level) > maxLevel) {
      // 孤儿子项，检查其父级状态
      if (showOrphanChildren) {
        const parentItem = itemMap[item.parent]
        if (!parentItem || parentItem.status !== 'checked') {
          if (!selectedNames.includes(item.name)) {
            selectedNames.push(item.name)
          }
        }
      }
      return
    }
    if (!selectedNames.includes(item.name)) {
      selectedNames.push(item.name)
    }
  })
  return selectedNames.join('/')
}
function getAreaContent(checkedList, mark) {
  return getContent(checkedList, {
    parentCondition: (item) => !item.parent
  })
}
function getTradeContent(checkedList, mark) {
  return getContent(checkedList, {
    parentCondition: (item) => item.level === '1',
    skipCondition: (item) => item.level === '1' && item.status === 'semi-checked',
    maxLevel: 2,
    showOrphanChildren: true
  })
}
</script>

<style scoped lang="scss">
.top-search {
  position: relative;
  .ent-list {
    position: absolute;
    top: 100%;
    width: 542px;
    max-height: 500px;
    overflow-y: scroll;
    background: #fff;
    border-radius: 8px;
    z-index: 1000;
    box-shadow: 0 0 2.13333vw 0 rgba(32, 38, 58, 0.2);
    padding: 24px;
    .ent-name {
      border-bottom: 1px solid #EEEEEE;
      padding: 24px 0;
      font-size: 28px;
      color: rgb(82 86 101);
    }

    .loading {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    .loading .size {
      width: 80px;
      height: 80px;
    }

    /* loading 样式 */
    .hloading {
      margin: 0 5px;
      width: 20px;
      height: 20px;
      display: inline-block;
      vertical-align: middle;
      -webkit-animation: spin 1s steps(12, end) infinite;
      animation: spin 1s steps(12, end) infinite;
      background: transparent
      url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=)
      no-repeat;
      background-size: 100%;
    }

    .loading .text {
      padding-top: 20px;
      font-size: 28px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: 400;
      color: #9b9eac;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.content-item-pop {
  position: relative;
  display: flex;
  justify-content: space-between;
  height: 92px;
  align-items: center;
  background: #fff;
  padding: 24px;
  border-bottom: 1px solid #EEEEEE;
}

.content-item-pop .ipt {
  position: relative;
  flex: 1;
  margin-left: 28px;
}

.content-item-pop .ipt > input {
  font-size: 28px;
  font-family: 'PingFang SC-Regular', 'PingFang SC';
  font-weight: 400;
  color: #3e7bfa;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  border: none;
}

.content-item-pop-r {
  display: inline-flex;
  align-items: center;
  flex: 1;
  justify-content: space-between;
}

$font-size-small: 28px;
$font-family: 'PingFang SC', 'PingFang SC-Semibold';
$font-weight-regular: 400;
$gray-medium: #7F7F7F;
$red: #3e7bfa;
.content-item-pop-r span {
  font-size: $font-size-small;
  font-family: $font-family;
  font-weight: $font-weight-regular;
  color: $gray-medium !important;
  flex: 1;
  margin-left: 28px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

.content-item-pop-r span.has {
  color: $red !important;
}

.content-item-pop-r img {
  width: 24px;
  height: 24px;
  margin-left: 10px;
}

.list-content {
  background: #fff;
  padding: 24px;
  border-bottom: 1px solid #EEEEEE;
  .title {
    font-size: 28px;
    color: #404040;
  }
  .list-item-wrap {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 20px;
    .item {
      padding: 12px 24px;
      font-size: 24px;
      color: #404040;
      background: #f7f7f7;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      &:hover {
        opacity: 0.8;
      }
      &.active {
        background: linear-gradient( 90deg, #4AB8EC 0%, #07A6F0 100%);
        color: #fff;
      }
    }
  }
}
</style>
